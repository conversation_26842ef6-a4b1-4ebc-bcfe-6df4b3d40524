请根据下面的 checklist 来 review PR 中的代码，通过的标记✅，不通过的标记❌

## Common

- [ ] 禁止`magic number`
- [ ] 不要轻易引入第三方依赖，确认第三方依赖是经过团队讨论的
- [ ] 避免嵌套过深的`effect hooks`
- [ ] 避免冗余的 hooks 设计
- [ ] 避免嵌套过深的条件分支语句
- [ ] 避免嵌套的类型声明类似这样：`A | { name: string, func: ({ some: number }) => void }`
- [ ] 代码行数，避免`fat component` or `fat function`
- [ ] 1 个`PR`不允许`20`个文件的修改，不允许`300`行以上的代码行（含注释）
- [ ] 需要包含关键模块的测试代码

## 实践

- [ ] 禁止使用`useCallback` 来返回`JSX`片段
- [ ] 禁止将 ref 传递到子组件由子组件来修改 ref 的值
- [ ] 避免在组件内部再定义组件，可以将其拆分开来
- [ ] 确认 context 的正确使用：避免过度的重复渲染
- [ ] 确认 effect 的依赖是否关联了其它内部函数（甚至是嵌套函数）内部对于 state 的访问
- [ ] 确认`??` 以及函数默认参数的正确使用: 因为这两个对于 null 会失效
- [ ] 确认`every`的正确使用: `every` on an empty Array is `true`
- [ ] 确认非 `Shopify` 平台的兼容性(Big-commerce & WooCommerce)

## 设计

- [ ] 组件分层是否合理
- [ ] 组件 props 接口是否合理

## 性能

- [ ] 是否有清理事件监听器
- [ ] 是否有不合适的 hooks 的依赖

## 易用性

- [ ] 是否有合适的错误处理机制：显示合适的错误信息；给出解决方案；错误上报；

## 安全

- [ ] 确认代码中没有存储明文的账密信息(包括 token, api key 等)
- [ ] 确认生产环境不能有`source map`信息
