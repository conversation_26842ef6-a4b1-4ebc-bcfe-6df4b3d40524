name: Meerkat release check
run-name: Diff checking

on:
  pull_request:
    branches: ['master']
  merge_group:
    types: [checks_requested]
  schedule:
    - cron: '30 10 * * 4' # 北京时间每周四晚 18:30 点执行

permissions:
  contents: read
  actions: write

jobs:
  build:
    runs-on: ubuntu-latest
    env:
      cache-key: i18n-diff
      cache-file: cache.txt
    strategy:
      matrix:
        node-version: ['20.x']
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
      - name: Use Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node-version }}
      - name: Check differences between "${{ github.head_ref }}" branch and "master" branch.
        run: node ./.github/workflows/meerkat-release-check.js
        env:
          MEERKAT_PROJECT_ID: returns-center-returns-page
          GITHUB_HEAD_REF: ${{ github.head_ref }}
          CACHE_FILE: ${{ env.cache-file }}
          GITHUB_EVENT_TYPE: ${{ github.event_name }}
          SLACK_BOT_ACTION: replay
          SLACK_CHANNEL_ID: GPZ43J6EA # proj-rtc
          SLACK_MEMBER_ID: A065W1SHTEF # 同步Staging环境验收结果
          SLACK_INDEX_MESSAGE: 请QA同学同步需求验收情况
