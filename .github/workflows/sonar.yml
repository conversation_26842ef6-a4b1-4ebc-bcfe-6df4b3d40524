name: CI Check

on:
  push:
    branches: [ master, testing, release/* ] # 需要执行当前 action 的分支
  pull_request:
    types: [ opened, synchronize, reopened, labeled, unlabeled ]
    branches: [ master, testing, release/* ] # 当 pull request 被创建的时候需要执行当前 action 的分支

jobs:
  # Job：用于检查 PR 修改的文件数量
  check-pr-files:
    name: Check Changed Files Count
    # 条件：仅当事件是提交到 release/* 分支的 pull request 时运行
    if: github.event_name == 'pull_request' && startsWith(github.base_ref, 'release/')
    runs-on: ubuntu-latest
    outputs:
      changed_files_count: ${{ steps.changed_files.outputs.all_changed_files_count }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0 # 获取完整提交历史以计算差异

      - name: Get changed files
        id: changed_files
        uses: tj-actions/changed-files@v44

      - name: Validate file count
        # 条件：当文件数 > Secrets and variables -> Actions -> Variables -> FILE_COUNT_LIMIT 且 PR 不包含 'bypass-file-check' 标签时，此步骤会执行
        if: >
          fromJSON(steps.changed_files.outputs.all_changed_files_count) > fromJSON(vars.FILE_COUNT_LIMIT) &&
          !contains(github.event.pull_request.labels.*.name, 'bypass-file-check')
        run: |
          echo "Error: This PR exceeds the ${{ vars.FILE_COUNT_LIMIT }} changed files limit for release branches."
          echo "Found ${{ steps.changed_files.outputs.all_changed_files_count }} changed files."
          echo "To bypass this check, please add the 'bypass-file-check' label to this PR and re-run the job."
          exit 1 # 命令执行失败，导致整个 Job 失败

  build:
    # 依赖于 check-pr-files Job，但允许在跳过时也执行
    needs: check-pr-files
    # 条件：当 check-pr-files 成功或被跳过时执行，但失败时不执行
    if: always() && (needs.check-pr-files.result == 'success' || needs.check-pr-files.result == 'skipped')
    runs-on: ubuntu-latest # 执行 action 的镜像（默认）

    strategy:
      matrix:
        node-version: [ 20.15.0 ] # 执行 action 的 Node.js 版本（建议使用 LTS 版本或根据当前项目需要配置各自的版本）

    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0 # 必须！获取完整提交历史以计算差异

      - name: Use Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node-version }}

      - name: Install pnpm
        uses: pnpm/action-setup@v3
        with:
          version: 9

      - name: Use NPM Token
        uses: dkershner6/use-npm-token-action@v1
        with:
          token: '${{ secrets.NPM_TOKEN }}'
          workspace: ./

      - name: pnpm install
        run: pnpm install --no-optional --frozen-lockfile > /dev/null

      # 先构建类型文件，再进行类型检查
      - name: Build type definitions
        run: |
          # 构建核心包的类型文件
          pnpm run --filter @aftership/returns-logics-core build:ts

      - name: Lint and test
        run: pnpm tsc --noEmit --incremental --project ./apps/web/tsconfig.json

      - name: SonarQube Scan On PR
        if: ${{ github.event_name == 'pull_request' }}
        uses: sonarsource/sonarqube-scan-action@v5.0.0
        with:
          projectBaseDir: .
          # 额外忽略了 validation.ts, 因为 sonar 会重复报错
          # https://aftership.slack.com/archives/C02E66S38M6/p1738824867203569
          args: >
            -Dsonar.javascript.lcov.reportPaths=coverage/lcov.info
            -Dsonar.exclusions=**/__tests__/**,**/*.test.ts,node_modules/**,dist/**,scripts/**,test/**,tests/**,build/**,config/**,apps/web/config/endPoints.ts,packages/returns-logics-core/src/config.ts,packages/returns-logics-core/src/core/utils/validation.ts,packages/design-system/utils/uuid.ts,apps/web/features/returns/utils/common.ts,apps/web/components/RegexpRichText/RegexpRichText.tsx,apps/web/features/returns/hooks/useSyncXStateAndRoute.ts,packages/recipe-with-tokens/**
            -Dsonar.projectKey=shopper-refactor.returnscenter.com
            -Dsonar.pullrequest.provider=github
            -Dsonar.pullrequest.github.repository=${{ github.repository }}
            -Dsonar.pullrequest.key=${{ github.event.number }}
            -Dsonar.pullrequest.branch=${{ github.head_ref }}
            -Dsonar.pullrequest.base=${{ github.base_ref }}
            -Dsonar.qualitygate.wait=true
        env:
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
          SONAR_HOST_URL: ${{ secrets.SONAR_HOST_URL }}

      - name: Get branch name (merge)
        if: ${{ github.event_name != 'pull_request' }}
        shell: bash
        run: echo "BRANCH_NAME=$(echo ${GITHUB_REF#refs/heads/} | tr / -)" >> $GITHUB_ENV

      - name: SonarQube Scan On Push
        if: ${{ github.event_name != 'pull_request' }}
        uses: sonarsource/sonarqube-scan-action@v5.0.0
        with:
          projectBaseDir: .
          args: >
            -Dsonar.javascript.lcov.reportPaths=coverage/lcov.info
            -Dsonar.exclusions=**/__tests__/**,**/*.test.ts,node_modules/**,dist/**,scripts/**,test/**,tests/**,build/**,config/**,apps/web/config/endPoints.ts,packages/returns-logics-core/src/config.ts,packages/returns-logics-core/src/core/utils/validation.ts,packages/design-system/utils/uuid.ts,apps/web/features/returns/utils/common.ts,apps/web/components/RegexpRichText/RegexpRichText.tsx,apps/web/features/returns/hooks/useSyncXStateAndRoute.ts,packages/recipe-with-tokens/**
            -Dsonar.projectKey=shopper-refactor.returnscenter.com
            -Dsonar.pullrequest.provider=github
            -Dsonar.pullrequest.github.repository=${{ github.repository }}
            -Dsonar.branch.name=${{ env.BRANCH_NAME }}
            -Dsonar.qualitygate.wait=true
        env:
          SONAR_TOKEN: ${{ secrets.SONAR_2_TOKEN }}
          SONAR_HOST_URL: ${{ secrets.SONAR_2_HOST_URL }}
