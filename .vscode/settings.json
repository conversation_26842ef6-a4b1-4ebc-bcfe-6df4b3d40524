{"prettier.prettierPath": "./node_modules/prettier/index.cjs", "editor.formatOnSave": true, "[javascript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[javascriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[typescript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[typescriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[html]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[json]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[scss]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[svg]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[yaml]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "prettier.configPath": ".prettierrc.json", "typescript.tsdk": "node_modules/typescript/lib"}