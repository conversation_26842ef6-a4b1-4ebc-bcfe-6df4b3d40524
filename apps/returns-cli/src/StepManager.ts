import { type FlowEngineType, StepInstance } from '@aftership/returns-logics-core';

type QueuedStep =
  | {
      type: 'input';
      step: StepInstance;
    }
  | {
      type: 'request';
      step: StepInstance;
      promise: Promise<any>;
      resolve: (value: any) => void;
    }
  | {
      type: 'display';
      step: StepInstance;
    };
export class StepManager {
  #engine: FlowEngineType;
  #flowQueues: Map<
    string,
    {
      steps: Map<string, QueuedStep>;
      queue: string[];
      getCurrentStep(): QueuedStep | undefined;
    }
  > = new Map();

  #currentFlowId: string;

  constructor(flowEngine: FlowEngineType) {
    this.#engine = flowEngine;
    this.#currentFlowId = this.#engine.mainFlow.value.id;
    this.#subscribeToFlowCurrentStep(this.#currentFlowId);
  }

  #subscribeToFlowCurrentStep = (flowId: string) => {
    this.#flowQueues.set(flowId, {
      steps: new Map(),
      queue: [],
      getCurrentStep() {
        const step = this.steps.get(this.queue[0]);

        if (!step) return;

        if (['finished', 'valid'].includes(step.step.status)) {
          this.queue.shift();
          return this.getCurrentStep();
        }

        return step;
      },
    });

    const flowRecord = this.#flowQueues.get(flowId)!;

    return this.#engine
      .getFlow(flowId)!
      .value.getCurrentStep()
      .subscribe((step) => {
        // Record the step if it doesn't exist
        if (!flowRecord.steps.has(step.id)) {
          switch (step.config?.type) {
            case 'input':
              flowRecord.steps.set(step.id, {
                step,
                type: 'input',
              });
              break;
            case 'request': {
              let resolve: (value: any) => void = () => {};
              const promise = new Promise<any>((res) => {
                resolve = res;
              });
              flowRecord.steps.set(step.id, {
                step,
                type: 'request',
                promise,
                resolve,
              });
              break;
            }
            case 'display':
              flowRecord.steps.set(step.id, {
                step,
                type: 'display',
              });
              break;
          }

          flowRecord.queue.push(step.id);
        }

        const stepRecord = flowRecord.steps.get(step.id)!;
        stepRecord.step = step;

        if (stepRecord.type === 'request' && step.status === 'finished') {
          stepRecord.resolve(step.output);
        }
      });
  };

  getCurrentStep = () => {
    return this.#flowQueues.get(this.#currentFlowId)!.getCurrentStep();
  };
}
