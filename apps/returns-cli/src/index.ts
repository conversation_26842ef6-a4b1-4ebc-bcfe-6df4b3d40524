import chalk from 'chalk';
import prompts from 'prompts';

import { runReturnsLogics } from '@aftership/returns-logics-core';

import { StepManager } from './StepManager';
import { request } from './request';
import { waitForPromise } from './waitForPromise';

export const flowEngine = runReturnsLogics({
  sendRequest: request,
});

const main = async () => {
  waitForPromise(flowEngine.result);

  console.log(chalk.bgBlue.black('Welcome to the Returns CLI!'));

  const stepManager = new StepManager(flowEngine);

  while (stepManager.getCurrentStep()) {
    const step = stepManager.getCurrentStep()!;

    console.log('step ->', step);

    switch (step.type) {
      case 'request':
        console.log(`waiting for request: ${step.step.name} ...`);
        await step.promise;
        break;
      case 'display': {
        if (step.step.config?.type !== 'display') break;

        const nextStep = await prompts({
          name: 'nextStep',
          type: 'select',
          message: 'What would you like to do next?',
          choices: [],
          // step.step.config.choices.map((choice) => ({
          //   title: choice.title,
          //   value: choice.nextStep,
          // })),
        });

        step.step.trigger(nextStep.nextStep);
        break;
      }
      case 'input':
        {
          if (step.step.config?.type !== 'input') break;

          const answer = await prompts(
            Object.entries(step.step.config.schema.shape).map(([fieldName, fieldSchema]) => ({
              name: fieldName,
              type: 'text',
              message: `Please input ${fieldName}`,
              validate: (input) => {
                const res = fieldSchema.safeParse(input);

                if (res.success) return true;

                return res.error.format()._errors[0];
              },
            })),
          );

          step.step.trigger(answer as any);
        }
        break;
    }
  }
};

main();
