export interface Pagination {
  page: number;
  limit: number;
  total: number;
  has_next_page: boolean;
}

export interface ErrorBody {
  code: number;
  details: ErrorDetail[];
  message: string;
  retryable: boolean;
}

export interface ResponseMetaError {
  path: string | null;
  info: ErrorBody;
}
export interface MetaErrorItem {
  code: number;
  message: string;
}

interface ResponseMeta<Type extends string = string> {
  code: number;
  type: Type;
  errors?: Type extends 'OK'
    ? undefined
    : (ResponseMetaError | MetaErrorItem)[];
  message: string;
}

interface PaginatedData {
  pagination: Pagination;
}
type ResponseData<
  T,
  Key extends string = '',
  Paginated extends boolean = false,
> = Key extends ''
  ? T
  : Paginated extends false
    ? { [key in Key]: T }
    : { [key in Key]: T } & PaginatedData;

interface BaseResponse<
  T,
  Key extends string = '',
  Paginated extends boolean = false,
> {
  meta: ResponseMeta;
  data?: ResponseData<T, Key, Paginated>;
}
interface SuccessResponse<
  T = unknown,
  Key extends string = '',
  Paginated extends boolean = false,
> extends BaseResponse<T, Key, Paginated> {
  meta: ResponseMeta<'OK'>;
  data: ResponseData<T, Key, Paginated>;
}
interface FailureResponse<
  T = unknown,
  Key extends string = '',
  Paginated extends boolean = false,
> extends BaseResponse<T, Key, Paginated> {
  meta: ResponseMeta;
  data?: undefined;
}
type ResponseBody<
  T = unknown,
  Key extends string = '',
  Paginated extends boolean = false,
> = SuccessResponse<T, Key, Paginated> | FailureResponse<T, Key, Paginated>;

interface IDOnly {
  id: string;
}

export interface OpenShipmentBody {
  count: number;
  courier_account_id: string;
  ship_from_location_id: string;
}

interface ErrorDetail {
  path: string;
  info: string;
}

export type OpenShipmentsResponse = ResponseBody<OpenShipmentBody[], 'summary'>;
