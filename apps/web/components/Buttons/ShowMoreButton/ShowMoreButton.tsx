import { useTranslation } from 'react-i18next';

import { Button, Icon, Stack, Typography } from '@aftership/astra';
import { ArrowDropDownFilled, ArrowDropUpFilled } from '@aftership/astra-icons';

interface IProps {
  isExpand?: boolean;
  onPress?: VoidFunction;
}

const ShowMoreButton = ({ isExpand, onPress }: IProps) => {
  const { t } = useTranslation();
  return (
    <Button onPress={onPress} variant='plain'>
      <Stack direction={'row'} align={'center'} gap={'s'}>
        <Typography variant='bodyMdSemibold'>
          {isExpand ? t('page.action.showLess') : t('page.action.showAll')}
        </Typography>
        <Icon source={isExpand ? ArrowDropUpFilled : ArrowDropDownFilled} />
      </Stack>
    </Button>
  );
};

export default ShowMoreButton;
