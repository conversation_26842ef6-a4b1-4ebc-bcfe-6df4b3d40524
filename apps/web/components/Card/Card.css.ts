import { recipe } from '@vanilla-extract/recipes';

import { tokenVars } from '@aftership/astra-tokens/Contract.css';

const { Space, Color } = tokenVars.Semantic;

export const cardRecipe = recipe({
  base: {
    backgroundColor: 'white',
    padding: Space.L,
    borderColor: Color.Border.Primary,
  },
});

export const cardSectionRecipe = recipe({
  base: [
    {
      width: '100%',
      selectors: {
        '&:not(:last-child):after': {
          content: '""',
          display: 'block',
          width: '100%',
          height: '1px',
          backgroundColor: Color.Border.Divider,
          marginBlock: Space.L,
        },
      },
    },
  ],
});

export const cardHeaderRecipe = recipe({
  base: {
    marginBottom: Space.L,
  },
});
