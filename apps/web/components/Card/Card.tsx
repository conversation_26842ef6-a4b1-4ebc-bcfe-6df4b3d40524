import { Card as AstraCard, CardProps as AstraCardProps, clsx } from '@aftership/astra';

import { cardRecipe } from './Card.css';

export interface CardProps extends AstraCardProps {}

const Card = ({ radiusSize = 'small', ...props }: CardProps) => {
  return (
    <AstraCard
      isInteractive={false}
      radiusSize={radiusSize}
      {...props}
      className={clsx(props.className, cardRecipe())}
    />
  );
};

export default Card;
