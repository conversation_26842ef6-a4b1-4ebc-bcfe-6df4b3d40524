import clsx from 'clsx';
import { PropsWithChildren } from 'react';

import { cardSectionRecipe } from './Card.css';

export interface CardSectionProps extends PropsWithChildren {
  className?: string;
}

const CardSection = ({ className, children }: PropsWithChildren<CardSectionProps>) => {
  return <div className={clsx(cardSectionRecipe(), className)}>{children}</div>;
};

export default CardSection;
