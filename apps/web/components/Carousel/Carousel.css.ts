import { style } from '@vanilla-extract/css';
import { recipe } from '@vanilla-extract/recipes';

import { semanticTokenVar } from '@aftership/astra-tokens/semantic-token/Contract.css';

const { Radius, Color: SemanticColor } = semanticTokenVar;

export const carouselRecipe = recipe({
  base: [
    {
      border: 'none',
      outlineStyle: 'none',
      margin: 0,
      display: 'flex',
      flexDirection: 'column',
      overflow: 'hidden',
      position: 'relative',
    },
  ],
});

export const carouselWrapperRecipe = recipe({
  base: [
    {
      display: 'flex',
      position: 'relative',
      overflow: 'hidden',
      width: '100%',
      borderRadius: Radius.Xs,
      flex: '1',
    },
  ],
});

export const carouselItemBoxRecipe = recipe({
  base: [
    {
      display: 'flex',
      position: 'relative',
      width: '100%',
      minHeight: '200px',
    },
  ],
  variants: {
    animated: {
      true: {
        transition: 'transform .3s cubic-bezier(0.78, 0.14, 0.15, 0.86)',
      },
      false: {
        transition: 'none',
      },
    },
  },
  defaultVariants: {
    animated: true,
  },
});

export const carouselItemStyle = style({ flex: '0 0 100%' });

export const carouselIndicatorButtonRecipe = recipe({
  base: [
    {
      outlineStyle: 'none',
      background: 'none',
      border: 'none',
      padding: 0,
      marginInline: '6px',
      width: '6px',
      height: '6px',
      borderRadius: '50%',
    },
  ],
  variants: {
    active: {
      true: {
        backgroundColor: SemanticColor.Brand.Primary,
      },
      false: {
        backgroundColor: SemanticColor.Fill.Disabled_Light,
      },
    },
  },
});

export const carouselActionButtonRecipe = recipe({
  base: [
    {
      position: 'absolute',
      outlineStyle: 'none',
      background: 'none',
      border: 'none',
      insetBlockEnd: '50%;',
      transform: 'translateY(50%)',
    },
  ],
});

export const carouselIndicatorRecipe = recipe({
  base: [
    {
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'flex-end',
      minHeight: '22px',
    },
  ],
});
