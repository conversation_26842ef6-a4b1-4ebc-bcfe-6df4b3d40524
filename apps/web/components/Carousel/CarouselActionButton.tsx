import { useCallback } from 'react';

import { UnstyledButton } from '@aftership/astra';

import { carouselActionButtonRecipe } from './Carousel.css';
import { useTimeout } from './hooks/useTimeout';
import { CarouselElementProps } from './type';

const CarouselActionButton = ({
  nextSilde,
  prevSilde,
  duration,
}: Pick<
  CarouselElementProps,
  'nextSilde' | 'prevSilde' | 'animated' | 'setAnimated' | 'duration'
>) => {
  const { isWaiting, start } = useTimeout({ delay: duration });
  const handlePress = useCallback(
    (direction: 'prev' | 'next') => {
      if (isWaiting) return;
      start();
      direction === 'prev' ? prevSilde() : nextSilde();
    },
    [isWaiting, start, prevSilde, nextSilde],
  );
  return (
    <>
      {/* {TODO} UI */}
      <UnstyledButton className={carouselActionButtonRecipe()} onPress={() => handlePress('prev')}>
        Prev
      </UnstyledButton>
      <UnstyledButton
        className={carouselActionButtonRecipe()}
        style={{ insetInlineEnd: 0 }}
        onPress={() => handlePress('next')}
      >
        Next
      </UnstyledButton>
    </>
  );
};

export default CarouselActionButton;
