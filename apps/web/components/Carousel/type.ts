import { CSSProperties } from 'react';

export type CarouselHookProps = {
  /**
   * Whether autoPlay
   * @default true
   */
  autoPlay?: boolean;
  /**
   * @default 'right'
   */
  playDirection?: 'right' | 'left';
  /**
   * Whether or not to display the left/right slide switch button
   * @default false
   */
  actionArrows?: boolean;
  /**
   * auto playback interval, How many milliseconds before automatically switching to the next
   * minimum 500
   * @default 3000
   */
  playInterval?: number;
  /**
   * transition time ms
   * Transition time of the toggle animation in milliseconds
   * minimum 100
   * @default 300
   */
  transitionDuration?: number;
  currentIndex: number;
  /**
   * silde total >= 1
   */
  total: number;
  /**
   * will be triggered at nextSilde and prevSilde.
   */
  stepAction?: () => void;
};

export type CarouselArgsProps = { active: boolean };

export type CarouselProps<T = any> = {
  className?: string;
  items: T[];
  renderItem: (item: T, args?: CarouselArgsProps) => React.ReactNode;
  showCarouseIndicator?: boolean;
  style?: CSSProperties;
} & Omit<CarouselHookProps, 'total' | 'stepAction' | 'currentIndex'>;

export interface CarouselElementProps extends CarouselProps {
  animated: boolean;
  duration: number;
  index: number;
  nextSilde: () => void;
  prevSilde: () => void;
  moveSildeTo: (index: number) => void;
  setAnimated: (animated: boolean) => void;
}
