import { globalStyle, style } from '@vanilla-extract/css';

import { tokenVars } from '@aftership/astra-tokens/Contract.css';
import {
  bodyLgDefaultText,
  bodyMdDefaultTextClassName,
  bodySmDefaultTextClassName,
  heading2XsTextClassName,
} from '@aftership/astra-tokens/texts.css';

import { datePickerThemeVars } from './DatePickerTokens.css';

const { Space, Color } = tokenVars.Semantic;

export const datePickerWrapper = style({
  alignItems: 'center',
  justifyContent: 'space-around',
  display: 'flex',
  gap: datePickerThemeVars.Spacing.Date_Picker.Gap,
});

export const dateItemStyle = style({
  backgroundColor: datePickerThemeVars.Color.Date_Item.BackgroundColor,
  borderRadius: datePickerThemeVars.Radius.Date_Item.BorderRadius,
  width: datePickerThemeVars.ItemSizing.Date_Item.Width,
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'center',
  cursor: 'pointer',
  flexShrink: 0,
  paddingBlock: datePickerThemeVars.Spacing.Date_Item.PaddingBlock,
});

export const selectedDateItemStyle = style({
  backgroundColor: datePickerThemeVars.Color.Date_Item.Selected_Background,
});
export const dateItemDisabledStyle = style({
  pointerEvents: 'none',
});

globalStyle(`.${dateItemStyle}:hover:not(${selectedDateItemStyle})`, {
  backgroundColor: datePickerThemeVars.Color.Date_Item.Hover_Background,
  borderRadius: datePickerThemeVars.Radius.Date_Item.BorderRadius,
});
// --- weekDayStyle start ---
const weekDayStyle = style({
  marginBottom: datePickerThemeVars.Spacing.WeekDay.MarginBottom,
  color: datePickerThemeVars.Color.WeekDay.Color,
});

export const weekDayClassname = style([weekDayStyle, bodyMdDefaultTextClassName]);
export const weekDaySelectedClassname = style({
  color: datePickerThemeVars.Color.WeekDay.Selected_Color,
});
export const weekDayDisabledClassname = style({
  color: datePickerThemeVars.Color.WeekDay.Disabled_Color,
});

globalStyle(`.${dateItemStyle}:hover .${weekDayStyle}:not(${weekDaySelectedClassname})`, {
  color: datePickerThemeVars.Color.WeekDay.Color,
});

// --- weekDayStyle end ---

// --- dayOfMonthStyle start ---
const dayOfMonthStyle = style({
  color: datePickerThemeVars.Color.DayOfMonth.Color,
});

export const dayOfMonthClassname = style([dayOfMonthStyle, heading2XsTextClassName]);
export const dayOfMonthSelectedClassname = style({
  color: datePickerThemeVars.Color.DayOfMonth.Selected_Color,
});
export const dayOfMonthDisabledClassname = style({
  color: datePickerThemeVars.Color.DayOfMonth.Disabled_Color,
});

globalStyle(`.${dateItemStyle}:hover .${dayOfMonthStyle}:not(${dayOfMonthSelectedClassname})`, {
  color: datePickerThemeVars.Color.DayOfMonth.Color,
});

// --- dayOfMonthStyle end ---

// --- dayOfMonthStyle start ---
const monthOfYearStyle = style({
  color: datePickerThemeVars.Color.MonthOfYear.Color,
});

export const monthOfYearClassname = style([monthOfYearStyle, bodySmDefaultTextClassName]);
export const monthOfYearSelectedClassname = style({
  color: datePickerThemeVars.Color.MonthOfYear.Selected_Color,
});
export const monthOfYearDisabledClassname = style({
  color: datePickerThemeVars.Color.MonthOfYear.Disabled_Color,
});

globalStyle(`.${dateItemStyle}:hover .${monthOfYearStyle}:not(${monthOfYearSelectedClassname})`, {
  color: datePickerThemeVars.Color.MonthOfYear.Color,
});
// --- dayOfMonthStyle end ---

export const chevronClass = style([
  {
    outline: 'none',
    background: 'none',
    border: 'none',
    boxSizing: 'border-box',
  },
]);

export const scrollableContainer = style({
  overflowX: 'auto',
});
globalStyle(`${scrollableContainer}::-webkit-scrollbar`, {
  display: 'none',
  msOverflowStyle: 'none',
  scrollbarWidth: 'none',
});

export const chevronTooltip = style({});

globalStyle(`.${chevronTooltip}:hover::after`, {
  ...bodyLgDefaultText,
  content: 'attr(data-tooltipContent)',
  position: 'absolute',
  padding: Space.Xs,
  color: Color.Text.Primary,
  backgroundColor: Color.Bg.Body,
  width: '230px',
  zIndex: 10000,
  boxShadow: '0px 0px 2px 0px rgba(0, 0, 0, 0.20), 0px 2px 10px 0px rgba(0, 0, 0, 0.10);',
});
