import { tokenVars } from '@aftership/astra-tokens/Contract.css';

const { Color } = tokenVars.Primitive;

/**
 * Q1: 什么时候用 itemSizing？
 * Q2: token 命名规范，类似于 Default_BackgroundColor 这种，可以随便命名吗，看起来可以？只是用来组合成 css？
 * Q3: Button.css.ts 作用是啥
 */

export const datePickerThemeVars = {
  Color: {
    Date_Item: {
      // TODO 色值确认
      BackgroundColor: tokenVars.Semantic.Color.Bg.Body,
      //TODO HOVER 状态下的背景色
      Hover_Background: Color.Gray[300],
      Selected_Background: tokenVars.Semantic.Color.Brand.Primary,
    },
    WeekDay: {
      Color: tokenVars.Semantic.Color.Text.Primary,
      Selected_Color: tokenVars.Semantic.Color.Text.White_Fixed,
      Disabled_Color: tokenVars.Semantic.Color.Text.Disabled,
    },
    DayOfMonth: {
      Color: tokenVars.Semantic.Color.Text.Primary,
      Selected_Color: tokenVars.Semantic.Color.Text.White_Fixed,
      Disabled_Color: tokenVars.Semantic.Color.Text.Disabled,
    },
    MonthOfYear: {
      Color: tokenVars.Semantic.Color.Text.Tertiary,
      Selected_Color: tokenVars.Semantic.Color.Text.White_Fixed,
      Disabled_Color: tokenVars.Semantic.Color.Text.Disabled,
    },
  },
  Radius: {
    Date_Item: {
      BorderRadius: tokenVars.Semantic.Radius.S,
    },
  },
  Spacing: {
    Date_Picker: {
      Gap: tokenVars.Semantic.Space['2Xs'],
    },
    Date_Item: {
      // TODO 确认间距，以及命名
      PaddingBlock: tokenVars.Semantic.Space.S,
    },
    WeekDay: {
      MarginBottom: tokenVars.Semantic.Space.Xs,
    },
  },

  ItemSizing: {
    Date_Item: {
      Width: 60,
    },
  },
};
