import { Typography } from '@aftership/astra';

import { Modal } from '../Modal';

export interface DeleteModalProps {
  title?: string;
  info?: string;
  cancelText?: string;
  confirmText?: string;
  isOpen?: boolean;
  onClose?: VoidFunction;
  onConfirm?: VoidFunction;
}

const DeleteModal = ({
  isOpen = false,
  onClose,
  onConfirm,
  title,
  info,
  cancelText,
  confirmText,
}: DeleteModalProps) => {
  return (
    <Modal
      isOpen={isOpen}
      title={title}
      onClose={onClose}
      primaryAction={{
        content: confirmText || 'Confirm',
        onAction: onConfirm ? onConfirm : () => {},
      }}
      secondaryAction={{
        content: cancelText || 'Cancel',
        onAction: onClose ? onClose : () => {},
      }}
    >
      <Typography variant='bodyLg'>{info}</Typography>
    </Modal>
  );
};

export default DeleteModal;
