import React from 'react';

import { BackgroundColor, Box, DimensionValue } from '@aftership/astra';
import { tokenVars } from '@aftership/astra-tokens/Contract.css';

export interface Props {
  backgroundColor?: BackgroundColor;
  spacing?: DimensionValue;
  direction?: 'horizontal' | 'vertical';
}
const Divider = ({
  // TODO: 需要补充
  spacing = tokenVars.Semantic.Space.Xl,
  direction = 'vertical',
  backgroundColor = tokenVars.Semantic.Color.Border.Divider,
}: Props) => {
  const style =
    direction === 'vertical'
      ? {
          height: '1px',
          marginTop: spacing,
          marginBottom: spacing,
        }
      : {
          width: '1px',
          height: '13px',
        };
  return <Box {...style} backgroundColor={backgroundColor} />;
};

export default Divider;
