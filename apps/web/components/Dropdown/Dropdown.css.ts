import { recipe } from '@vanilla-extract/recipes';

import { tokenVars } from '@aftership/astra-tokens/Contract.css';
import { resetClassNames } from '@aftership/astra-tokens/reset.css';
import { bodyLgDefaultTextClassName, bodyLgSemiboldText } from '@aftership/astra-tokens/texts.css';

export const dropdownListBoxItemRecipe = recipe({
  base: [
    resetClassNames.noOutline,
    bodyLgDefaultTextClassName,
    {
      cursor: 'default',
      wordBreak: 'break-all',
      paddingInline: tokenVars.Semantic.Space.M,
      paddingBlock: tokenVars.Semantic.Space.Xs,
      width: '100%',
      minWidth: 160,
      selectors: {
        '&[data-hovered]': {
          background: tokenVars.Primitive.Color.Gray['200'],
        },
        '&[data-selected]': {
          ...bodyLgSemiboldText,
          background: tokenVars.Primitive.Color.Gray['400'],
        },
      },
    },
  ],
});

export const dropdownButtonRecipe = recipe({
  base: [resetClassNames.noOutline, resetClassNames.noBackground, resetClassNames.noBorder],
});

export const dropdownListBoxRecipe = recipe({
  base: [
    resetClassNames.noBorder,
    resetClassNames.noOutline,
    bodyLgDefaultTextClassName,
    {
      display: 'flex',
      flexDirection: 'column',
      paddingBlock: tokenVars.Semantic.Space.Xs,
      overflowY: 'auto',
      maxHeight: 380,
    },
  ],
});
