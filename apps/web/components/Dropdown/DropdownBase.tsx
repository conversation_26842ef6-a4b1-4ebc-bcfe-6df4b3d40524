import clsx from 'clsx';

import {
  OverlayTrigger,
  OverlayTriggerState,
  Popover,
  PopoverTriggerType,
  UnstyledButton,
} from '@aftership/astra';

import {
  dropdownButtonRecipe,
  dropdownListBoxItemRecipe,
  dropdownListBoxRecipe,
} from './Dropdown.css';
import { DropdownBaseProps, DropdownItemProps, DropdownKey } from './type';

import { ListBox, ListBoxItem } from '../ListBox';

const DropdownBase = <T extends React.ElementType = PopoverTriggerType>(
  props: DropdownBaseProps<T>,
) => {
  const {
    children,
    pressedClose = true,
    items,
    selectionMode,
    renderChildren,
    onAction,
    showArrow,
    trigger,
    placement,
    isOpen,
    offset,
    triggerProps,
    className,
    ...rest
  } = props;

  const popoverProps = {
    showArrow,
    trigger,
    placement,
    isOpen,
    offset,
    triggerProps,
    className,
  };

  const handleAction = (key: DropdownKey, state?: OverlayTriggerState) => {
    pressedClose && selectionMode !== 'multiple' && state?.close?.();
    onAction?.(key, state);
  };

  const contentRender = (
    <OverlayTrigger
      render={(state) => (
        <ListBox
          onAction={(key) => handleAction(key, state)}
          {...rest}
          selectionMode={selectionMode}
          className={clsx(dropdownListBoxRecipe())}
          items={items}
        >
          {(item: DropdownItemProps) => (
            <ListBoxItem
              className={clsx(dropdownListBoxItemRecipe())}
              textValue={item.name}
              id={item.id}
            >
              {item.render || item.name}
            </ListBoxItem>
          )}
        </ListBox>
      )}
    />
  );
  return (
    <Popover {...popoverProps} content={contentRender}>
      {renderChildren || (
        <UnstyledButton className={dropdownButtonRecipe()}>{children}</UnstyledButton>
      )}
    </Popover>
  );
};

export default DropdownBase;
