import { PopoverProps, RawListBoxItemRenderProps } from '@aftership/astra';

import { ListBoxProps } from '../ListBox';

export type DropdownKey = string | number;
export type SelectionMode = 'single' | 'multiple';

export interface DropdownItemProps {
  id: string | number;
  /**
   * item display name, will be used to search for matching
   */
  name?: string;
  /**
   * custom render
   */
  render?: (args: RawListBoxItemRenderProps) => React.ReactNode;
}

type ListBoxPropsType = ListBoxProps<DropdownItemProps>;

export type DropdownBaseProps<T extends React.ElementType = any> = React.PropsWithChildren<{
  /**
   * Whether or not to close the popup box when clicking on an item
   * in the selectionMode ! == 'multiple'
   * @default true
   */
  pressedClose?: boolean;
  /**
   * custom render
   * default Button is used as the trigger.
   */
  renderChildren?: React.ReactNode;
  /**
   * The type of selection that is allowed in the collection.
   * @default single
   */
  selectionMode?: SelectionMode;

  className?: string;

  onAction?: (key: string | number, state?: any) => void;
}> &
  Omit<ListBoxPropsType, 'onAction'> &
  Pick<
    PopoverProps<T>,
    'showArrow' | 'trigger' | 'placement' | 'isOpen' | 'offset' | 'triggerProps'
  >;

export type DropdownProps = Pick<
  DropdownBaseProps,
  | 'isOpen'
  | 'items'
  | 'onAction'
  | 'className'
  | 'children'
  | 'offset'
  | 'showArrow'
  | 'placement'
  | 'className'
>;
