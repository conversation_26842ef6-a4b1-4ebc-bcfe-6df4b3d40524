import { style } from '@vanilla-extract/css';

export const ellipsisClassName = style({
  overflow: 'hidden',
  textOverflow: 'ellipsis',
  display: '-webkit-box',
  WebkitBoxOrient: 'vertical',
  wordBreak: 'break-word',
});

export const maxLine1ClassName = style({
  WebkitLineClamp: 1,
});
export const maxLine2ClassName = style({
  WebkitLineClamp: 2,
});
export const maxLine3ClassName = style({
  WebkitLineClamp: 3,
});
