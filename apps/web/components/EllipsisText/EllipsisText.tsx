import React from 'react';

import { Typography, TypographyProps } from '@aftership/astra';

import {
  ellipsisClassName,
  maxLine1ClassName,
  maxLine2ClassName,
  maxLine3ClassName,
} from './EllipsisText.css';

interface EllipsisTextProps extends TypographyProps<'span'> {
  text: string;
  maxLine?: 1 | 2 | 3;
}

const EllipsisText = ({
  text,
  maxLine = 1,
  className,
  ...props
}: Omit<EllipsisTextProps, 'children'>) => {
  return (
    <Typography
      className={`${ellipsisClassName} ${maxLine === 1 && maxLine1ClassName} ${maxLine === 2 && maxLine2ClassName} ${maxLine === 3 && maxLine3ClassName} ${className}`}
      {...props}
    >
      {text}
    </Typography>
  );
};

export default EllipsisText;
