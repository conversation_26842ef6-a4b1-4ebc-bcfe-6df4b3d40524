import { style } from '@vanilla-extract/css';

export const titleText = style({
  fontSize: '24px',
  fontWeight: '800',
  lineHeight: '32px',
  color: '#000000',
  fontFamily: 'Lato',
});

export const messageText = style({
  fontSize: '12px',
  fontFamily: 'Lato',
});

export const redirectButton = style({
  padding: '8px 16px',
  borderRadius: '100px',
  border: '1px solid #00000024',
  backgroundColor: 'transparent',
  fontFamily: 'Lato',
  fontSize: '14px',
  color: '#232933',
  fontWeight: '700',
  selectors: {
    '&:hover': {
      borderColor: '#232933',
    },
  },
});
