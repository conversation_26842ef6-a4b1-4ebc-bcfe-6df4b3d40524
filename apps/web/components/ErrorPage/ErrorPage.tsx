import Image from 'next/image';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';

import { Box, Stack } from '@aftership/astra';
import { tokenVars } from '@aftership/astra-tokens/Contract.css';

import { messageText, redirectButton, titleText } from './ErrorPage.css';

export interface ErrorPageProps {
  title: string;
  message?: string;
  redirectUrl?: string;
  redirectUrlText?: string;
}

export default function ErrorPage({
  title,
  message,
  redirectUrl,
  redirectUrlText,
}: ErrorPageProps) {
  const { t } = useTranslation();
  // const timerId = useRef<NodeJS.Timeout | null>(null);
  const httpsRedirectUrl = useMemo(() => {
    if (redirectUrl) {
      return redirectUrl.includes('http') ? redirectUrl : `https://${redirectUrl}`;
    }
    return '';
  }, [redirectUrl]);

  // useEffect(() => {
  //   if (redirectUrl && process.env.NODE_ENV === 'production') {
  //     timerId.current = setTimeout(() => {
  //       location.href = httpsRedirectUrl;
  //     }, 5000);
  //   }
  //   return () => {
  //     timerId.current && clearTimeout(timerId.current);
  //     timerId.current = null;
  //   };
  // }, [httpsRedirectUrl, redirectUrl]);

  // 不要应用主题
  return (
    <Box backgroundColor='#F5F6F7'>
      <Stack direction='column' style={{ height: '100vh' }}>
        <Box backgroundColor={tokenVars.Semantic.Color.Bg.Body}>
          <Stack align='center' justify='center' style={{ height: 80 }}>
            <Image
              width={260}
              height={32}
              src={require('@/assets/returns-center-logo.svg').default?.src}
              alt='Returns center logo'
            />
          </Stack>
        </Box>

        <Stack flex={1} gap={'l'} direction='column' align='center' justify='center'>
          <Image
            width={360}
            height={229}
            src={require('@/assets/404.svg').default?.src}
            alt={title}
          />
          <p className={titleText}>{title}</p>

          {redirectUrl ? (
            <Stack justify='center'>
              <button className={redirectButton} onClick={() => (location.href = httpsRedirectUrl)}>
                {redirectUrlText || t('page.error.backToHomePage')}
              </button>
            </Stack>
          ) : (
            <p className={messageText}>{message}</p>
          )}
        </Stack>
      </Stack>
    </Box>
  );
}
