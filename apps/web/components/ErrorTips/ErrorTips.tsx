import { Icon, Stack, Typography } from '@aftership/astra';
import { AlertTriangleOutlined } from '@aftership/astra-icons';

export interface ErrorTipsProps {
  children: React.ReactNode;
}

const ErrorTips = ({ children }: ErrorTipsProps) => {
  return (
    <Stack align='center' gap='2xs'>
      <Icon source={AlertTriangleOutlined} size={16} color='error' />
      {typeof children === 'string' ? (
        <Typography variant='bodyMd' color='error'>
          {children}
        </Typography>
      ) : (
        children
      )}
    </Stack>
  );
};

export default ErrorTips;
