import { recipe } from '@vanilla-extract/recipes';

import { resetClassNames } from '@aftership/astra-tokens/reset.css';
import { bodySmDefaultTextClassName } from '@aftership/astra-tokens/texts.css';

import { fileUploadThemeVars } from './FileUploadToken.css';

export const fileUploadContainer = recipe({
  base: {
    display: 'flex',
  },
});

export const uploadItemRecipe = recipe({
  base: [
    resetClassNames.noOutline,
    {
      borderRadius: fileUploadThemeVars.Radius.UploadItem,
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      position: 'relative',
      textAlign: 'center',
    },
  ],
  variants: {
    size: {
      small: {
        width: fileUploadThemeVars.ItemSizing.UploadItem.Small.Width,
        height: fileUploadThemeVars.ItemSizing.UploadItem.Small.Height,
      },
      medium: {
        width: fileUploadThemeVars.ItemSizing.UploadItem.Medium.Width,
        height: fileUploadThemeVars.ItemSizing.UploadItem.Medium.Height,
      },
      large: {
        width: fileUploadThemeVars.ItemSizing.UploadItem.Large.Width,
        height: fileUploadThemeVars.ItemSizing.UploadItem.Large.Height,
      },
    },
  },
  defaultVariants: {
    size: 'small',
  },
});

export const uploadTriggerMessageRecipe = recipe({
  base: [
    bodySmDefaultTextClassName,
    {
      color: fileUploadThemeVars.Color.UploadTrigger.Message_FontColor,
      wordBreak: 'break-all',
    },
  ],
});

export const uploadTriggerRecipe = recipe({
  base: [
    {
      display: 'flex',
      flexDirection: 'column',
      backgroundColor: fileUploadThemeVars.Color.UploadTrigger.Background,
      borderStyle: 'dashed',
      borderWidth: fileUploadThemeVars.Border.UploadItem.Width,
      borderColor: fileUploadThemeVars.Color.UploadTrigger.BorderColor,
      color: fileUploadThemeVars.Color.UploadTrigger.Icon,
      gap: '4px',
      padding: 6,
      selectors: {
        '&[data-hovered]': {
          borderColor: fileUploadThemeVars.Color.UploadTrigger.Hovered_BorderColor,
        },
      },
    },
  ],
});

export const uploadRemoveIconRecipe = recipe({
  base: [
    {
      position: 'absolute',
      display: 'flex',
      height: 24,
      width: 24,
      justifyContent: 'center',
      alignItems: 'center',
      insetBlockStart: '0',
      insetInlineEnd: '0',
      borderRadius: '50%',
      color: fileUploadThemeVars.Color.UploadItem.RemoveIcon,
      backgroundColor: fileUploadThemeVars.Color.UploadItem.RemoveIconBg,
      marginBlockStart: fileUploadThemeVars.Spacing.RemoveIcon.MarginBlockStart,
      marginInlineEnd: fileUploadThemeVars.Spacing.RemoveIcon.MarginInlineEnd,
      selectors: {
        '&[data-hovered]': {
          backgroundColor: fileUploadThemeVars.Color.UploadItem.RemoveIcon_Hover,
        },
      },
    },
  ],
});

export const uploadPreviewImage = recipe({
  base: {
    width: '100%',
    height: '100%',
    objectFit: 'cover',
    borderRadius: fileUploadThemeVars.Radius.UploadItem,
    borderWidth: fileUploadThemeVars.Border.UploadItem.Width,
  },
});

export const previewContainerRecipe = recipe({
  base: [
    {
      display: 'flex',
      gap: fileUploadThemeVars.Spacing.PreviewContainer.Gap,
      flexWrap: 'wrap',
    },
  ],
  variants: {
    status: {
      error: {
        backgroundColor: fileUploadThemeVars.Color.PreviewContainer.Error.Background,
        color: fileUploadThemeVars.Color.UploadItem.Error_RemoveIcon,
      },
      done: {},
      uploading: {},
    },
  },
});

export const uploadingMaskRecipe = recipe({
  base: [
    {
      position: 'absolute',
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      insetBlockStart: 0,
      insetInlineStart: 0,
      width: '100%',
      height: '100%',
      backgroundColor: fileUploadThemeVars.Color.PreviewContainer.Uploading.Background,
      color: fileUploadThemeVars.Color.UploadItem.LoadingIcon,
    },
  ],
});
