import { primitiveTokenVar } from '@aftership/astra-tokens/primitive-token/Contract.css';
import { semanticTokenVar } from '@aftership/astra-tokens/semantic-token/Contract.css';

const { Color: PrimitiveColor, Stroke_Width } = primitiveTokenVar;
const { Radius, Color } = semanticTokenVar;

export const fileUploadThemeVars = {
  Color: {
    UploadItem: {
      RemoveIconBg: PrimitiveColor.Black_Alpha[600],
      RemoveIcon: Color.Bg.Body,
      RemoveIcon_Hover: PrimitiveColor.Black_Alpha[1200],
      Error_RemoveIcon: PrimitiveColor.Red[1100],
      LoadingIcon: Color.Bg.Body,
    },
    UploadTrigger: {
      Background: Color.Bg.Body,
      BorderColor: PrimitiveColor.Gray[700],
      Hovered_BorderColor: PrimitiveColor.Gray[1100],
      Icon: PrimitiveColor.Gray[1100],
      Message_FontColor: PrimitiveColor.Gray[1100],
    },
    PreviewContainer: {
      Error: {
        Background: PrimitiveColor.Red[300],
      },
      Uploading: {
        Background: PrimitiveColor.Black_Alpha[300],
      },
    },
    UploadingMask: {
      BackgroundColor: PrimitiveColor.Black_Alpha[300],
    },
  },
  Border: {
    UploadItem: {
      Width: Stroke_Width[25],
    },
  },
  Radius: {
    UploadItem: Radius.Xs,
  },
  Spacing: {
    RemoveIcon: {
      MarginBlockStart: '4px',
      MarginInlineEnd: '4px',
    },
    PreviewContainer: {
      Gap: '10px',
    },
    UploadingMask: {
      Padding: '6px',
    },
  },
  ItemSizing: {
    UploadItem: {
      Small: {
        Width: '80px',
        Height: '80px',
      },
      Medium: {
        Width: '150px',
        Height: '150px',
      },
      Large: {
        Width: '200px',
        Height: '200px',
      },
    },
  },
};
