import { I<PERSON>, Spinner, UnstyledButton, clsx } from '@aftership/astra';
import { CloseOutlined } from '@aftership/astra-icons';

import {
  previewContainerRecipe,
  uploadItemRecipe,
  uploadPreviewImage,
  uploadRemoveIconRecipe,
  uploadingMaskRecipe,
} from './FileUpload.css';
import ErrorIcon from './icon/ErrorIcon';
import { Uploader } from './useUpload';

export interface UploadPreviewProps extends Uploader {
  src: string;
  onRemove: () => void;
}

const UploadPreview = (props: UploadPreviewProps) => {
  const { onRemove, src, file, status } = props;
  const loading = status === 'uploading';

  return (
    <div className={clsx(uploadItemRecipe(), previewContainerRecipe({ status }))}>
      {status === 'error' ? (
        <Icon source={ErrorIcon} size='24px' color='error' />
      ) : (
        <img src={src} alt={file.name} className={uploadPreviewImage()} />
      )}
      {loading ? (
        <div className={uploadingMaskRecipe()}>
          <Spinner />
        </div>
      ) : null}
      <UnstyledButton onPress={onRemove} className={uploadRemoveIconRecipe()}>
        <Icon source={CloseOutlined} size='16px' color='inherit' />
      </UnstyledButton>
    </div>
  );
};

export default UploadPreview;
