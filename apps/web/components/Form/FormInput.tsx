import { get } from 'lodash-es';
import { Control, Controller } from 'react-hook-form';

import { TextField, TextFieldProps } from '@aftership/astra';

export type Props = {
  control: Control<any>;
  name: string;
  placeholder?: string;
  fullWidth?: boolean;
  onInputChange?: (value: string, name: string) => void;
  hiddenError?: boolean;
  transform?: (value: string) => string;
} & TextFieldProps;

function FormInput({
  control,
  name,
  placeholder,
  onInputChange,
  fullWidth,
  hiddenError,
  transform,
  ...rest
}: Props) {
  return (
    <Controller
      name={name}
      control={control}
      render={({ field: { onBlur, onChange, value }, formState }) => {
        const msg = get(formState?.errors, name)?.message as string;
        return (
          <TextField
            placeholder={placeholder}
            value={value}
            onChange={(v) => {
              const transformValue = transform ? transform(v) : v;
              onChange(transformValue);
              onInputChange?.(transformValue, name);
            }}
            isFullWidth={fullWidth}
            onBlur={onBlur}
            validationError={msg && !hiddenError ? msg : undefined}
            {...rest}
          />
        );
      }}
    />
  );
}

export default FormInput;
