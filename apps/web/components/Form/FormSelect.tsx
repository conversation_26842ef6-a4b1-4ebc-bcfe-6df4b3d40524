import { get } from 'lodash-es';
import React from 'react';
import { Control, Controller } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

import { Select } from '@aftership/astra';
import { InputFieldErrorType } from '@aftership/returns-logics-core';

import { IErrorItem } from '@/types/error';

export type Option = {
  label: string;
  value: string;
};

export type Props = {
  control: Control<any>;
  placeholder?: string;

  name: string;
  defaultValue?: string;
  errors?: Record<string, InputFieldErrorType>;
  errorMapping?: Record<string, IErrorItem>;

  options: Array<Option>;

  isDisabled?: boolean;

  onSelectChange?: (v: string, name: string) => void;
};

function FormSelect({
  control,
  name,
  options,
  errors,
  errorMapping,
  placeholder,
  isDisabled,
  onSelectChange,
}: Props) {
  const { t } = useTranslation();

  return (
    <Controller
      name={name}
      control={control}
      render={({ field: { onChange, value }, formState }) => {
        const error = errors?.[name];

        const msg =
          error && errorMapping?.[name]
            ? t(errorMapping?.[name]?.[error['code']])
            : get(formState?.errors, name)?.message;

        return (
          <Select
            onChange={(value) => {
              onChange(value[0]);
              onSelectChange?.(String(value[0]), name);
            }}
            options={options}
            value={[value].filter(Boolean) as string[]}
            placeholder={placeholder}
            validationError={msg}
            isDisabled={isDisabled}
          />
        );
      }}
    />
  );
}

export default FormSelect;
