import { get } from 'lodash-es';
import { Control, Controller } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

import { TextArea, TextAreaProps, TypographyProps } from '@aftership/astra';
import { InputFieldErrorType } from '@aftership/returns-logics-core';

import { IErrorItem } from '@/types/error';

export type Props = Omit<TypographyProps<'span'>, 'children'> & {
  control: Control<any>;
  name: string;
  placeholder?: string;

  showError?: boolean;
  errorMapping?: Record<string, IErrorItem>;
  errors?: Record<string, InputFieldErrorType>;
  onInputChange?: (value: string, name: string) => void;
};

function FormTextArea({
  control,
  name,
  placeholder,
  errorMapping,
  errors,
  onInputChange,
  ...rest
}: Props & TextAreaProps) {
  const { t } = useTranslation();
  return (
    <Controller
      name={name}
      control={control}
      render={({ field: { onBlur, onChange, value }, formState }) => {
        const error = errors?.[name];

        const msg =
          error && errorMapping?.[name]
            ? t(errorMapping?.[name]?.[error['code']])
            : get(formState?.errors, name)?.message;

        return (
          <TextArea
            placeholder={placeholder}
            value={value}
            onChange={(v) => {
              onChange(v);
              onInputChange?.(v, name);
            }}
            onBlur={onBlur}
            validationError={msg}
            {...rest}
          />
        );
      }}
    />
  );
}

export default FormTextArea;
