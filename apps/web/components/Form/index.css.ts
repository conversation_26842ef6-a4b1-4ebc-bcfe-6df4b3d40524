import { globalStyle, style } from '@vanilla-extract/css';

import { tokenVars } from '@aftership/astra-tokens/Contract.css';

export const mobileTextFieldClassName = style({
  paddingBlock: tokenVars.Semantic.Space['2Xs'],
});

export const desktopStackClass = style({ width: 288 });

export const mobileStackClass = style({ width: '100%' });

export const mobileSelectClassName = style({});

export const mobileSelectGlobalClassName = globalStyle(`${mobileSelectClassName} > button`, {
  padding: tokenVars.Semantic.Space['L'],
});
