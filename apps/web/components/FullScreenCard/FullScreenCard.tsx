import React from 'react';

import { Box, IconButton, Stack, StackProps, Typography } from '@aftership/astra';
import { ChevronLeftOutlined } from '@aftership/astra-icons';
import { tokenVars } from '@aftership/astra-tokens/Contract.css';

import { wrapperClassName } from './FullScreenCard.css';

import { ScrollBox } from '../ScrollBox';

interface FullScreenCardProps extends StackProps {
  title?: React.ReactNode;
  footer?: React.ReactNode;
  children: React.ReactNode;
  onBack?: () => void;
}

const FullScreenCard = ({
  title = '',
  footer,
  children,
  onBack,
  ...props
}: FullScreenCardProps) => {
  const Space = tokenVars.Semantic.Space;
  return (
    <Stack
      flex={1}
      style={{
        height: 0,
        position: 'relative',
      }}
      direction='column'
      className={wrapperClassName}
      {...props}
    >
      <Box
        // TODO 需要补充
        paddingTop={Space.Xl}
        paddingBottom={Space.L}
        paddingX={Space.L}
        backgroundColor={tokenVars.Semantic.Color.Bg.Body}
      >
        <Stack
          align='center'
          justify='center'
          // TODO 需要补充
          style={{
            height: Space.Xl,
            width: Space.Xl,
          }}
        >
          <IconButton icon={ChevronLeftOutlined} size='medium' onPress={onBack} />
        </Stack>
      </Box>
      <ScrollBox backgroundColor={tokenVars.Semantic.Color.Bg.Body} paddingX={Space.Xs}>
        <Stack
          style={{
            height: '100%',
          }}
          flex={1}
          direction='column'
        >
          {typeof title === 'string' ? (
            <Typography
              variant='headingSm'
              textAlign='center'
              color='primary'
              // TODO 需要补充
              style={{ padding: `0 ${Space.S}` }}
            >
              {title}
            </Typography>
          ) : (
            title
          )}
          {children}
        </Stack>
      </ScrollBox>
      {footer}
    </Stack>
  );
};

export default FullScreenCard;
