import { Box, Spinner, Stack } from '@aftership/astra';

import { overlayClassName } from './FullScreenLoading.css';

export interface IFullScreenLoadingProps {
  loading: boolean;
}
const FullScreenLoading = ({ loading }: IFullScreenLoadingProps) => {
  if (!loading) {
    return null;
  }

  return (
    <Box className={overlayClassName}>
      <Stack
        style={{
          width: '100%',
          height: '100%',
        }}
        align='center'
        justify='center'
      >
        <Spinner />
      </Stack>
    </Box>
  );
};

export default FullScreenLoading;
