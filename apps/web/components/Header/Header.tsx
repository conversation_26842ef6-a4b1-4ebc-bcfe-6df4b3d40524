import React from 'react';

import { Box, IconButton, Stack, StackProps, Typography } from '@aftership/astra';
import { ChevronLeftOutlined } from '@aftership/astra-icons';

export interface HeaderProps {
  onBack?: VoidFunction;
  title: React.ReactNode;
  leftSlot?: React.ReactNode;
  rightSlot?: React.ReactNode;
  alignItems?: StackProps['align'];
  backgroundColor?: string;
  containerRef?: React.RefObject<HTMLDivElement>;
}

const EmptySlot = () => {
  return <Box />;
};

const LeftSlot = ({ onBack }: { onBack?: VoidFunction }) => {
  return <IconButton icon={ChevronLeftOutlined} size='large' onPress={onBack} />;
};

const Header = ({
  onBack,
  title,
  backgroundColor,
  leftSlot,
  rightSlot,
  alignItems = 'start',
  containerRef,
}: HeaderProps) => {
  return (
    <div ref={containerRef} style={{ backgroundColor: backgroundColor }}>
      <Stack gap={'l'} align={alignItems}>
        <Stack direction='column' align='start'>
          {leftSlot ? leftSlot : <LeftSlot onBack={onBack} />}
          <Box height={0} overflow='hidden'>
            {rightSlot ? rightSlot : <EmptySlot />}
          </Box>
        </Stack>
        <Box flex={1}>
          {React.isValidElement(title) ? (
            title
          ) : (
            <Typography as='p' variant='headingXs' textAlign='center' color='primary'>
              {title}
            </Typography>
          )}
        </Box>
        <Stack direction='column' align='end'>
          {rightSlot ? rightSlot : <EmptySlot />}
          <Box height={0} overflow='hidden'>
            {leftSlot ? leftSlot : <LeftSlot onBack={onBack} />}
          </Box>
        </Stack>
      </Stack>
    </div>
  );
};

export default Header;
