import { Box, Stack, StackProps, Typography } from '@aftership/astra';
import { tokenVars } from '@aftership/astra-tokens/Contract.css';

import useDevice from '@/hooks/useDevice';

import { ImageWithFallback } from '../ImageWithFallback';

const IMAGE_SIZE_DESKTOP = 60;
const IMAGE_SIZE_MOBILE = 64;
const MAX_IMAGE_COUNT_DESKTOP = 8;
const MAX_IMAGE_COUNT_MOBILE = 3;

interface ImageListProps {
  list?: string[];
  /**
   * @description 最大展示数量
   * @default 8 - desktop, 3 - mobile
   */
  max?: number;
  imageSize?: number;
  gap?: StackProps['gap'];
  showMore?: boolean;
}

const ImageList = (props: ImageListProps) => {
  const isMobile = useDevice().mobile;
  const {
    list = [],
    max = isMobile ? MAX_IMAGE_COUNT_MOBILE : MAX_IMAGE_COUNT_DESKTOP,
    gap = 'xs',
    showMore = true,
  } = props;
  const imageSize = props.imageSize ?? (isMobile ? IMAGE_SIZE_MOBILE : IMAGE_SIZE_DESKTOP);

  return (
    <Stack gap={gap}>
      {list.slice(0, max).map((item) => (
        <Box key={item} overflow='hidden' borderRadius={tokenVars.Semantic.Radius.Xs}>
          <ImageWithFallback
            usingShopifyPreview
            width={imageSize}
            height={imageSize}
            src={item}
            alt={item}
          />
        </Box>
      ))}
      {showMore && list.length > max && (
        <Box backgroundColor='#F6F6F7' borderRadius={tokenVars.Semantic.Radius.Xs}>
          <Stack
            style={{
              width: imageSize,
              height: imageSize,
            }}
            align='center'
            justify='center'
          >
            <Typography variant='bodyLgSemibold'>+{list.length - max}</Typography>
          </Stack>
        </Box>
      )}
    </Stack>
  );
};

export default ImageList;
