import Image, { ImageProps } from 'next/image';
import React, { memo, useEffect, useState } from 'react';

import thumb from '@/assets/thumbnail.svg';
import { getShopifyPreviewImage } from '@/utils/images.ts';

export type IProps = Record<string, any>;

export interface Props extends ImageProps {
  src: string;
  autoFitWidth?: boolean;
  isBundle?: boolean;
  usingShopifyPreview?: boolean;
}

// const LayerItemImage = (props: Props) => {};

const ImageWithFallback = memo((props: Props) => {
  const {
    usingShopifyPreview = false,
    autoFitWidth,
    width,
    height,
    src,
    alt,
    style,
    ...rest
  } = props;
  const previewSrc = usingShopifyPreview ? getShopifyPreviewImage(src) : src;
  const [imgSrc, setImgSrc] = useState(previewSrc || thumb.src);

  useEffect(() => {
    setImgSrc(previewSrc || thumb.src);
  }, [previewSrc]);
  if (autoFitWidth) {
    return (
      <div
        style={{
          position: 'relative',
          width: '100%',
          height: '0',
          paddingTop: '100%' /* 高度与宽度相等 */,
        }}
      >
        <Image
          alt={alt}
          src={imgSrc}
          onError={() => {
            setImgSrc(thumb.src);
          }}
          fill={true}
          layout='fill'
          style={{ objectFit: 'cover', ...style }}
          {...rest}
        />
      </div>
    );
  }

  return (
    <Image
      alt={alt}
      src={imgSrc}
      onError={() => {
        setImgSrc(thumb.src);
      }}
      width={width}
      height={height}
      style={{ objectFit: 'cover', ...style }}
      {...rest}
    />
  );
});

ImageWithFallback.displayName = 'ImageWithFallback';

export default ImageWithFallback;
