import { t } from 'i18next';
import React from 'react';

import { Stack, Typography } from '@aftership/astra';

export interface Props {
  tags: Array<string>;
}
const CustomizationsTags = ({ tags }: Props) => {
  if (!tags.length) {
    return null;
  }
  return (
    <Stack direction={'column'}>
      <Typography variant='bodySm' color='secondary'>
        {t('page.description.customizationAdded')}
      </Typography>

      {tags.map((tag, index) => (
        <Typography
          key={`${tag}-${index}`}
          variant='bodySm'
          color='secondary'
        >{`• ${tag}`}</Typography>
      ))}
    </Stack>
  );
};

export default CustomizationsTags;
