import React from 'react';

import { CardP<PERSON>, Stack, TypographyProps } from '@aftership/astra';
import { IconSource } from '@aftership/astra';
import { PresentmentMoney } from '@aftership/returns-logics-core';

import ItemCardContent from '@/components/ItemCardContent/ItemCardContent';

import { CardSection } from '../Card';

export interface ProductInfo {
  /**
   * 商品标题
   */
  productTitle: string;
  /**
   * 商品variant标题
   */
  variantTitle?: string;
  /**
   * 商品价格信息
   */
  price?: PresentmentMoney | null;
  /**
   * 商品的原始价格
   */
  originPrice?: PresentmentMoney | null;
  /**
   * 商品标签列表
   */
  productTags?: string[];
  /**
   * 商品数量
   */
  quantity?: number;

  productCoverUrl?: string;
}

export interface ItemCardProps extends Omit<CardProps, 'children'> {
  titleClassName?: string;
  title?: React.ReactNode | string;
  image?: string | IconSource;
  footer?: React.ReactNode;
  productInfo: ProductInfo;
  returnReason?: string;
  returnSubReason?: string;
  rightContent?: React.ReactNode;
  showMoreReason?: boolean;
  showMoreReasonClick?: () => void;
  hideDivider?: boolean;
  productTitleVariant?: TypographyProps<'span'>['variant'];
  productTitleContainerStyles?: string;
  exchangeNotes?: string;
}

const ItemCard: React.FC<ItemCardProps> = ({
  footer,
  productInfo,
  returnReason,
  returnSubReason,
  rightContent,
  showMoreReason,
  hideDivider = false,
  productTitleVariant,
  productTitleContainerStyles,
  showMoreReasonClick,
  exchangeNotes,
}) => {
  return hideDivider ? (
    <Stack direction={'column'} gap='m'>
      <ItemCardContent
        productInfo={productInfo}
        rightContent={rightContent}
        showMoreReasonClick={showMoreReasonClick}
        showMoreReason={showMoreReason}
        returnReason={returnReason}
        productTitleContainerStyles={productTitleContainerStyles}
        returnSubReason={returnSubReason}
        exchangeNotes={exchangeNotes}
      />
      {footer}
    </Stack>
  ) : (
    <CardSection>
      <Stack direction={'column'} gap='m'>
        <ItemCardContent
          productTitleVariant={productTitleVariant}
          productInfo={productInfo}
          rightContent={rightContent}
          showMoreReasonClick={showMoreReasonClick}
          showMoreReason={showMoreReason}
          returnReason={returnReason}
          productTitleContainerStyles={productTitleContainerStyles}
          returnSubReason={returnSubReason}
          exchangeNotes={exchangeNotes}
        />
        {footer}
      </Stack>
    </CardSection>
  );
};

export default ItemCard;
