import React, { useMemo } from 'react';
import { useTranslation } from 'react-i18next';

import {
  Box,
  Grid,
  Icon,
  ModalTrigger,
  Pressable,
  Stack,
  Tooltip,
  Typography,
  TypographyProps,
  UnstyledButton,
} from '@aftership/astra';
import { ChevronRightOutlined, FileTextOutlined } from '@aftership/astra-icons';
import { tokenVars } from '@aftership/astra-tokens/Contract.css';
import { PresentmentMoney } from '@aftership/returns-logics-core';

import CustomizationsTags from '@/components/ItemCard/CustomizationsTags';
import { productTitleContainer } from '@/components/ItemCardContent/styles.css.ts';
import { Modal } from '@/components/Modal';
import useDevice from '@/hooks/useDevice.ts';
import { toCurrency } from '@/utils/price';
import { decodeHtmlEntities, extractStorefrontTags } from '@/utils/products';
import { renderIfNotNullOrUndefined } from '@/utils/render';

import { EllipsisText } from '../EllipsisText/index.ts';
import ImageWithFallback from '../ImageWithFallback/ImageWithFallback.tsx';

const { Space } = tokenVars.Semantic;

export interface ProductInfo {
  /**
   * 商品标题
   */
  productTitle: string;
  /**
   * 商品variant标题
   */
  variantTitle?: string;
  /**
   * 商品价格信息
   */
  price?: PresentmentMoney | null;
  /**
   * 商品的原始价格
   */
  originPrice?: PresentmentMoney | null;
  /**
   * 商品标签列表
   */
  productTags?: string[];
  /**
   * 商品数量
   */
  quantity?: number;

  productCoverUrl?: string;
}
export interface ItemCardContentProps {
  productTitleVariant?: TypographyProps<'span'>['variant'];
  productInfo: ProductInfo;
  returnReason?: string;
  returnSubReason?: string;
  showMoreReason?: boolean;
  showMoreReasonClick?: () => void;
  rightContent?: React.ReactNode;
  productTitleContainerStyles?: string;
  exchangeNotes?: string;
}

const ItemCardContent = ({
  productTitleVariant = 'bodyLgSemibold',
  productInfo,
  returnReason,
  returnSubReason,
  showMoreReason,
  showMoreReasonClick,
  rightContent,
  productTitleContainerStyles = productTitleContainer,
  exchangeNotes,
}: ItemCardContentProps) => {
  const { t } = useTranslation();
  const isMobile = useDevice().mobile;
  const {
    productTitle: productTitleOrigin,
    productTags,
    price,
    originPrice,
    variantTitle: variantTitleOrigin,
    quantity,
    productCoverUrl,
  } = productInfo ?? {};
  const productTitle = useMemo(() => decodeHtmlEntities(productTitleOrigin), [productTitleOrigin]);
  const variantTitle = useMemo(() => decodeHtmlEntities(variantTitleOrigin), [variantTitleOrigin]);
  const tags = extractStorefrontTags(productTags || []);

  // 是否展示原价. 当原价和现价都有值，且现价小于原价时展示原价
  const showOriginalPrice =
    originPrice?.amount && price?.amount && Number(price.amount) < Number(originPrice.amount);

  const renderProductTitle = () => {
    return renderIfNotNullOrUndefined(
      productTitle,
      <EllipsisText
        maxLine={isMobile ? 2 : 1}
        variant={productTitleVariant}
        color='primary'
        text={productTitle}
      />,
    );
  };

  const renderVariantTitle = () => {
    return renderIfNotNullOrUndefined(
      variantTitle,
      <EllipsisText variant='bodyMd' text={variantTitle || ''} color='secondary' />,
    );
  };

  const renderProductTags = () => {
    return renderIfNotNullOrUndefined(tags?.length, <CustomizationsTags tags={tags ?? []} />);
  };

  const renderReturnReason = () => {
    return renderIfNotNullOrUndefined(
      returnReason,
      (() => {
        const content = (
          <Stack gap='2xs' align='center' wrap='nowrap' style={{ maxWidth: '284px' }}>
            <Stack gap='none' align='center'>
              <EllipsisText
                maxLine={1}
                variant='bodyMd'
                color='secondary'
                text={`${returnReason}${returnSubReason ? ` (${returnSubReason})` : ''}`}
              />
            </Stack>
            {showMoreReason && (
              <Icon
                source={ChevronRightOutlined}
                size={tokenVars.Semantic.Space['M']}
                color='secondary'
              />
            )}
          </Stack>
        );

        return showMoreReason ? (
          <Pressable onPress={showMoreReasonClick}>{content}</Pressable>
        ) : (
          content
        );
      })(),
    );
  };

  const renderExchangeNotes = () => {
    const content = (
      <Stack align='center' gap='2xs'>
        <Icon source={FileTextOutlined} color='secondary' />
        <Typography variant='bodyMd' color='secondary'>
          {t('page.description.exchangeNotes')}
        </Typography>
      </Stack>
    );

    if (!exchangeNotes) {
      return null;
    }

    if (isMobile) {
      return (
        <ModalTrigger>
          <UnstyledButton style={{ marginBlockStart: Space['2Xs'] }}>
            <Stack align='center' gap='2xs'>
              <Icon source={FileTextOutlined} color='secondary' />
              <Typography variant='bodyMd' color='secondary'>
                {t('page.description.exchangeNotes')}
              </Typography>
            </Stack>
          </UnstyledButton>
          <Modal title={t('page.description.exchangeNotes')} minHeight='240px'>
            {exchangeNotes}
          </Modal>
        </ModalTrigger>
      );
    }

    return (
      <Tooltip content={exchangeNotes} placement='top'>
        <UnstyledButton style={{ alignSelf: 'flex-start', marginBlockStart: Space['2Xs'] }}>
          {content}
        </UnstyledButton>
      </Tooltip>
    );
  };

  // price 展示
  const renderPrice = () => {
    return (
      <Box marginTop={Space.S}>
        <Stack direction='row' gap={'xs'} align={'baseline'}>
          {/* 是否展示原价 */}
          {showOriginalPrice && (
            <Typography
              variant='bodyMd'
              color='secondary'
              style={{ textDecoration: 'line-through' }}
            >
              {toCurrency(originPrice?.amount, originPrice.currency)}
            </Typography>
          )}
          {price?.amount && price?.currency && (
            <Typography variant='bodyMd' color='primary'>
              {toCurrency(price!.amount, price!.currency)}
            </Typography>
          )}
          {!!quantity && (
            <Typography variant='bodyMd' color='primary'>{`x ${quantity}`}</Typography>
          )}
        </Stack>
      </Box>
    );
  };
  const renderRightContent = () => {
    return renderIfNotNullOrUndefined(rightContent, rightContent);
  };

  return (
    <Grid direction={'row'} columnSpacing={'m'} container>
      <Box borderRadius={tokenVars.Semantic.Radius['Xs']} overflow='hidden' width={80} height={80}>
        <ImageWithFallback
          usingShopifyPreview
          width={80}
          height={80}
          src={productCoverUrl ?? ''}
          alt=''
        />
      </Box>

      <Stack className={productTitleContainerStyles}>
        <Stack direction='column' flex='0'>
          {renderProductTitle()}
          {renderVariantTitle()}
          {renderProductTags()}
          {renderReturnReason()}
          {renderExchangeNotes()}
          {!isMobile && renderPrice()}
        </Stack>
        {isMobile && renderPrice()}
      </Stack>
      <Grid size='auto' container>
        {renderRightContent()}
      </Grid>
    </Grid>
  );
};

export default ItemCardContent;
