import { Icon, <PERSON>, Stack, Typography } from '@aftership/astra';
import { ShareFilled } from '@aftership/astra-icons';

interface LinkWithIconProps {
  link?: string | null;
  children?: React.ReactNode;
}

const LinkWithIcon = ({ link, children }: LinkWithIconProps) => {
  return (
    <Link target='_blank' href={link || ''}>
      <Stack align='center' gap={'xs'}>
        {typeof children === 'string' ? (
          <Typography variant='bodyMd' color='primary'>
            {children}
          </Typography>
        ) : (
          children
        )}
        <Icon source={ShareFilled} color='secondary' />
      </Stack>
    </Link>
  );
};

export default LinkWithIcon;
