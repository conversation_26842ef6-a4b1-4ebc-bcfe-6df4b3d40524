import { calc } from '@vanilla-extract/css-utils';
import { recipe } from '@vanilla-extract/recipes';

import { tokenVars } from '@aftership/astra-tokens/Contract.css';
import { primitiveTokenVar } from '@aftership/astra-tokens/primitive-token/Contract.css';

const { Space, Radius, Color } = tokenVars.Semantic;
const { Stroke_Width } = primitiveTokenVar;

export const listBoxItemRecipe = recipe({
  base: [
    { outlineStyle: 'none', overflow: 'hidden' },
    {
      position: 'relative',
      borderRadius: Radius.M,
      selectors: {
        '&::after': {
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          content: '',
          borderRadius: Radius.M,
          outline: `${Stroke_Width[25]} solid ${Color.Border.Primary}`,
          outlineOffset: calc(Stroke_Width[25]).negate().toString(),
          pointerEvents: 'none',
        },
        '&[data-selected]::after': {
          outline: `${Stroke_Width[50]} solid ${Color.Brand.Primary}`,
          outlineOffset: calc(Stroke_Width[50]).negate().toString(),
        },
      },
    },
  ],
});

export const listBoxRecipe = recipe({
  base: [{ outlineStyle: 'none' }, { display: 'flex', flexDirection: 'column', rowGap: Space.M }],
});
