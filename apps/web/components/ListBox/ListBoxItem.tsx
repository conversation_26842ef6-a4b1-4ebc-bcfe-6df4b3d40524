import { clsx } from 'clsx';

import { GridListItem } from '@aftership/astra';

import { listBoxItemRecipe } from './ListBox.css';
import { ListBoxItemProps } from './type';

const ListBoxItem = ({ children, className, style, ...props }: ListBoxItemProps) => {
  return (
    <GridListItem className={clsx(listBoxItemRecipe(), className)} style={style} {...props}>
      {children}
    </GridListItem>
  );
};

export default ListBoxItem;
