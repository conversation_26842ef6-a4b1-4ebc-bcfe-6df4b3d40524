import { Spinner, SpinnerProps, Stack, StackProps } from '@aftership/astra';

interface LoadingWrapperProps {
  isLoading?: boolean;
  children: React.ReactNode;
  spinnerProps?: SpinnerProps;
  wrapperProps?: StackProps;
}

const LoadingWrapper = ({
  isLoading = false,
  children,
  spinnerProps,
  wrapperProps,
}: LoadingWrapperProps) => {
  if (isLoading) {
    return (
      <Stack
        align='center'
        justify='center'
        style={{
          height: '100%',
        }}
        {...wrapperProps}
      >
        <Spinner {...spinnerProps} />
      </Stack>
    );
  }

  return <>{children}</>;
};

export default LoadingWrapper;
