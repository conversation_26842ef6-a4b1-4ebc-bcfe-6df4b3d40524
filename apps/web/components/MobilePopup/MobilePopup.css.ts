import { style } from '@vanilla-extract/css';

import { tokenVars } from '@aftership/astra-tokens/Contract.css';

export const containerStyle = style({
  overflowX: 'hidden',
});
export const variantContainerStyle = style({
  flexGrow: 1,
  flexBasis: 0,
  overflowY: 'auto',
  paddingRight: tokenVars.Semantic.Space.Xl,
  paddingLeft: tokenVars.Semantic.Space.Xl,
  display: 'flex',
  gap: tokenVars.Semantic.Space.Xl,
  flexDirection: 'column',
});

export const bodyMobileClassName = style({
  overflowY: 'hidden',
  display: 'flex',
  flexBasis: 0,
  flex: 1,
  flexDirection: 'column',
});
export const variantBodyMobileClassName = style({
  marginLeft: tokenVars.Semantic.Space.S,
  marginRight: tokenVars.Semantic.Space.S,
  paddingLeft: tokenVars.Semantic.Space.S,
  paddingRight: tokenVars.Semantic.Space.S,
  paddingBottom: tokenVars.Semantic.Space['3Xl'],
  flex: 1,
  overflowY: 'auto',
  display: 'flex',
  flexDirection: 'column',
  maxHeight: 'max-content',
  gap: tokenVars.Semantic.Space.Xl,
});

export const flexJustifyContentCenter = style({
  justifyContent: 'center',
});
