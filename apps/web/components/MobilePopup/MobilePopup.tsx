import { isValidElement } from 'react';

import {
  Box,
  DimensionValue,
  Drawer,
  Drawer<PERSON><PERSON>,
  DrawerHeader,
  IconButton,
  Stack,
  Typography,
} from '@aftership/astra';
import { ChevronLeftOutlined, CloseOutlined } from '@aftership/astra-icons';
import { tokenVars } from '@aftership/astra-tokens/Contract.css';

import {
  bodyMobileClassName,
  flexJustifyContentCenter,
  variantBodyMobileClassName,
} from './MobilePopup.css';

export interface IMobilePopupProps {
  isOpen: boolean;
  onClose?: VoidFunction;
  onBack?: VoidFunction;
  minHeight?: string;
  header?: React.ReactNode | React.ReactNode[];
  children?: React.ReactNode | React.ReactNode[];
  leftSlot?: React.ReactNode;
  rightSlot?: React.ReactNode;
  title?: React.ReactNode;
  footer?: React.ReactNode;
  height?: DimensionValue;
  layout?: 'center' | 'top';
}

const MobilePopup = ({
  isOpen,
  leftSlot,
  rightSlot,
  minHeight = '40%',
  height,
  header,
  footer,
  title,
  layout = 'center',
  children,
  onClose,
  onBack,
}: IMobilePopupProps) => {
  const renderHeader = () => {
    return (
      <Stack
        direction='row'
        style={{
          width: '100%',
        }}
      >
        {header ?? [
          <Box key={'left'}>
            {leftSlot ? (
              leftSlot
            ) : (
              <Stack
                align='center'
                justify='center'
                style={{
                  height: tokenVars.Semantic.Space['3Xl'],
                  width: tokenVars.Semantic.Space['3Xl'],
                }}
              >
                <IconButton
                  icon={ChevronLeftOutlined}
                  onPress={onBack}
                  style={{
                    color: onBack
                      ? tokenVars.Semantic.Color.Icon.Primary
                      : // TODO 透明或者移除
                        'rgba(0, 0, 0, 0)',
                  }}
                />
              </Stack>
            )}
          </Box>,
          <Box flex={1} key={'middle'}>
            {isValidElement(title) ? (
              title
            ) : (
              <Stack direction='row' justify='center' align='center' style={{ height: '100%' }}>
                <Box>
                  <Typography textAlign='center' variant='headingXs'>
                    {title}
                  </Typography>
                </Box>
              </Stack>
            )}
          </Box>,
          <Box key={'right'}>
            {rightSlot ? (
              rightSlot
            ) : (
              <Stack
                align='center'
                justify='center'
                style={{
                  height: tokenVars.Semantic.Space['3Xl'],
                  width: tokenVars.Semantic.Space['3Xl'],
                }}
              >
                <IconButton
                  icon={CloseOutlined}
                  style={{
                    color: onClose
                      ? tokenVars.Semantic.Color.Icon.Primary
                      : // TODO 透明或者移除
                        'rgba(0, 0, 0, 0)',
                  }}
                />
              </Stack>
            )}
          </Box>,
        ]}
      </Stack>
    );
  };

  const layoutClassName = layout === 'center' ? flexJustifyContentCenter : '';
  return (
    <Drawer
      placement='bottom'
      style={{
        height,
        minHeight,
      }}
      isOpen={isOpen}
      onOpenChange={(isOpen) => !isOpen && onClose?.()}
    >
      <DrawerHeader>{renderHeader()}</DrawerHeader>
      <DrawerBody>
        <Box className={bodyMobileClassName}>
          <Box className={`${variantBodyMobileClassName} ${layoutClassName}`}>{children}</Box>
          {footer}
        </Box>
      </DrawerBody>
    </Drawer>
  );
};

export default MobilePopup;
