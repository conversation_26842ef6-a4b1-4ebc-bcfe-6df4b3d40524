import {
  Modal as AstraModal,
  ModalProps as AstraModalProps,
  Box,
  ModalBody,
  ModalFooter,
  ModalHeader,
  Stack,
  useBreakpointValue,
} from '@aftership/astra';

import { Button } from '@/components/Button';

import Sheet, { SheetProps } from '../Sheet/Sheet';

interface ModalProps extends SheetProps {
  size?: AstraModalProps['size'];
  /**
   * @default true
   * @description
   * - true: responsive
   * - false: not responsive
   */
  responsive?: boolean;
  /**
   * @description Only available for mobile, display leading action
   */
  leadingAction?: SheetProps['leadingAction'];
  minHeight?: string | number;
  bodyClassName?: string;
  bodyStyle?: React.CSSProperties;
}

export const Modal = ({
  responsive = true,
  leadingAction,
  title,
  minHeight,
  bodyClassName,
  bodyStyle,
  ...rest
}: ModalProps) => {
  const isMobile = useBreakpointValue<'base' | 'm', boolean>({
    base: responsive,
    m: false,
  });

  if (isMobile) {
    return (
      <Sheet
        title={title}
        minHeight={minHeight}
        leadingAction={leadingAction}
        bodyClassName={bodyClassName}
        bodyStyle={bodyStyle}
        {...rest}
      />
    );
  }

  const { footerExtra, onClose, children, primaryAction, secondaryAction, ...restProps } = rest;

  return (
    <AstraModal isRounded onOpenChange={(isOpen) => !isOpen && onClose?.()} {...restProps}>
      {(onClose || title) && (
        <ModalHeader
          onClose={onClose}
          title={title}
          textAlign='center'
          showCloseButton={!!onClose}
        />
      )}
      <ModalBody className={bodyClassName} style={bodyStyle}>
        {children}
      </ModalBody>
      {(primaryAction || secondaryAction || footerExtra) && (
        <ModalFooter isRounded>
          <Stack flex={1} gap='m'>
            {footerExtra && (
              <>
                {footerExtra}
                <Box style={{ flex: 1 }} />
              </>
            )}
            {secondaryAction && (
              <Button
                size='large'
                variant='basic'
                isFullWidth={!footerExtra}
                onPress={secondaryAction.onAction}
                {...secondaryAction}
              >
                {secondaryAction?.content}
              </Button>
            )}
            {primaryAction && (
              <Button
                size='large'
                isFullWidth={!footerExtra}
                onPress={primaryAction.onAction}
                {...primaryAction}
              >
                {primaryAction?.content}
              </Button>
            )}
          </Stack>
        </ModalFooter>
      )}
    </AstraModal>
  );
};

export default Modal;
