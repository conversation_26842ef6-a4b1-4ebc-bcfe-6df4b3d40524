import { t } from 'i18next';
import React from 'react';

import { Box } from '@aftership/astra';
import { semanticTokenVar } from '@aftership/astra-tokens/semantic-token/Contract.css';

import useDevice from '@/hooks/useDevice';

import { Button } from '../Button';

export interface NextButtonProps {
  isLoading: boolean;
  isDisabled: boolean;
  onPress: VoidFunction;
}
const NextButton = ({ isLoading, isDisabled, onPress }: NextButtonProps) => {
  const isMobile = useDevice().mobile;
  return (
    <Box
      paddingX={isMobile ? semanticTokenVar.Space.M : '160px'}
      paddingY={semanticTokenVar.Space.M}
    >
      <Button
        isFullWidth
        isLoading={isLoading}
        isDisabled={isDisabled}
        size={'large'}
        onPress={onPress}
      >
        {t('page.request.nextStep')}
      </Button>
    </Box>
  );
};

export default NextButton;
