import Image from 'next/image';
import React from 'react';

import { Select, Stack, Typography } from '@aftership/astra';
import { CountryItem } from '@aftership/returns-logics-core';
import { useCountries } from 'returns-logics/react';

import useDevice from '@/hooks/useDevice';

import { desktopStackClass, mobileSelectClassName, mobileStackClass } from '../Form/index.css';

export const formatCountriesToOptions = (countries?: CountryItem[]) => {
  return (
    countries
      ?.filter((item) => Boolean(item.calling_code))
      .map(({ name, calling_code, flag_url, code }) => ({
        // 不用 calling code 作为 value，是因为会重复
        value: code,
        label: calling_code || '',
        flag_url,
        name,
      })) ?? []
  );
};

type CallingCodeData = {
  countryCode: string;
  callingCode: string;
};

type CallingPhoneCodeProps = {
  value?: CallingCodeData;
  onChange: (value: CallingCodeData) => void;
};

// FIXME: 有类型问题
const CallingPhoneCode = ({ value, onChange }: CallingPhoneCodeProps) => {
  const {
    data: { countries },
  } = useCountries();

  const isMobile = useDevice().mobile;

  const callingCodeOptions = formatCountriesToOptions(Object.values(countries ?? {}));

  const selectedOption = callingCodeOptions.find((item) => item.value === value?.countryCode);

  return (
    <Stack>
      <Select
        options={callingCodeOptions}
        value={[value?.countryCode].filter(Boolean) as string[]}
        className={isMobile ? mobileSelectClassName : undefined}
        // @ts-ignore
        onChange={(v: string) => {
          const selectedCountry = countries?.[v];
          onChange({
            callingCode: selectedCountry?.calling_code ?? '',
            countryCode: selectedCountry?.code ?? '',
          });
        }}
        placeholder='-'
        prefix={
          selectedOption ? (
            <Image src={selectedOption?.flag_url ?? ''} alt='country icon' width={28} height={24} />
          ) : (
            ''
          )
        }
        isFullWidth={false}
        optionRender={({ label, flag_url, name }) => (
          <Stack gap='l' className={isMobile ? mobileStackClass : desktopStackClass}>
            <Image src={flag_url} alt='country icon' width={32} height={24} />
            <Stack flex={1}>
              <Typography style={{ wordBreak: 'break-word' }}>{name}</Typography>
            </Stack>
            <Typography>{label}</Typography>
          </Stack>
        )}
        style={{ minWidth: 64 }}
      />
    </Stack>
  );
};

export default CallingPhoneCode;
