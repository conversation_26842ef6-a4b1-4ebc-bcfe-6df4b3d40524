import React, { ReactNode } from 'react';

import { Link, Typography, TypographyProps } from '@aftership/astra';

export interface IRegexpRichTextProps extends TypographyProps<'span'> {
  children: ReactNode;
}

export enum TagType {
  Link = 'link',
  Text = 'text',
}

export interface ITag {
  type: TagType;
  content: string;
  properties?: Record<string, string>;
}

const TAG_A_REGEX = /<a\s([^<>]*)>([^<>]*)<\/a>/;
const PROPERTY_A_REGEX = /([a-zA-Z]+?)="([^"]+?)"/g;

function extractTags(str: string) {
  const results: ITag[] = [];

  if (!str) {
    return [];
  }

  try {
    const match = new RegExp(TAG_A_REGEX).exec(str);
    if (match) {
      const [, propertyContent, innerContent] = match;
      // ["href=\"https://www.google.com/\"", "target=\"_blank\""]
      const propertyList = propertyContent.match(new RegExp(PROPERTY_A_REGEX)) || [];
      const properties: Record<string, string> = {};

      propertyList.forEach((property) => {
        // [content, key, value]
        const result = new RegExp(PROPERTY_A_REGEX).exec(property) || [];
        const [, key, value] = result;
        key && (properties[key] = value || '');
      });

      results.push({ type: TagType.Text, content: str.slice(0, match.index) });
      results.push({ type: TagType.Link, content: innerContent, properties });
      results.push(...extractTags(str.slice(match.index + match[0].length)));
    } else {
      results.push({ type: TagType.Text, content: str });
    }

    return results;
  } catch (error) {
    return [];
  }
}

const RegexpRichText = ({ children, ...props }: IRegexpRichTextProps) => {
  if (typeof children !== 'string') {
    return <Typography {...props}>{children}</Typography>;
  }

  const tags = extractTags(children);

  return (
    <Typography {...props}>
      {tags.map((tag) => {
        switch (tag.type) {
          case TagType.Link: {
            return (
              <Link
                key={tag.content}
                href={tag.properties?.href}
                target={tag.properties?.target}
                className={props.className}
              >
                {tag.content}
              </Link>
            );
          }
          default:
            return tag.content;
        }
      })}
    </Typography>
  );
};

export default RegexpRichText;
