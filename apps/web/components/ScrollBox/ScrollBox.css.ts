import { style } from '@vanilla-extract/css';

export const outerContainer = style({
  display: 'flex',
  flex: 1,
  overflow: 'hidden',
  flexDirection: 'column',
});
export const innerContainer = style({
  display: 'flex',
  flexDirection: 'column',
  width: '100%',
  flex: 1,
  overflowY: 'auto',
  // @ts-ignore
  // preview 模式下，container 会被设置为 pointer-events: none，导致无法滚动，因此这儿需要重置
  pointerEvents: 'auto !important',
});
