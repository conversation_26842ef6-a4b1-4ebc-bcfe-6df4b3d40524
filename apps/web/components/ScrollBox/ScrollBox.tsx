import React, { ReactNode, Ref } from 'react';

import { Box, BoxProps } from '@aftership/astra';

import * as styles from './ScrollBox.css';

interface ScrollBoxProps extends BoxProps {
  children: ReactNode;
  className?: string;
  innerProps?: BoxProps;
  innerRef?: Ref<HTMLDivElement>;
}

const ScrollBox = ({
  padding,
  paddingY,
  paddingTop,
  paddingBottom,
  paddingX,
  paddingStart,
  paddingEnd,
  children,
  innerProps,
  className,
  innerRef,
  ...props
}: ScrollBoxProps) => {
  const start = paddingStart || paddingX || padding || 0;
  const end = paddingEnd || paddingX || padding || 0;
  const top = paddingTop || paddingY || padding || 0;
  const bottom = paddingBottom || paddingY || padding || 0;

  return (
    <Box className={styles.outerContainer} {...props}>
      <Box
        paddingStart={start}
        paddingEnd={end}
        paddingTop={top}
        paddingBottom={bottom}
        className={`${styles.innerContainer} ${className}`}
        ref={innerRef}
        {...innerProps}
      >
        {children}
      </Box>
    </Box>
  );
};

export default ScrollBox;
