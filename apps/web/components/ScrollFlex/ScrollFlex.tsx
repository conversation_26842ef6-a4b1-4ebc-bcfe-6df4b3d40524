import React, { Ref } from 'react';

import { useBreakpointValue } from '@aftership/astra';
import { semanticTokenVar } from '@aftership/astra-tokens/semantic-token/Contract.css';

import { ScrollBox } from '../ScrollBox';

interface ScrollFlexProps {
  className?: string;
  children: React.ReactNode;
  innerRef?: Ref<HTMLDivElement>;
}

const { Space } = semanticTokenVar;

const ScrollFlex = ({ children, className, innerRef }: ScrollFlexProps) => {
  const paddingBottom = useBreakpointValue({ base: 0, m: Space.M });
  const paddingX = useBreakpointValue({ base: Space.L, m: Space['3Xl'] });
  const paddingTop = useBreakpointValue({ base: 0, m: Space.M });

  return (
    <ScrollBox
      className={className}
      paddingX={paddingX}
      paddingTop={paddingTop}
      paddingBottom={paddingBottom}
      innerRef={innerRef}
    >
      {children}
    </ScrollBox>
  );
};

export default ScrollFlex;
