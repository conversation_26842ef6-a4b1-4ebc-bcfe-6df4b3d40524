import { recipe } from '@vanilla-extract/recipes';

import { tokenVars } from '@aftership/astra-tokens/Contract.css';
import { resetClassNames } from '@aftership/astra-tokens/reset.css';
import { bodyLgDefaultText, bodyLgDefaultTextClassName } from '@aftership/astra-tokens/texts.css';

const colorVars = tokenVars.Primitive.Color;
const strokeWidthVars = tokenVars.Primitive.Stroke_Width;
const radiusVars = tokenVars.Primitive.Radius;
const spacingVars = tokenVars.Semantic.Space;

const { Color } = tokenVars.Semantic;

export const searchFieldInputRecipe = recipe({
  base: [
    resetClassNames.noBorder,
    resetClassNames.noOutline,
    resetClassNames.noMargin,
    bodyLgDefaultTextClassName,
    {
      color: Color.Text.Primary,
      border: `${strokeWidthVars[25]} solid ${colorVars.Gray[400]}`,
      borderRadius: radiusVars[200],
      padding: `${spacingVars.Xs} ${spacingVars.M}`,
      width: '100%',
      selectors: {
        '&[data-focused]': {
          borderColor: Color.Border.Secondary,
        },
        '&[data-hovered]': {
          borderColor: Color.Border.Secondary,
        },
        '&[data-invalid]': {
          borderColor: colorVars.Red['1000'],
        },
        '&::placeholder': {
          ...bodyLgDefaultText,
          color: colorVars.Gray['1000'],
        },
      },
    },
  ],
});
