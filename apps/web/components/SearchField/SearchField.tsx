import { OverlayTriggerState, RawComboBox, RawInput } from '@aftership/astra';

import { searchFieldInputRecipe } from './SearchField.css';
import { SearchFieldProps } from './type';

import { DropdownKey } from '../Dropdown';
import DropdownBase from '../Dropdown/DropdownBase';

type THandleActionState = OverlayTriggerState & { setSelectedKey?: (key: DropdownKey) => void };

const SearchField = (props: SearchFieldProps) => {
  const { items, placeholder, ...rest } = props;

  const handleAction = (key: DropdownKey, state?: THandleActionState) => {
    state?.setSelectedKey?.(key);
  };

  return (
    <DropdownBase
      trigger={RawComboBox}
      items={items}
      onAction={handleAction}
      triggerProps={{
        menuTrigger: 'focus',
        allowsCustomValue: true,
        ...rest,
      }}
      renderChildren={<RawInput className={searchFieldInputRecipe()} placeholder={placeholder} />}
    />
  );
};

export default SearchField;
