import { RawComboBoxProps, RawInputProps } from '@aftership/astra';

import { DropdownItemProps } from '../Dropdown';

export interface SearchFieldItemProps extends DropdownItemProps {}

type ComboBoxPropsType = RawComboBoxProps<SearchFieldItemProps>;

export interface SearchFieldProps extends Omit<ComboBoxPropsType, 'children'> {
  /**
   * The interaction required to display the ComboBox menu.
   * @default 'focus'
   */
  menuTrigger?: ComboBoxPropsType['menuTrigger'];
  /**
   * Whether the ComboBox allows a non-item matching input value to be set.
   * @default true
   */
  allowsCustomValue?: ComboBoxPropsType['allowsCustomValue'];

  placeholder?: RawInputProps['placeholder'];
}
