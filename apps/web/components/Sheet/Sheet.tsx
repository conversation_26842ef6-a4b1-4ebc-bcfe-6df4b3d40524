import React from 'react';

import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
} from '@aftership/astra';
import { semanticTokenVar } from '@aftership/astra-tokens/semantic-token/Contract.css';

import SheetHeader, { SheetHeaderProps } from './SheetHeader';

import { Button } from '../Button';

/**
 * Almost same as <PERSON><PERSON>, but with arbitrary header and footer.
 * And it is used on mobile.
 */
export interface SheetProps
  extends Omit<DrawerProps, 'header' | 'title' | 'onOpenChange' | 'tokens'>,
    SheetHeaderProps {
  onClose?: () => void;
  children: React.ReactNode;
  /**
   * @default {SheetFooter}
   * @description Footer of the sheet, will be rendered at the bottom and will be sticky
   */
  footerExtra?: React.ReactNode;
  /**
   * Customize the default footer
   */
  footer?: React.ReactNode;
  /**
   * @default false
   */
  disableFocusManagement?: boolean;
  minHeight?: string | number;
  bodyClassName?: string;
  bodyStyle?: React.CSSProperties;
}

const { Space } = semanticTokenVar;

const Sheet = ({
  isOpen,
  children,
  footerExtra,
  primaryAction,
  secondaryAction,
  onClose,
  isClosable = true,
  title,
  leadingAction,
  footer,
  placement = 'bottom',
  minHeight,
  bodyClassName,
  bodyStyle,
  ...props
}: SheetProps) => {
  return (
    <Drawer
      {...props}
      isOpen={isOpen}
      placement={placement}
      onOpenChange={(isOpen) => !isOpen && onClose?.()}
      style={{ borderTopLeftRadius: Space.M, borderTopRightRadius: Space.M, ...props.style }}
      isClosable={false}
      shouldCloseOnInteractOutside={() => false}
    >
      {!!(title || isClosable || leadingAction) && (
        <DrawerHeader style={{ padding: Space.M }}>
          <SheetHeader
            title={title}
            leadingAction={leadingAction}
            isClosable={isClosable}
            onClose={onClose}
          />
        </DrawerHeader>
      )}
      <DrawerBody style={{ minHeight, ...bodyStyle }} className={bodyClassName}>
        {children}
      </DrawerBody>
      {footer
        ? footer
        : (footerExtra || primaryAction || secondaryAction) && (
            <DrawerFooter>
              <Stack direction='column' gap='m' style={{ width: '100%' }}>
                {primaryAction && (
                  <Button
                    {...primaryAction}
                    isFullWidth
                    size='large'
                    onPress={primaryAction.onAction}
                  >
                    {primaryAction?.content}
                  </Button>
                )}
                {secondaryAction && (
                  <Button
                    {...secondaryAction}
                    isFullWidth
                    size='large'
                    variant='outlined'
                    color='default'
                    onPress={secondaryAction.onAction}
                  >
                    {secondaryAction?.content}
                  </Button>
                )}
                {footerExtra}
              </Stack>
            </DrawerFooter>
          )}
    </Drawer>
  );
};

export default Sheet;
