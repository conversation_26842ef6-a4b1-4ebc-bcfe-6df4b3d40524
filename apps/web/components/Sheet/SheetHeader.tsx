import { IconButton, IconSource, Stack, Typography } from '@aftership/astra';
import { ChevronLeftOutlined, CloseOutlined } from '@aftership/astra-icons';

export interface SheetHeaderProps {
  /**
   * @description Title of the sheet, to be rendered in the center of header
   */
  title?: string;
  /**
   * @default undefined
   * @description optional, render on left action
   */
  leadingAction?: {
    icon: IconSource;
    onPress: () => void;
  };
  /**
   * @default true
   * @description whether to show close button
   */
  isClosable?: boolean;
  onClose?: () => void;
  wrapperStyle?: React.CSSProperties;
}

const SheetHeader = ({
  wrapperStyle,
  title,
  leadingAction,
  onClose,
  isClosable,
}: SheetHeaderProps) => {
  const { icon, onPress } = leadingAction || {};
  return (
    <Stack align='center' justify='space-between' style={wrapperStyle}>
      <Stack style={icon ? {} : { height: 0, overflow: 'hidden' }}>
        <IconButton
          isDisabled={!icon}
          icon={icon || ChevronLeftOutlined}
          size='large'
          onPress={onPress}
        />
      </Stack>
      <Typography variant='heading2Xs'>{title}</Typography>
      <Stack style={isClosable ? {} : { height: 0, overflow: 'hidden' }}>
        <IconButton
          size='large'
          // 非受控模式下，设置 slot='close' 来触发关闭
          slot='close'
          icon={CloseOutlined}
          isDisabled={!isClosable}
          onPress={onClose}
        />
      </Stack>
    </Stack>
  );
};

export default SheetHeader;
