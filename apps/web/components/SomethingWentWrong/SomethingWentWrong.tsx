import React, { useMemo } from 'react';
import { useTranslation } from 'react-i18next';

import { Stack, StackProps, Typography } from '@aftership/astra';

interface SomethingWentWrongProps extends StackProps {
  type?: 'resolution' | 'return-method' | 'request-reutrn';
}

const SomethingWentWrong = ({ type, ...props }: SomethingWentWrongProps) => {
  const { t } = useTranslation();
  const { title, description } = useMemo(() => {
    switch (type) {
      case 'resolution':
        return {
          title: t('page.description.somethingWentWrong'),
          description: `${t('page.description.noAvailableResolution')} ${t(
            'page.description.contactTheStore',
          )}`,
        };
      case 'return-method':
        return {
          title: t('page.description.somethingWentWrong'),
          description: `${t('page.description.noAvailableReturnMethod')} ${t(
            'page.description.contactTheStore',
          )}`,
        };
      case 'request-reutrn':
        return {
          title: t('page.description.somethingWentWrong'),
          description: `${t('page.description.refreshPage')}`,
        };
      default:
        return {
          title: t('page.description.somethingWentWrong'),
          description: t('page.description.contactTheStore'),
        };
    }
  }, [t, type]);

  return (
    <Stack
      flex={1}
      direction='column'
      align='center'
      justify='center'
      gap='s'
      style={{ alignSelf: 'center' }}
      {...props}
    >
      <Typography variant='headingXs' color='primary'>
        {title}
      </Typography>
      <Typography variant='bodySm' color='secondary'>
        {description}
      </Typography>
    </Stack>
  );
};

export default SomethingWentWrong;
