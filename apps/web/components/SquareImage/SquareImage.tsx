/* eslint-disable @next/next/no-img-element */
import { Box, BoxProps } from '@aftership/astra';

import { squareImageClassName } from './SquareImage.css';

interface SquareImageProps extends BoxProps {
  src?: string;
  width?: string | number;
}

const SquareImage = ({ src, width = '100%', ...props }: SquareImageProps) => {
  return (
    <Box
      width={width}
      paddingTop={width}
      height={0}
      position='relative'
      overflow='hidden'
      {...props}
    >
      <img src={src} className={squareImageClassName} alt={src} />
    </Box>
  );
};

export default SquareImage;
