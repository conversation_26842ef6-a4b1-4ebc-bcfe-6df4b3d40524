import { style } from '@vanilla-extract/css';
import { recipe } from '@vanilla-extract/recipes';

export const cardRecipe = recipe({
  base: {
    display: 'flex',
    flexDirection: 'column',
    padding: 0,
    // safari 滚动时会导致 padding 0 失效，进而 fallback 到默认的 24px
    paddingBlock: 0,
    paddingInline: 0,
    border: 'none',
  },

  variants: {
    isFullWidth: {
      true: {},
      false: {
        boxShadow: `0px 16px 32px 0px #00000008,
                0px 8px 16px 0px #00000005,
                0px 0px 16px 0px #00000005`,
      },
    },
  },
});

export const mobileBodyContainer = style({
  display: 'flex',
  flexDirection: 'column',
  flex: 1,
  flexBasis: 0,
  overflow: 'auto',
});
