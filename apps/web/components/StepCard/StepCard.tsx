import React, { FC, PropsWithChildren } from 'react';

import { Box, IconButton, Stack } from '@aftership/astra';
import { ChevronLeftOutlined } from '@aftership/astra-icons';
import { semanticTokenVar } from '@aftership/astra-tokens/semantic-token/Contract.css';

import { Card, CardProps } from '@/components/Card';
import { Header } from '@/components/Header';
import useDevice from '@/hooks/useDevice';

import { cardRecipe, mobileBodyContainer } from './StepCard.css';

export interface Props
  extends Omit<
    PropsWithChildren<CardProps>,
    | 'padding'
    | 'paddingX'
    | 'paddingY'
    | 'paddingTop'
    | 'paddingBottom'
    | 'paddingStart'
    | 'paddingEnd'
  > {
  title: React.ReactNode;
  onBack?: VoidFunction;
  backgroundColor?: string;
  headerContainerAlignItems?: 'center' | 'start';
  rightSlot?: React.ReactNode;
  hiddenHeader?: boolean;
  width?: string | number;
  height?: string | number | string;
  isFullWidth?: boolean;
}

const { Color, Space } = semanticTokenVar;

const StepCard: FC<Props> = ({
  onBack,
  title,
  rightSlot,
  children: body,
  headerContainerAlignItems = 'center',
  width = '1080px',
  isFullWidth,
  height,
  hiddenHeader,
  style,
  ...rest
}) => {
  const isMobile = useDevice().mobile;
  const widthValue = isFullWidth ? '100%' : width;

  if (isMobile) {
    return (
      <Box flex={1} height={'100%'} backgroundColor={Color.Bg.Body}>
        <Stack direction='column' style={{ height: '100%' }}>
          <Box
            style={{
              paddingTop: Space.Xl,
              paddingBottom: Space.M,
              paddingLeft: Space.L,
              paddingRight: Space.L,
            }}
          >
            {!hiddenHeader && (
              <IconButton onPress={() => onBack?.()} icon={ChevronLeftOutlined} size='large' />
            )}
          </Box>
          <Box className={mobileBodyContainer}>{body}</Box>
        </Stack>
      </Box>
    );
  }

  return (
    <Card
      className={cardRecipe({ isFullWidth })}
      style={{ width: widthValue, height, padding: 0, ...style }}
      {...rest}
    >
      {!hiddenHeader && (
        <Box paddingX={Space['3Xl']} paddingTop={Space['2Xl']} paddingBottom={Space.S}>
          <Header
            onBack={onBack}
            title={title}
            rightSlot={rightSlot}
            alignItems={headerContainerAlignItems}
          />
        </Box>
      )}
      {body}
    </Card>
  );
};
export default StepCard;
