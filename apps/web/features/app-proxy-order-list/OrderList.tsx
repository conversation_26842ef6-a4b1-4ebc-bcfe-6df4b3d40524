import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Box, Stack, Typography } from '@aftership/astra';
import { tokenVars } from '@aftership/astra-tokens/Contract.css';

import { Card } from '@/components/Card';
import useDevice from '@/hooks/useDevice';
import { useStepCardMinHeight } from '@/hooks/useStepCardMinHeight';
import { PageId } from '@/utils/tracker/consts';
import { useReportPageViewEvent } from '@/utils/tracker/useReportPageViewEvent';

import OrderItem from './components/OrderItem';
import PagingStepper from './components/PagingStepper';
import Policy from './components/Policy';
import { orderListContainer } from './style.css';
import { Order } from './types';

import { useReturns } from '../returns/hooks/useReturns';

export function convertShopifyShopLangCode(code: string, i18nLangCodes: string[]) {
  if (i18nLangCodes.includes(code)) return code;
  const language = i18nLangCodes.find((lang) => {
    return lang.slice(0, 2) === code.slice(0, 2);
  });
  if (language?.includes('zh')) return 'zh-Hant';
  if (language?.includes('pt')) return 'pt-PT';
  return language;
}

const OrderList = (props: { orders: Order[] }) => {
  const { orders } = props;
  const { t, i18n } = useTranslation();
  const {
    policy_url: policyUrl,
    policy_text: policyText,
    external_return_policy_page: externalReturnPolicyPage,
    languages,
  } = useReturns()?.shopInfo ?? {};

  const minHeight = useStepCardMinHeight();

  const isMobile = useDevice().mobile;

  const pageSize = 10;
  const pageNumber = Math.ceil(orders.length / pageSize);
  const [pageIndex, setPageIndex] = useState(0);

  const pageOrders = orders.slice(pageIndex * pageSize, (pageIndex + 1) * pageSize);

  // 上一页
  const onPrevious = () => {
    setPageIndex(pageIndex - 1);
  };

  // 下一页
  const onNext = () => {
    setPageIndex(pageIndex + 1);
  };

  useEffect(() => {
    const langCode = convertShopifyShopLangCode(window.customerLangCode, languages ?? []);
    if (langCode && langCode !== i18n.language) {
      i18n.changeLanguage(langCode);
    }
  }, [i18n, languages]);

  useReportPageViewEvent(PageId.appProxyOrderList);

  const content = (
    <Stack direction='column' flex={1}>
      <Typography
        variant='headingSm'
        textAlign='center'
        style={{
          paddingBottom: tokenVars.Semantic.Space['Xl'],
          color: tokenVars.Semantic.Color.Text.Primary,
        }}
      >
        {t('page.landing.title')}
      </Typography>
      <Policy {...{ policyUrl, policyText, externalReturnPolicyPage }} />
      <Stack
        direction='column'
        gap='xs'
        flex={1}
        style={{ paddingTop: tokenVars.Semantic.Space['3Xl'] }}
      >
        <Typography variant='heading2Xs' style={{ color: tokenVars.Semantic.Color.Text.Primary }}>
          {t('page.description.yourOrders')}
        </Typography>
        <Stack direction='column' gap='2xs'>
          {pageOrders.map((order) => (
            <OrderItem key={order.order_number} {...{ order }} />
          ))}
        </Stack>
      </Stack>
      {orders.length > pageSize && (
        <PagingStepper
          isPreviousDisabled={pageIndex <= 0}
          isNextDisabled={pageIndex >= pageNumber - 1}
          {...{ onPrevious, onNext }}
        />
      )}
    </Stack>
  );

  if (isMobile) {
    return (
      <Box
        flex={1}
        backgroundColor={tokenVars.Semantic.Color.Bg.Body}
        padding={tokenVars.Semantic.Space['M']}
      >
        {content}
      </Box>
    );
  }

  return (
    <Card
      paddingX={tokenVars.Semantic.Space['3Xl']}
      paddingY={tokenVars.Semantic.Space['2Xl']}
      className={orderListContainer}
      style={{ width: 800, minHeight }}
    >
      {content}
    </Card>
  );
};

export default OrderList;
