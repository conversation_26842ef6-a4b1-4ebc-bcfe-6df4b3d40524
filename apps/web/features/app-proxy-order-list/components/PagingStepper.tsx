import React from 'react';

import { Icon, Stack } from '@aftership/astra';
import { ChevronLeftOutlined, ChevronRightOutlined } from '@aftership/astra-icons';
import { tokenVars } from '@aftership/astra-tokens/Contract.css';

import { Button } from '@/components/Button';

export interface PagingStepperProps {
  isPreviousDisabled: boolean;
  isNextDisabled: boolean;
  onPrevious: () => void;
  onNext: () => void;
}

const PagingStepper = (props: PagingStepperProps) => {
  const { isPreviousDisabled, isNextDisabled, onPrevious, onNext } = props;

  return (
    <Stack gap='3xl' style={{ alignSelf: 'center', paddingTop: tokenVars.Semantic.Space['3Xl'] }}>
      <Button isDisabled={isPreviousDisabled} onPress={onPrevious} variant='plain'>
        <Icon
          source={ChevronLeftOutlined}
          style={{
            color: isPreviousDisabled
              ? tokenVars.Primitive.Color['Gray']['700']
              : tokenVars.Primitive.Color['Gray']['1200'],
          }}
        />
      </Button>

      <Button isDisabled={isNextDisabled} onPress={onNext} variant='plain'>
        <Icon
          source={ChevronRightOutlined}
          style={{
            color: isNextDisabled
              ? tokenVars.Primitive.Color['Gray']['700']
              : tokenVars.Primitive.Color['Gray']['1200'],
          }}
        />
      </Button>
    </Stack>
  );
};

export default PagingStepper;
