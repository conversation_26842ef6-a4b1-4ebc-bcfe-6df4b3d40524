import React, { <PERSON> } from 'react';
import { useTranslation } from 'react-i18next';

import { Link, Stack, Typography } from '@aftership/astra';

import { useGetPrefiedLink } from '@/features/returns/hooks/useUniversalRouting';

interface Props {
  policyUrl?: string;
  policyText?: string;
  externalReturnPolicyPage?: boolean;
}

const Policy: FC<Props> = ({ policyUrl, policyText, externalReturnPolicyPage }) => {
  const { t } = useTranslation();
  const privatePolicyLink = useGetPrefiedLink('/return-policy'); // 兼顾 app proxy 模式

  return (
    <Stack direction='column'>
      <Typography variant='heading2Xs' color='primary'>
        {t('page.description.returnPolicy')}
      </Typography>
      <Typography variant='bodyMd' as='p' color='primary'>
        {t('page.description.acceptReturnsPolicy', {
          rawValue: policyText,
          defaultValue: policyText,
        })}{' '}
        {externalReturnPolicyPage ? (
          policyUrl && (
            <Link href={policyUrl} target='_blank' showUnderline={false}>
              <Typography variant='bodyMd' style={{ textDecoration: 'underline' }}>
                {t('page.landing.viewFullPolicy')}
              </Typography>
            </Link>
          )
        ) : (
          <Link href={privatePolicyLink} target='_blank' showUnderline={false}>
            <Typography variant='bodyMd' style={{ textDecoration: 'underline' }}>
              {t('page.landing.viewFullPolicy')}
            </Typography>
          </Link>
        )}
      </Typography>
    </Stack>
  );
};

export default Policy;
