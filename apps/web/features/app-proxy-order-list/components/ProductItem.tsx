import React from 'react';

import { Box, Stack, Typography } from '@aftership/astra';
import { tokenVars } from '@aftership/astra-tokens/Contract.css';

import { EllipsisText } from '@/components/EllipsisText';
import { ImageWithFallback } from '@/components/ImageWithFallback';
import { toCurrency } from '@/utils/price';

import { Product } from '../types';

const defaultTitle = 'Default Title';

const ProductItem = (props: { product: Product }) => {
  const { product } = props;
  const name = product.name.trim();
  const variants = product.variants.filter((value) => value !== defaultTitle).join('·');

  return (
    <Stack style={{ width: '100%' }}>
      <Box borderRadius={tokenVars.Semantic.Space['2Xs']} overflow='hidden'>
        <ImageWithFallback
          usingShopifyPreview
          alt='product image'
          width={80}
          height={80}
          src={product.image_url}
        />
      </Box>
      <Stack direction='column' flex={1} style={{ marginInlineStart: 16 }}>
        <EllipsisText text={name} maxLine={1} variant='bodyLgSemibold' />
        <EllipsisText text={variants} maxLine={1} variant='bodyMd' color='secondary' />
        <Typography
          variant='bodyMd'
          color='primary'
          style={{ paddingTop: tokenVars.Semantic.Space['2Xs'] }}
        >
          {toCurrency(product.price, product.currency)} x {product.number}
        </Typography>
      </Stack>
    </Stack>
  );
};

export default ProductItem;
