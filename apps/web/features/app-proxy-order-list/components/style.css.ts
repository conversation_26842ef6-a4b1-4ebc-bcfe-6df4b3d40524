import { style } from '@vanilla-extract/css';

import { tokenVars } from '@aftership/astra-tokens/Contract.css';

export const orderItemWrapper = style({
  borderWidth: tokenVars.Primitive.Stroke_Width['25'],
  borderRadius: tokenVars.Semantic.Radius['L'],
  borderStyle: 'solid',
  borderColor: tokenVars.Primitive.Color.Gray['400'],
});

export const orderItemHeader = style({
  height: '72px',
  padding: '0 24px',
  borderTop: 0,
  borderRight: 0,
  borderLeft: 0,
  borderBottom:
    tokenVars.Primitive.Stroke_Width['25'] + ' solid ' + tokenVars.Primitive.Color.Gray['400'],
});
