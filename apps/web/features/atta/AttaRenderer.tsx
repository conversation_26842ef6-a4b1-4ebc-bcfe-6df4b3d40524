import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { StyleSheetManager } from 'styled-components';

import { ClientRenderer, RuntimeProvider } from '@aftership/atta-engine/renderer';

import useDevice from '@/hooks/useDevice';

import { AttaPreviewer } from './AttaPreviewer';
import { loader } from './component-loader/loader';
import { RemoteLoaderProvider } from './component-loader/provider';
import { AttaProps } from './type';

import { useSyncXStateAndRoute } from '../returns/hooks/useSyncXStateAndRoute';

const emptyLoader = async () => () => null;

const Renderer: React.FC<{
  attaProps: {
    preview: false;
    code: string;
    deps: string;
  };
  renderProps: any;
}> = ({ attaProps, renderProps }) => {
  return (
    <StyleSheetManager>
      <RemoteLoaderProvider>
        <RuntimeProvider
          code={attaProps.code}
          deps={attaProps.deps}
          loader={emptyLoader}
          syncLoader={loader}
        >
          <ClientRenderer lazy={false} renderProps={renderProps}></ClientRenderer>
        </RuntimeProvider>
      </RemoteLoaderProvider>
    </StyleSheetManager>
  );
};

export const AttaRenderer: React.FC<{ attaProps: AttaProps; pageProps: any }> = ({
  attaProps,
  pageProps,
}) => {
  useSyncXStateAndRoute();

  const { t } = useTranslation();
  const text = t('page.widget.helpWidget.buttonText');

  const isMobile = useDevice().mobile;

  const renderProps = useMemo(() => {
    const renderContext = {
      isMobile,
      helpWidgetText: text,
    };
    return {
      ...pageProps,
      renderContext,
    };
  }, [pageProps, isMobile, text]);

  if (attaProps.preview) {
    return <AttaPreviewer schema={attaProps.schema} renderProps={renderProps}></AttaPreviewer>;
  }

  return <Renderer attaProps={attaProps} renderProps={renderProps} />;
};
