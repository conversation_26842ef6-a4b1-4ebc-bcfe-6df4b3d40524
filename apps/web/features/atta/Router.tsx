import { useRouter } from 'next/router';
import { useMemo } from 'react';

import { ComponentName, PathName } from './type';

type RouterProps = Record<ComponentName, React.ReactNode>;

/**
 * next 路由与渲染组件的映射
 */
const routerPathMap: Record<PathName, ComponentName> = {
  [PathName.OrderLookup]: ComponentName.OrderLookup,
  [PathName.RequestReturns]: ComponentName.ItemSelection,
  [PathName.ReturnResolution]: ComponentName.Resolution,
  [PathName.ReturnMethod]: ComponentName.ReturnMethod,
  [PathName.Review]: ComponentName.Review,
  [PathName.ReturnList]: ComponentName.ReturnList,
  [PathName.GiftReturn]: ComponentName.GiftReturn,
  [PathName.ReturnPolicy]: ComponentName.ReturnPolicy,
  [PathName.MissingShippingAddress]: ComponentName.MissingAddress,
  [PathName.ReturnDetail]: ComponentName.ReturnDetail,
};

/**
 * 根据 next 路由，渲染对应的组件
 *
 * 在使用 Atta 渲染时，无论是进入哪个 next 路由，都会渲染同一个 Atta Renderer 和同一份 schema，
 * 所以为了能够根据实际的 next 路由渲染出对应的组件，需要有一个 Router，可以动态的根据 next 路由渲染对应组件，
 * 组件与路由的对应关系维护在 routerPathMap 中。
 */
export const Router: React.FC<RouterProps> = (props) => {
  const router = useRouter();

  const children = useMemo(() => {
    const pathName = router.pathname as PathName;
    const name = routerPathMap[pathName];
    return props[name];
  }, [props, router.pathname]);

  return <>{children}</>;
};
