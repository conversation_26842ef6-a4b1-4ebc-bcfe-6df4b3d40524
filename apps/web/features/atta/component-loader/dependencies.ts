// systemjs 是以 module.default 的形式去加载的，所以需要使用 * as any 的形式去加载
import * as React from 'react';
import * as ReactDOM from 'react-dom';
import * as ReactJSXRuntime from 'react/jsx-runtime';
import * as StyledComponents from 'styled-components';

import * as AttaEngineRenderer from '@aftership/atta-engine/renderer';

export const dependencies = [
  {
    name: 'react',
    module: React,
  },
  {
    name: 'react-dom',
    module: ReactDOM,
  },
  {
    name: 'react/jsx-runtime',
    module: ReactJSXRuntime,
  },
  {
    name: 'styled-components',
    module: StyledComponents,
  },
  {
    name: '@aftership/atta-engine/renderer',
    module: AttaEngineRenderer,
  },
];
