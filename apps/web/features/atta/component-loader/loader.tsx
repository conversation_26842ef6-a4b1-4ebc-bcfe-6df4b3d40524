import { GiftReturnPage } from '@/features/gift-return/page';
import { OrderLookupPage } from '@/features/order-lookup/page';
import { RequestReturnsPage } from '@/features/request-returns/page';
import { ReturnResolutionPage } from '@/features/resolution/page';
import ReturnDetail from '@/features/return-detail';
import ReturnList from '@/features/return-list';
import PrivacyPolicy from '@/features/return-policy/components/PrivacyPolicy';
import { Container, Footer, Header, Layout } from '@/features/returns/components/ReturnsPage';
import { ReviewPage } from '@/features/review/page';

import { RemoteComponent } from './RemoteComponent';

import { MissingShippingAddressPage } from '../../missing-shipping-address/page';
import { ReturnMethodPage } from '../../return-method/page';
import { Router } from '../Router';
import { WidgetContainer } from '../WidgetContainer';
import { ComponentName } from '../type';

export const components: Record<ComponentName, React.FC<any>> = {
  [ComponentName.Router]: Router,
  [ComponentName.ReturnsPageLayout]: Layout,
  [ComponentName.ReturnsPageContainer]: Container,
  [ComponentName.ReturnsPageHeader]: Header,
  [ComponentName.ReturnsPageFooter]: Footer,
  [ComponentName.WidgetContainer]: WidgetContainer,
  [ComponentName.OrderLookup]: OrderLookupPage,
  [ComponentName.ReturnMethod]: ReturnMethodPage,
  [ComponentName.ReturnList]: ReturnList,
  [ComponentName.Review]: ReviewPage,
  [ComponentName.Resolution]: ReturnResolutionPage,
  [ComponentName.ItemSelection]: RequestReturnsPage,
  [ComponentName.GiftReturn]: GiftReturnPage,
  [ComponentName.ReturnPolicy]: PrivacyPolicy,
  [ComponentName.MissingAddress]: MissingShippingAddressPage,
  [ComponentName.ReturnDetail]: ReturnDetail,
};

export const loader = (type: string, name: string) => {
  if (components[name as ComponentName]) {
    return components[name as ComponentName];
  } else {
    const Comp = (props: any) => <RemoteComponent name={name} {...props} />;
    return Comp;
  }
};
