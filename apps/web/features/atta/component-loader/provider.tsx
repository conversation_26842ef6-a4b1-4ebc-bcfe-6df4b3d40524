import { createContext, useContext } from 'react';

import { ComponentLoader } from '@aftership/atta-engine/loader';

import { useRemoteComponentLoader } from './useRemoteComponentLoader';

export const remoteLoaderContext = createContext<ComponentLoader | null>(null);

export const RemoteLoaderProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const loader = useRemoteComponentLoader();

  return (
    <remoteLoaderContext.Provider value={loader || null}>{children}</remoteLoaderContext.Provider>
  );
};

export const useRemoteLoader = () => {
  const loader = useContext(remoteLoaderContext);
  return loader;
};
