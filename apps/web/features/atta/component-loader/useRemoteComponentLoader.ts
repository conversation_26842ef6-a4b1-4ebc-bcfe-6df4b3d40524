import { useEffect, useState } from 'react';

import { ComponentLoader } from '@aftership/atta-engine/loader';

import { dependencies } from './dependencies';

const getAssetId = (env: string) => {
  switch (env) {
    case 'staging':
      return '64f7afe2e9444fa9b1f9b6381976738b';

    case 'production':
      return '64f7afe2e9444fa9b1f9b6381976738b';

    default:
      return '6ad5fb4f4680430eb033831ccaf773ff';
  }
};

const getComponentLibUrl = (env: string): string => {
  const assetId = getAssetId(env);
  switch (env) {
    case 'staging':
      return 'https://api.automizely.com/icecube/v1/assets/public/' + assetId;

    case 'production':
      return 'https://api.automizely.com/icecube/v1/assets/public/' + assetId;

    default:
      return 'https://api.automizely.io/icecube/v1/assets/public/' + assetId;
  }
};

const componentUrl = getComponentLibUrl(process.env.APP_ENV || 'development');

export const useRemoteComponentLoader = () => {
  const [loader, setLoader] = useState<ComponentLoader | undefined>(undefined);

  useEffect(() => {
    const loader = new ComponentLoader(componentUrl, dependencies);
    loader.load().then(() => {
      setLoader(loader);
    });
  }, []);

  return loader;
};
