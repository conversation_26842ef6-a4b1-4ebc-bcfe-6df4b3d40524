import dayjs from 'dayjs';

import { compileSync } from '@aftership/atta-engine/renderer';

import { loader } from './component-loader/loader';
import { getSchema, getSchemaObject } from './schema';
import { AttaProps } from './type';

type SharedConfigs = {
  whitelist: 'after_created_at' | 'all' | 'none';
  after_created_at: string;
};

export const checkAttaEditorEnable = (createdAt: string, sharedConfigs: SharedConfigs): boolean => {
  const { whitelist, after_created_at } = sharedConfigs;

  if (whitelist === 'all') {
    return true;
  } else if (whitelist === 'none') {
    return false;
  } else if (whitelist === 'after_created_at') {
    return dayjs(createdAt).isAfter(dayjs(after_created_at));
  }

  return false;
};

/**
 * 获取服务端 Atta 配置属性
 * @param isPreview 是否为预览模式
 * @param widgetSchemas 需要挂载的插件 schemas 数组
 * @returns AttaProps 配置对象
 */
export const getServerSideAttaProps = async (
  isPreview: boolean,
  widgetSchemas: (string | null)[],
): Promise<AttaProps | null> => {
  let schema = getSchema();

  // 处理需要挂载的插件 schemas
  const validWidgetSchemas = widgetSchemas.filter(Boolean); // 过滤掉 null/undefined

  if (validWidgetSchemas.length > 0) {
    const schemaObj = getSchemaObject();
    const container = schemaObj.$elements.find((item: any) => item.$element === 'WidgetContainer');

    if (container) {
      // 处理每个 widget schema
      validWidgetSchemas.forEach((widgetSchemaStr) => {
        try {
          const widgetSchemaObj = JSON.parse(widgetSchemaStr as string);
          const el = widgetSchemaObj?.$elements;
          if (el) {
            container.$children.push(el);
          }
        } catch (error) {
          console.error('Failed to parse widget schema:', widgetSchemaStr, error);
        }
      });

      schema = JSON.stringify(schemaObj);
    }
  }

  let code = '';
  let deps = '';

  if (!isPreview) {
    const { code: c, deps: d } = compileSync(schema, loader);
    code = c;
    deps = d;
  }

  return isPreview
    ? {
        preview: true,
        schema,
      }
    : {
        preview: false,
        code,
        deps,
      };
};
