/* eslint-disable @typescript-eslint/naming-convention */
const schema = {
  $meta: {
    theme: {},
  },
  $elements: [
    {
      $element: 'ReturnsPageLayout',
      $children: [
        {
          $element: 'ReturnsPageHeader',
        },
        {
          $element: 'ReturnsPageContainer',
          $children: [
            {
              $element: 'Router',
              $props: {
                OrderLookup: {
                  $element: 'OrderLookup',
                },
                ItemSelection: {
                  $element: 'ItemSelection',
                },
                Resolution: {
                  $element: 'Resolution',
                },
                ReturnMethod: {
                  $element: 'ReturnMethod',
                },
                Review: {
                  $element: 'Review',
                },
                ReturnList: {
                  $element: 'ReturnList',
                },
                GiftReturn: {
                  $element: 'GiftReturn',
                },
                ReturnPolicy: {
                  $element: 'ReturnPolicy',
                },
                ReturnDetail: {
                  $element: 'ReturnDetail',
                },
                MissingAddress: {
                  $element: 'MissingAddress',
                },
              },
            },
          ],
        },
        {
          $element: 'ReturnsPageFooter',
        },
      ],
    },
    {
      $element: 'WidgetContainer',
      $children: [],
    },
  ],
};

const schemaString = JSON.stringify(schema);

export function getSchema() {
  return schemaString;
}

export function getSchemaObject(): typeof schema {
  return JSON.parse(schemaString);
}
