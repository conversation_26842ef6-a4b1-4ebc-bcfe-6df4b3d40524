export type AttaProps =
  | {
      preview: true;
      schema: string;
    }
  | {
      preview: false;
      code: string;
      deps: string;
    };

export enum PathName {
  OrderLookup = '/',
  MissingShippingAddress = '/missing-address',
  RequestReturns = '/request-returns',
  ReturnResolution = '/return-resolution',
  ReturnMethod = '/return-method',
  Review = '/review',
  ReturnList = '/return-list',
  GiftReturn = '/gift-return',
  ReturnPolicy = '/return-policy',
  ReturnDetail = '/return-detail/[rmaId]',
}

export enum ComponentName {
  Router = 'Router',
  ReturnsPageLayout = 'ReturnsPageLayout',
  ReturnsPageContainer = 'ReturnsPageContainer',
  ReturnsPageHeader = 'ReturnsPageHeader',
  ReturnsPageFooter = 'ReturnsPageFooter',
  WidgetContainer = 'WidgetContainer',
  OrderLookup = 'OrderLookup',
  ItemSelection = 'ItemSelection',
  Resolution = 'Resolution',
  ReturnMethod = 'ReturnMethod',
  Review = 'Review',
  ReturnList = 'ReturnList',
  GiftReturn = 'GiftReturn',
  ReturnPolicy = 'ReturnPolicy',
  ReturnDetail = 'ReturnDetail',
  MissingAddress = 'MissingAddress',
}
