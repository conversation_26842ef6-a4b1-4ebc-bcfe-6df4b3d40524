import { uniqueId } from 'lodash-es';
import { useEffect } from 'react';

import { useUpdateItemsRect } from '@aftership/atta-engine/renderer';

import { useIsAttaEditor } from '@/features/returns/hooks/useIsAttaEditor';

export const useScrollUpdate = () => {
  const isAttaEditor = useIsAttaEditor();
  const updateHandler = useUpdateItemsRect();
  const className = uniqueId('atta-scroll-container-');
  useEffect(() => {
    if (!isAttaEditor) return;
    const dom = document.querySelector('.' + className);
    if (dom) dom.addEventListener('scroll', updateHandler);
    return () => {
      if (dom) dom.removeEventListener('scroll', updateHandler);
    };
  });
  return className;
};
