import { useTranslation } from 'react-i18next';

import { Stack, Typography } from '@aftership/astra';

import { Button } from '@/components/Button';

import { CartList } from './components/CartList';
import { Summary } from './components/Summary';
import { useExchangeInAppSubFlow } from './hooks/useExchangeInAppSubFlow';

interface CartProps {
  onAddMore?: () => void;
}

const Cart = ({ onAddMore }: CartProps) => {
  const { t } = useTranslation();
  const { context, dispatch } = useExchangeInAppSubFlow() || {};
  const { exchangeItems = [] } = context || {};
  const isEmpty = exchangeItems.length <= 0;

  const handleNext = () => {
    dispatch?.({ type: 'GO_NEXT' });
  };
  const handleAddMore = () => {
    onAddMore?.();
  };

  if (isEmpty) {
    return (
      <Stack
        direction='column'
        align='center'
        justify='center'
        gap='m'
        style={{
          width: '100%',
          height: '100%',
        }}
      >
        <Typography variant='heading2Xs'>{t('page.description.yourCartEmpty')}</Typography>
        <Button onPress={handleAddMore}>{t('page.request.addMore')}</Button>
      </Stack>
    );
  }

  return (
    <Stack flex={1} direction='column' style={{ height: 0 }}>
      <CartList />
      <Summary onSubmit={handleNext} />
    </Stack>
  );
};

export default Cart;
