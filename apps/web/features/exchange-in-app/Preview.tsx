import { useLayoutEffect } from 'react';

import { Box, Skeleton, Stack, Typography } from '@aftership/astra';
import { tokenVars } from '@aftership/astra-tokens/Contract.css';

import { widgetEndpoint } from '@/config/endPoints';

const BestSellerList = [1, 2, 3];

const Preview = () => {
  useLayoutEffect(() => {
    const script = document.createElement('script');
    script.src = `${widgetEndpoint}/preview/main.js`;
    script.async = true;
    document.head.appendChild(script);

    return () => {
      const banner = document.querySelector('credit-banner');
      banner?.parentNode?.removeChild(banner);
      script.parentNode?.removeChild(script);
    };
  }, []);

  return (
    <Stack direction='column' style={{ height: '100%' }}>
      <Box padding={tokenVars.Semantic.Space.L} backgroundColor={tokenVars.Semantic.Color.Bg.Body}>
        <Stack gap='m'>
          <Skeleton variant='rounded' width={100} height={24} />
          <Box flex={1} />
          <Skeleton variant='rounded' width={100} height={24} />
          <Skeleton variant='rounded' width={100} height={24} />
          <Skeleton variant='rounded' width={100} height={24} />
        </Stack>
      </Box>
      <Box flex={1} backgroundColor={tokenVars.Semantic.Color.Bg.Body}>
        <Box padding={`50px 78px`} backgroundColor='#ebebeb'>
          <Stack gap='2xl'>
            <Skeleton variant='rounded' height={260} style={{ flex: 4 }} />
            <Stack flex={3} direction='column' gap='m'>
              <Skeleton variant='rounded' height={24} />
              <Skeleton variant='rounded' height={24} />
              <Skeleton variant='rounded' height={24} />
              <Stack gap='m'>
                <Skeleton variant='rounded' width={100} height={48} />
                <Skeleton variant='rounded' width={100} height={48} />
              </Stack>
            </Stack>
          </Stack>
        </Box>
        <Box padding={`30px 78px`}>
          <Stack direction='column' gap='m'>
            <Typography variant='headingSm'>Best seller</Typography>
            <Stack gap='2xl'>
              {BestSellerList.map((item) => (
                <Stack key={item} flex={1} direction='column' gap='s'>
                  <Skeleton variant='rounded' height={160} />
                  <Skeleton variant='rounded' height={24} />
                  <Skeleton variant='rounded' width='25%' height={24} />
                </Stack>
              ))}
            </Stack>
          </Stack>
        </Box>
      </Box>
    </Stack>
  );
};

export default Preview;
