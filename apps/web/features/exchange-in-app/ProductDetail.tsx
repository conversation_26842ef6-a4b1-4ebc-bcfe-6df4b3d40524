import { useCallback, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import {
  Box,
  IconButton,
  NumberField,
  Stack,
  Typography,
  useBreakpointValue,
} from '@aftership/astra';
import { ChevronLeftOutlined } from '@aftership/astra-icons';
import { tokenVars } from '@aftership/astra-tokens/Contract.css';
import { IExchangeItem, Variant, VariantOption } from '@aftership/returns-logics-core';

import { Button } from '@/components/Button';
import { Carousel } from '@/components/Carousel';
import { Divider } from '@/components/Divider';
import { LoadingWrapper } from '@/components/LoadingWrapper';
import { SquareImage } from '@/components/SquareImage';
import { useExchangeInAppSubFlow } from '@/features/exchange-in-app/hooks/useExchangeInAppSubFlow';
import { draftCheckoutItemsAdaptor } from '@/features/exchange-in-app/utils/adaptor';
import { INIT_QUANTITY } from '@/features/exchange-in-app/utils/constants';
import { evaluatePriceByVariant, findSkuByOption } from '@/features/exchange-in-app/utils/variant';
import { VariantList } from '@/features/return-replacement/components/VariantList';
import { xStateMetaData } from '@/features/returns/hooks/useSyncXStateAndRoute';
import { useUniversalRouting } from '@/features/returns/hooks/useUniversalRouting';
import { useAvailableQuantity } from '@/hooks/useAvailableQuantity';
import useDevice from '@/hooks/useDevice';

const MIN_QUANTITY = 1;

const FixedGoBack = ({ position = 'absolute' }: { position?: 'absolute' | 'fixed' }) => {
  const router = useUniversalRouting();

  return (
    <Box
      zIndex={9}
      position={position}
      left={tokenVars.Semantic.Space.M}
      top={tokenVars.Semantic.Space.M}
    >
      <IconButton
        size='large'
        icon={ChevronLeftOutlined}
        onPress={() => router.navigate({ pathname: '/exchange/products' })}
      />
    </Box>
  );
};

const ProductInfo = ({ title = '', variant = '' }: { title?: string; variant?: string }) => {
  return (
    <Stack direction='column' gap='xs'>
      <Typography variant='heading2Xs' color='primary'>
        {title}
      </Typography>
      <Typography variant='heading2Xs' color='primary'>
        {variant}
      </Typography>
    </Stack>
  );
};

interface QuantityProps {
  max?: number;
  step?: number;
  value?: number | string;
  isDisable?: boolean;
  isLoading?: boolean;
  submitText?: string;
  onChange?: (value: number) => void;
  onSubmit?: () => void;
  isMaxTip?: boolean;
}
const Quantity = ({
  max,
  step = 1,
  value,
  isDisable = false,
  isLoading = false,
  isMaxTip = false,
  submitText = '',
  onChange,
  onSubmit,
}: QuantityProps) => {
  const { t } = useTranslation();
  const isMobile = useBreakpointValue({ base: true, m: false });

  return (
    <Stack direction='column' gap='xs' align='start'>
      <Stack direction='column' gap='m'>
        {isMobile && <Typography variant='heading2Xs'>{t('page.description.quantity')}</Typography>}
        <Stack gap='xl'>
          <NumberField
            variant='outlined'
            size='medium'
            minValue={MIN_QUANTITY}
            maxValue={max}
            step={step}
            value={Number(value)}
            onChange={onChange}
            style={{ width: '180px' }}
          />
          {!isMobile && (
            <Button
              size='large'
              isDisabled={isLoading || isDisable}
              isLoading={isLoading}
              onPress={onSubmit}
            >
              {submitText}
            </Button>
          )}
        </Stack>
      </Stack>
      {isMaxTip && (
        <Typography variant='bodySm'>
          {t('page.error.maximumQuantity', { maximumQuantity: max })}
        </Typography>
      )}
    </Stack>
  );
};

const Description = ({ description = '' }: { description?: string }) => {
  const { t } = useTranslation();
  return (
    <Stack direction='column' gap='m'>
      <Typography variant='heading2Xs'>{t('page.description.productDescription')}</Typography>
      <div dangerouslySetInnerHTML={{ __html: description }} />
    </Stack>
  );
};

const ProductDetail = () => {
  const router = useUniversalRouting();
  const isMobile = useDevice().mobile;
  const productId = router.getParam('id');
  const { t } = useTranslation();
  const { dispatch, context, matches } = useExchangeInAppSubFlow() || {};
  const { productMap = {}, exchangeItems = [] } = context || {};

  const [sku, setSku] = useState<Variant>();
  const [lastValidSku, setLastValidSku] = useState<Variant | undefined>();
  const [quantity, setQuantity] = useState(INIT_QUANTITY);
  const [isMaxTip, setIsMaxTip] = useState(false);
  const [calculatingProductId, setCalculatingProductId] = useState('');
  const [selectedOptions, setSelectedOptions] = useState<VariantOption[]>([]);

  const availableQuantity = useAvailableQuantity(sku);
  const skuMaxNumber: number = useMemo(() => {
    const currCartItem = exchangeItems.find((cart) => cart.variantId === sku?.external_id);
    return currCartItem
      ? currCartItem.availableQuantity - currCartItem.quantity
      : availableQuantity;
  }, [availableQuantity, exchangeItems, sku?.external_id]);
  const max = Math.max(skuMaxNumber, MIN_QUANTITY);

  const product = useMemo(() => productMap[productId], [productMap, productId]);
  const isCalculating = matches ? matches({ exchangeCalculation: 'loading' }) : false;
  const isCalculateSuccess = matches ? matches?.({ exchangeCalculation: 'success' }) : false;
  const isSoldOut = sku && (!product?.lowest_price_variant || skuMaxNumber <= 0);
  const isDisable = isSoldOut || !sku || !skuMaxNumber || !sku?.availability?.available;
  const isLoading = !product;

  // 与 replace 类似的 hidden variant option 效果，需要时开启
  // const productOptionsAfterFilter = getNewAvailableOptionsWhenSelected(
  //   selectedOptions,
  //   product?.options,
  //   (product?.variants || []).map((variant) => ({
  //     ...variant,
  //     productId,
  //     productTitle: product.title,
  //     variantTitle: variant.title,
  //     variantId: variant.external_id,
  //     variantCoverUrl: variant.image_url,
  //     isValid: variant.available,
  //     allowBackorder: variant.allow_backorder,
  //     online_available_quantity: variant.available_quantity,
  //   })),
  // );

  useEffect(() => {
    xStateMetaData.delete('exchangeInApp');
  }, []);
  useEffect(() => {
    if (product) {
      const defaultVariant = product.lowest_price_variant;
      setSelectedOptions(defaultVariant.options);
      setSku(defaultVariant);
      setLastValidSku(defaultVariant);
    }
  }, [product]);
  // 加载 product 数据
  useEffect(() => {
    if (productId) {
      dispatch?.({ type: 'LOAD_PRODUCT_DETAILS', productId });
    }
  }, [dispatch, productId]);
  // add to cart 成功后返回 list 页面
  useEffect(() => {
    if (isCalculateSuccess && calculatingProductId === product?.external_id) {
      router.navigate({
        pathname: '/exchange/products',
        asPath: '/exchange/products?openSheet=true',
      });
    }
  }, [calculatingProductId, isCalculateSuccess, product, router]);

  const handleQuantityChange = useCallback(
    (value: number) => {
      setQuantity(value);
      setIsMaxTip(value > skuMaxNumber);
    },
    [skuMaxNumber],
  );

  const handleSelectedOptions = useCallback(
    (optionName: string, value: string) => {
      const shouldUnSelect = selectedOptions.some(
        (item) => item.name === optionName && item.value === value,
      );
      const options = selectedOptions.filter((item) => item.name !== optionName);
      const newSelectedOptions: VariantOption[] = shouldUnSelect
        ? [...options]
        : [...options, { name: optionName, value }];
      const sku = findSkuByOption(
        product?.variants || [],
        newSelectedOptions,
        product?.options.length,
      );

      setSelectedOptions(newSelectedOptions);
      setSku(sku);
      setQuantity(INIT_QUANTITY);
      sku && setLastValidSku(sku);
    },
    [product, selectedOptions],
  );

  const handleSubmit = useCallback(() => {
    if (sku) {
      setCalculatingProductId(product!.external_id);
      dispatch?.({
        type: 'SET_EXCHANGE_ITEMS',
        data: {
          exchangeItems: draftCheckoutItemsAdaptor(
            {
              productId: product!.external_id,
              variantId: sku!.external_id,
              quantity,
              productTitle: product!.title,
              variantTitle: sku!.title,
              productCoverUrl: sku!.image_url,
              price: sku!.price,
              originPrice: sku!.compare_at_price,
              inventoryQuantity: sku!.online_available_quantity || 0,
              allowBackorder: sku!.allow_backorder,
            },
            // 这里不需要 availableQuantity，因为会重新计算
            // Omit 这个 key 用来修复类型错误
            exchangeItems as Omit<IExchangeItem, 'availableQuantity'>[],
            true,
          ),
        },
      });
    }
  }, [sku, dispatch, product, quantity, exchangeItems]);

  const buttonText = isSoldOut
    ? t('page.request.soldOut')
    : isDisable
      ? t('page.request.variantInvalid')
      : t('page.request.addToCart');

  if (isMobile) {
    return (
      <Box flex={1} height={0} overflow='auto' backgroundColor={tokenVars.Semantic.Color.Bg.Body}>
        <LoadingWrapper isLoading={isLoading} spinnerProps={{ size: 'large' }}>
          {!isLoading && (
            <>
              <FixedGoBack position='fixed' />
              <Carousel
                items={lastValidSku?.image_urls || product.image_urls || []}
                renderItem={(item) => <SquareImage width='100%' src={item} borderRadius='' />}
                style={{ width: '100%' }}
              />
              <Stack
                direction='column'
                gap='2xl'
                style={{
                  paddingInline: tokenVars.Semantic.Space.M,
                  paddingTop: tokenVars.Semantic.Space.M,
                  paddingBottom: tokenVars.Primitive.Size['2400'],
                }}
              >
                <ProductInfo title={product.title} variant={evaluatePriceByVariant(sku)} />
                <Divider spacing={0} />
                <VariantList
                  productOptions={product.options}
                  selectedOptions={selectedOptions}
                  onSelectedOption={handleSelectedOptions}
                />
                <Quantity
                  max={max}
                  value={quantity}
                  isDisable={isSoldOut || isDisable}
                  isLoading={isCalculating}
                  isMaxTip={isMaxTip}
                  submitText={buttonText}
                  onChange={handleQuantityChange}
                  onSubmit={handleSubmit}
                />
                <Description description={product.description} />
              </Stack>

              <Box
                position='fixed'
                bottom={0}
                padding={tokenVars.Semantic.Space.M}
                width='100%'
                backgroundColor={tokenVars.Semantic.Color.Bg.Body}
              >
                <Button
                  isFullWidth
                  size='large'
                  onPress={handleSubmit}
                  isLoading={isCalculating}
                  isDisabled={isSoldOut || isDisable}
                >
                  {buttonText}
                </Button>
              </Box>
            </>
          )}
        </LoadingWrapper>
      </Box>
    );
  }

  return (
    <Box flex={1} backgroundColor='#f5f6f7'>
      <Stack align='center' justify='center' style={{ height: '100%' }}>
        <Box
          position='relative'
          height='100%'
          width={960}
          padding={tokenVars.Primitive.Size['1600']}
          backgroundColor={tokenVars.Semantic.Color.Bg.Body}
        >
          <LoadingWrapper isLoading={isLoading} spinnerProps={{ size: 'large' }}>
            {!isLoading && (
              <>
                <FixedGoBack />

                <Stack direction='column' gap='xl' style={{ height: '100%' }}>
                  <Stack gap='3xl'>
                    <Carousel
                      style={{ width: 360 }}
                      items={lastValidSku?.image_urls || product.image_urls || []}
                      renderItem={(item) => <SquareImage width={360} src={item} />}
                    />
                    <Stack flex={1} direction='column' gap='xl'>
                      <ProductInfo title={product.title} variant={evaluatePriceByVariant(sku)} />
                      <VariantList
                        productOptions={product.options}
                        selectedOptions={selectedOptions}
                        onSelectedOption={handleSelectedOptions}
                      />
                      <Quantity
                        max={max}
                        value={quantity}
                        isDisable={isSoldOut || isDisable}
                        isLoading={isCalculating}
                        isMaxTip={isMaxTip}
                        submitText={buttonText}
                        onChange={handleQuantityChange}
                        onSubmit={handleSubmit}
                      />
                    </Stack>
                  </Stack>
                  <Divider spacing={0} />
                  <Description description={product.description} />
                </Stack>
              </>
            )}
          </LoadingWrapper>
        </Box>
      </Stack>
    </Box>
  );
};

export default ProductDetail;
