import { useEffect } from 'react';
import { useTranslation } from 'react-i18next';

import { Box, Stack } from '@aftership/astra';
import { tokenVars } from '@aftership/astra-tokens/Contract.css';

import { Header } from '@/components/Header';
import useDevice from '@/hooks/useDevice';
import { PageId } from '@/utils/tracker/consts';
import { useReportPageViewEvent } from '@/utils/tracker/useReportPageViewEvent';

import * as styles from './Products.css';
import { CollectionsList } from './components/CollectionsList';
import { ProductsList } from './components/ProductsList';
import { SearchBar } from './components/SearchBar';
import { useExchangeInAppSubFlow } from './hooks/useExchangeInAppSubFlow';

import { useShopInfo } from '../returns/hooks/useShopInfo';
import { xStateMetaData } from '../returns/hooks/useSyncXStateAndRoute';

const Products = () => {
  const { t } = useTranslation();
  const isMobile = useDevice().mobile;
  const exchangeInAppSubFlow = useExchangeInAppSubFlow();
  const shopInfo = useShopInfo();

  const handleOnBack = () => {
    exchangeInAppSubFlow.dispatch({ type: 'GO_BACK' });
  };

  useEffect(() => {
    xStateMetaData.delete('exchangeInApp');
  }, []);
  useEffect(() => {
    if (
      exchangeInAppSubFlow &&
      exchangeInAppSubFlow.matches('beforeInit') &&
      exchangeInAppSubFlow?.context?.products.length === 0
    ) {
      exchangeInAppSubFlow.dispatch({ type: 'INIT' });
    }
  }, [exchangeInAppSubFlow]);

  useReportPageViewEvent(PageId.EFAInApp);

  if (isMobile) {
    return (
      <Box className={styles.mobileContainer}>
        <Box
          backgroundColor={shopInfo.theme_color}
          style={{
            paddingBlock: tokenVars.Semantic.Space.M,
            paddingInline: tokenVars.Semantic.Space.M,
          }}
        >
          <Header onBack={handleOnBack} title={t('page.general.exchangeItems')} />
        </Box>
        <Stack flex={1} direction='column' gap='m' style={{ height: 0 }}>
          <SearchBar />
          <Stack flex={1} direction='column' style={{ height: '0' }}>
            <CollectionsList />
            <ProductsList />
          </Stack>
        </Stack>
      </Box>
    );
  }

  return (
    <Box width='100%' height={0} flex={1} backgroundColor={tokenVars.Semantic.Color.Bg.Body}>
      <Stack flex={1} direction='column' gap='m' style={{ height: '100%' }}>
        <SearchBar />
        <Box flex={1} height={0} paddingX={tokenVars.Semantic.Space.M}>
          <Stack style={{ height: '100%', maxWidth: 1280, margin: 'auto' }}>
            <CollectionsList />
            <ProductsList />
          </Stack>
        </Box>
      </Stack>
    </Box>
  );
};

export default Products;
