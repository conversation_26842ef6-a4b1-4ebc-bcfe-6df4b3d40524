import Image from 'next/image';
import { useCallback, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Box, NumberField, Stack, Typography } from '@aftership/astra';
import { tokenVars } from '@aftership/astra-tokens/Contract.css';
import { IExchangeItem } from '@aftership/returns-logics-core';

import { Button } from '@/components/Button';
import { useExchangeInAppSubFlow } from '@/features/exchange-in-app/hooks/useExchangeInAppSubFlow';
import { draftCheckoutItemsAdaptor } from '@/features/exchange-in-app/utils/adaptor';
import { useDebounceFn } from '@/hooks/useDebounceFn';
import { toCurrency } from '@/utils/price';

interface CartItemProps {
  item: IExchangeItem;
  onDelete?: (fn: VoidFunction) => void;
}

const CartItem = ({ item, onDelete }: CartItemProps) => {
  const { t } = useTranslation();
  const { dispatch, context, matches } = useExchangeInAppSubFlow();
  const { exchangeItems = [] } = context ?? {};
  const { productTitle: title, variantTitle, productCoverUrl, price, availableQuantity } = item;
  const [overMaxQuantity, setOverMaxQuantity] = useState(false);
  const [quantity, setQuantity] = useState(item.quantity);

  const cover = productCoverUrl || require('@/assets/thumbnail.png');
  const isCalculating = matches({ exchangeCalculation: 'loading' });

  useEffect(() => {
    setQuantity(item.quantity);
  }, [item.quantity]);

  const syncQuantity = useDebounceFn(
    useCallback(
      (quantity: number) => {
        dispatch({
          type: 'SET_EXCHANGE_ITEMS',
          data: {
            exchangeItems: draftCheckoutItemsAdaptor({ ...item, quantity }, exchangeItems),
          },
        });
      },
      [dispatch, exchangeItems, item],
    ),
  );
  const handleQuantityChange = useCallback(
    (quantity: number) => {
      setOverMaxQuantity(quantity >= availableQuantity);
      setQuantity(quantity);
      syncQuantity(quantity);
    },
    [syncQuantity, availableQuantity],
  );

  return (
    <Box
      borderRadius={tokenVars.Semantic.Space.M}
      backgroundColor={tokenVars.Semantic.Color.Bg.Body}
    >
      <Stack direction='column'>
        <Stack gap='m' style={{ width: '100%' }}>
          <Image
            width={78}
            height={78}
            src={cover}
            style={{ borderRadius: tokenVars.Semantic.Space.M }}
            alt='cover'
          />
          <Stack direction='column' flex={1}>
            <Typography variant='bodyLgSemibold'>{title}</Typography>
            {variantTitle && <Typography variant='bodyMd'>{variantTitle}</Typography>}
            <Typography variant='bodyLgSemibold'>
              {toCurrency(price?.amount, price?.currency)}
            </Typography>
          </Stack>
          <Stack direction='column' justify='space-between' align='end'>
            <NumberField
              value={quantity}
              minValue={1}
              maxValue={availableQuantity}
              isDisabled={isCalculating}
              variant='noOutline'
              onChange={handleQuantityChange}
              style={{ width: '120px' }}
            />
            <Button
              variant='plain'
              color='default'
              size='small'
              isDisabled={isCalculating}
              onPress={() => onDelete?.(() => handleQuantityChange(0))}
            >
              <Typography variant='bodyMd'>{t('v2.page.action.confirm.remove')}</Typography>
            </Button>
          </Stack>
        </Stack>

        {overMaxQuantity && (
          <Typography variant='bodySm'>
            {t('page.error.maximumQuantity', { maximumQuantity: availableQuantity })}
          </Typography>
        )}
      </Stack>
    </Box>
  );
};

export default CartItem;
