import { useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Box, Stack } from '@aftership/astra';
import { tokenVars } from '@aftership/astra-tokens/Contract.css';

import { DeleteModal, DeleteModalProps } from '@/components/DeleteModal';

import { useExchangeInAppSubFlow } from '../../hooks/useExchangeInAppSubFlow';
import { CartItem } from '../CartItem';

const CartList = () => {
  const { t } = useTranslation();
  const { context } = useExchangeInAppSubFlow();
  const { exchangeItems = [] } = context ?? {};
  const [isOpen, setIsOpen] = useState(false);
  const handleConfirmRef = useRef<DeleteModalProps['onConfirm']>();

  return (
    <Box
      flex={1}
      overflow='auto'
      paddingX={tokenVars.Semantic.Space.Xl}
      paddingTop={tokenVars.Semantic.Space.Xl}
    >
      <Stack direction='column' gap='m'>
        {exchangeItems.map((item) => {
          return (
            <CartItem
              key={item.productId + item.variantId}
              item={item}
              onDelete={(fn) => {
                handleConfirmRef.current = fn;
                setIsOpen(true);
              }}
            />
          );
        })}
      </Stack>
      <DeleteModal
        title={t('popup.description.deleteItem')}
        info={t('popup.description.doYouWantToDeleteThisItem')}
        cancelText={t('page.request.cancel')}
        confirmText={t('page.request.delete')}
        isOpen={isOpen}
        onClose={() => {
          handleConfirmRef.current = undefined;
          setIsOpen(false);
        }}
        onConfirm={() => {
          handleConfirmRef.current?.();
          handleConfirmRef.current = undefined;
          setIsOpen(false);
        }}
      />
    </Box>
  );
};

export default CartList;
