import { useEffect, useMemo } from 'react';
import { useTranslation } from 'react-i18next';

import { Box, Pressable, Stack, Typography } from '@aftership/astra';
import { tokenVars } from '@aftership/astra-tokens/Contract.css';
import { CollectionValue } from '@aftership/returns-logics-core';

import { useExchangeInAppSubFlow } from '@/features/exchange-in-app/hooks/useExchangeInAppSubFlow';
import { useShopInfo } from '@/features/returns/hooks/useShopInfo';
import useDevice from '@/hooks/useDevice';

import { collectionItemClassName } from './CollectionsList.css';

const CollectionsList = () => {
  const isMobile = useDevice().mobile;
  const { t } = useTranslation();
  const { dispatch, context } = useExchangeInAppSubFlow() || {};
  const { collectionId = '', collectionOptions = [] } = context || {};
  const shopInfo = useShopInfo();

  /**
   * 当未开启推荐商品时，如果用户之前选择了推荐商品，则将 collectionId 设置为 ALL
   */
  useEffect(() => {
    if (
      !shopInfo.exchange_for_anything_recommendation_active &&
      collectionId === CollectionValue.Recommend
    ) {
      dispatch({ type: 'SET_COLLECTION_ID', data: { collectionId: CollectionValue.ALL } });
    }
  }, [collectionId, dispatch, shopInfo.exchange_for_anything_recommendation_active]);

  const options = useMemo(() => {
    return [
      ...(shopInfo.exchange_for_anything_recommendation_active
        ? [
            {
              label: t('v2.in_app.collection.recommend'),
              value: CollectionValue.Recommend,
            },
          ]
        : []),
      {
        label: t('v2.in_app.collection.all'),
        value: CollectionValue.ALL,
      },
      ...collectionOptions,
    ];
  }, [collectionOptions, shopInfo, t]);

  if (isMobile) {
    return (
      <Box
        width='100%'
        overflow='auto'
        paddingX={tokenVars.Semantic.Space.M}
        paddingBottom={tokenVars.Semantic.Space.M}
      >
        <Stack wrap={false} align='stretch' justify='stretch' gap='xs'>
          {options.map((item) => {
            return (
              <Pressable
                key={item.value}
                onPress={() => {
                  dispatch({ type: 'SET_COLLECTION_ID', data: { collectionId: item.value } });
                }}
              >
                <Box
                  padding={`${tokenVars.Semantic.Space.Xs} ${tokenVars.Semantic.Space.M}`}
                  borderRadius={tokenVars.Semantic.Space.L}
                  backgroundColor={
                    collectionId === item.value
                      ? tokenVars.Primitive.Color['Gray']['300']
                      : tokenVars.Semantic.Color.Bg.Body
                  }
                  className={collectionItemClassName}
                  borderColor={
                    collectionId === item.value
                      ? 'transparent'
                      : tokenVars.Primitive.Color['Gray']['300']
                  }
                >
                  <Typography
                    variant='bodyMd'
                    color={collectionId === item.value ? 'primary' : 'secondary'}
                    style={{
                      whiteSpace: 'nowrap',
                    }}
                  >
                    {item.label}
                  </Typography>
                </Box>
              </Pressable>
            );
          })}
        </Stack>
      </Box>
    );
  }

  return (
    <Box overflow='auto' flex='0 0 256px' paddingBottom={tokenVars.Semantic.Space.M}>
      <Stack align='stretch' justify='stretch' direction='column' gap='xs'>
        {options.map((item) => {
          return (
            <Pressable
              key={item.value}
              onPress={() => {
                dispatch({ type: 'SET_COLLECTION_ID', data: { collectionId: item.value } });
              }}
            >
              <Box
                padding={`${tokenVars.Semantic.Space.Xs} ${tokenVars.Semantic.Space.M}`}
                borderRadius={tokenVars.Semantic.Space.Xs}
                backgroundColor={collectionId === item.value ? '#f5f6f7' : 'transparent'}
                className={collectionItemClassName}
              >
                <Typography
                  variant='bodyMd'
                  color={collectionId === item.value ? 'primary' : 'secondary'}
                  style={{
                    whiteSpace: 'nowrap',
                  }}
                >
                  {item.label}
                </Typography>
              </Box>
            </Pressable>
          );
        })}
      </Stack>
    </Box>
  );
};

export default CollectionsList;
