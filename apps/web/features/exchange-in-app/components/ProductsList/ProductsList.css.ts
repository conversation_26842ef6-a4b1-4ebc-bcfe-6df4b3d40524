import { style } from '@vanilla-extract/css';

import { tokenVars } from '@aftership/astra-tokens/Contract.css';

export const productListContainerClassName = style({
  display: 'grid',
  gridTemplateColumns: 'repeat(auto-fill, 200px)',
  gap: tokenVars.Semantic.Space['2Xl'],
  justifyContent: 'space-between',
});

export const mobileProductListContainerClassName = style({
  display: 'grid',
  gridTemplateColumns: '1fr 1fr',
  gap: tokenVars.Semantic.Space['2Xl'],
  justifyContent: 'space-between',
});
