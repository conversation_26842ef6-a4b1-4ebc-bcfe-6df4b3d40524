import { UIEvent } from 'react';

import { <PERSON>, Spinner, Stack, Typography, UnstyledButton } from '@aftership/astra';
import { tokenVars } from '@aftership/astra-tokens/Contract.css';

import { EllipsisText } from '@/components/EllipsisText';
import { SquareImage } from '@/components/SquareImage';
import { useUniversalRouting } from '@/features/returns/hooks/useUniversalRouting';
import useDevice from '@/hooks/useDevice';
import { toCurrency } from '@/utils/price';

import {
  mobileProductListContainerClassName,
  productListContainerClassName,
} from './ProductsList.css';

import { useExchangeInAppSubFlow } from '../../hooks/useExchangeInAppSubFlow';

const { Space, Color } = tokenVars.Semantic;

const LoadMoreSpinner = () => {
  return (
    <Stack
      align='center'
      justify='center'
      style={{
        width: '100%',
        height: tokenVars.Primitive.Size['1600'],
      }}
    >
      <Spinner />
    </Stack>
  );
};

const ProductsList = () => {
  const isMobile = useDevice().mobile;
  const router = useUniversalRouting();
  const { context, dispatch, currentStep } = useExchangeInAppSubFlow() || {};
  const { products = [], haveNextPage } = context || {};

  const handleScroll = (e: UIEvent<HTMLDivElement>) => {
    const { scrollTop, scrollHeight, clientHeight } = e.currentTarget;
    if (scrollTop + clientHeight >= scrollHeight - 50) {
      handleLoadMore();
    }
  };
  const handleLoadMore = () => {
    if (haveNextPage && !currentStep?.isLoading) {
      dispatch({ type: 'LOAD_MORE' });
    }
  };

  if (products.length === 0 && currentStep?.isLoading) {
    return (
      <Stack
        align='center'
        justify='center'
        flex={1}
        style={{
          paddingBottom: tokenVars.Primitive.Size['2000'],
        }}
      >
        <Spinner size='large' />
      </Stack>
    );
  }

  if (isMobile) {
    return (
      <div style={{ overflow: 'auto', flex: 1, paddingBottom: '100px' }} onScroll={handleScroll}>
        <Box className={mobileProductListContainerClassName} paddingX={tokenVars.Semantic.Space.M}>
          {products.map((product) => {
            const title = product.title || '';
            const cover = (product.image_urls || [])[0];
            const price = product.lowest_price_variant.price;
            const compareAtPrice = product.lowest_price_variant.compare_at_price;
            return (
              <UnstyledButton
                key={product.external_id}
                onPress={() =>
                  router.navigate({
                    params: {
                      id: product.external_id,
                    },
                    pathname: `/exchange/products/[id]`,
                    asPath: `/exchange/products/${product.external_id}`,
                  })
                }
              >
                <Box>
                  <SquareImage
                    src={cover}
                    width='100%'
                    borderRadius={tokenVars.Semantic.Radius.M}
                  />
                  <EllipsisText
                    maxLine={2}
                    text={title}
                    variant='bodyLg'
                    style={{ paddingTop: tokenVars.Semantic.Space.Xs }}
                  />
                  <Stack gap='xs' align='center'>
                    <Typography variant='bodySm'>
                      {toCurrency(price.amount, price.currency)}
                    </Typography>
                    {compareAtPrice && compareAtPrice.amount > price.amount && (
                      <Typography
                        variant='bodySm'
                        color='secondary'
                        style={{ textDecoration: 'line-through' }}
                      >
                        {toCurrency(compareAtPrice.amount, compareAtPrice.currency)}
                      </Typography>
                    )}
                  </Stack>
                </Box>
              </UnstyledButton>
            );
          })}
        </Box>
        {haveNextPage && <LoadMoreSpinner />}
      </div>
    );
  }

  return (
    <div
      style={{ overflow: 'auto', flex: 1, paddingBottom: tokenVars.Semantic.Space.M }}
      onScroll={handleScroll}
    >
      <Box className={productListContainerClassName} paddingX={Space.L}>
        {products.map((product) => {
          const title = product.title || '';
          const cover = (product.image_urls || [])[0];
          const price = product.lowest_price_variant.price;
          const compareAtPrice = product.lowest_price_variant.compare_at_price;
          return (
            <UnstyledButton
              key={product.external_id}
              onPress={() =>
                router.navigate({
                  pathname: `/exchange/products/[id]`,
                  asPath: `/exchange/products/${product.external_id}`,
                  params: {
                    id: product.external_id,
                  },
                })
              }
            >
              <Box>
                <SquareImage src={cover} width={200} borderRadius={tokenVars.Semantic.Radius.M} />
                <EllipsisText
                  maxLine={2}
                  text={title}
                  variant='bodyLg'
                  style={{ paddingTop: tokenVars.Semantic.Space.Xs }}
                />
                <Stack gap='xs' align='center'>
                  <Typography variant='bodySm'>
                    {toCurrency(price.amount, price.currency)}
                  </Typography>
                  {compareAtPrice && compareAtPrice.amount > price.amount && (
                    <Typography
                      variant='bodySm'
                      style={{ textDecoration: 'line-through', color: Color.Text.Secondary }}
                    >
                      {toCurrency(compareAtPrice.amount, compareAtPrice.currency)}
                    </Typography>
                  )}
                </Stack>
              </Box>
            </UnstyledButton>
          );
        })}
      </Box>
      {haveNextPage && <LoadMoreSpinner />}
    </div>
  );
};

export default ProductsList;
