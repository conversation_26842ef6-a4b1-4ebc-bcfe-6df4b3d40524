/* eslint-disable @typescript-eslint/naming-convention */
import { useEffect, useMemo, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';

import {
  Box,
  Drawer,
  Icon,
  IconButton,
  Stack,
  TextField,
  Typography,
  UnstyledButton,
} from '@aftership/astra';
import { CartOutlined, ChevronLeftOutlined } from '@aftership/astra-icons';
import { tokenVars } from '@aftership/astra-tokens/Contract.css';

import { useExchangeInAppSubFlow } from '@/features/exchange-in-app/hooks/useExchangeInAppSubFlow';
import { useShopInfo } from '@/features/returns/hooks/useShopInfo';
import { useUniversalRouting } from '@/features/returns/hooks/useUniversalRouting';
import { useDebounce } from '@/hooks/useDebounce';
import useDevice from '@/hooks/useDevice';
import { toCurrency } from '@/utils/price';

import { drawerClassName, textFieldClassName } from './SearchBar.css';

import Cart from '../../Cart';

const searchInputTokens = {
  Radius: {
    Default: '9999px',
  },
};

interface CartNumberProps {
  onPress?: () => void;
  value?: number;
}

const CartNumber = ({ onPress, value }: CartNumberProps) => {
  return (
    <UnstyledButton
      onPress={onPress}
      style={{
        flexShrink: 0,
        border: '2px solid #e5e7eb',
        paddingInline: tokenVars.Semantic.Space.M,
        paddingBlock: tokenVars.Semantic.Space.Xs,
        borderRadius: tokenVars.Semantic.Radius['2Xl'],
      }}
    >
      <Stack gap='xs' align='center'>
        <Icon
          source={CartOutlined}
          size={tokenVars.Semantic.Space.M}
          style={{ color: tokenVars.Semantic.Color.Bg.Body }}
        />
        <Typography variant='bodyMd' style={{ color: tokenVars.Semantic.Color.Bg.Body }}>
          {value}
        </Typography>
      </Stack>
    </UnstyledButton>
  );
};

const SearchBar = () => {
  const router = useUniversalRouting();
  const isMobile = useDevice().mobile;
  const { t } = useTranslation();
  const { dispatch, context } = useExchangeInAppSubFlow() || {};
  const { exchangeItems = [], previewSummary } = context || {};
  const { extraCredit, efaPreDiscountCredit, creditRemain } = previewSummary || {};

  // 获取返回金额
  const [keyword, setKeyword] = useState('');
  const [openSheet, setOpenSheet] = useState(false);
  const firstRender = useRef(true);
  const shopInfo = useShopInfo();

  useEffect(() => {
    if (router.getSearchParam('openSheet')) {
      setOpenSheet(true);
      router.replace({
        pathname: '/exchange/products',
      });
    }
  }, [router]);

  useDebounce(
    () => {
      if (firstRender.current) {
        firstRender.current = false;
        return;
      }
      dispatch?.({ type: 'SET_KEYWORD', data: { keyword } });
    },
    500,
    [keyword],
  );

  const extraCreditLabel = useMemo(() => {
    // 使用与 Summary 组件中相同的计算逻辑
    const creditAmount =
      Number(extraCredit?.amount ?? 0) + Number(efaPreDiscountCredit?.amount ?? 0);
    const credit = {
      amount: creditAmount,
      currency: extraCredit?.currency ?? efaPreDiscountCredit?.currency,
    };

    if (credit?.amount) {
      return ` (${t('resolution.description.extraCredit', {
        extraCredit: toCurrency(credit.amount, credit.currency, true),
      })})`;
    }
    return null;
  }, [extraCredit, efaPreDiscountCredit, t]);

  const handleGoBack = () => {
    dispatch({ type: 'GO_BACK' });
  };

  return (
    <Box
      backgroundColor={shopInfo.theme_color}
      paddingX={isMobile ? tokenVars.Semantic.Space.M : tokenVars.Primitive.Size['2000']}
    >
      <Drawer
        isOpen={openSheet}
        isFullScreen={isMobile}
        className={drawerClassName}
        style={{ width: '400px' }}
        title={`${t('page.description.cart')}${exchangeItems.length > 0 ? ` (${exchangeItems.length})` : ''}`}
        onOpenChange={(isVisible) => setOpenSheet(isVisible)}
      >
        <Cart onAddMore={() => setOpenSheet(false)} />
      </Drawer>

      {isMobile ? (
        <Stack
          gap='m'
          align='center'
          justify='center'
          style={{ height: tokenVars.Primitive.Size['2000'] }}
        >
          <TextField
            isFullWidth
            type='text'
            className={textFieldClassName}
            placeholder={t('product.search.placeholder')}
            onChange={setKeyword}
            tokens={searchInputTokens}
          />
          <CartNumber value={exchangeItems.length} onPress={() => setOpenSheet(true)} />
          {creditRemain && !openSheet && (
            <Box
              position='fixed'
              left={0}
              right={0}
              bottom={0}
              padding={tokenVars.Semantic.Space.L}
              backgroundColor='#3b73af'
              zIndex={9}
            >
              <Typography variant='bodyMd' style={{ color: tokenVars.Semantic.Color.Bg.Body }}>
                {Number(creditRemain?.amount) >= 0
                  ? t('exchangeOnStore.banner.creditUse', {
                      RemainingCredit: toCurrency(creditRemain, true),
                    })
                  : t('exchangeOnStore.banner.creditExchange', {
                      RemainingCredit: toCurrency(
                        Math.abs(Number(creditRemain?.amount)),
                        creditRemain?.currency,
                        true,
                      ),
                    })}
                {extraCreditLabel}
              </Typography>
            </Box>
          )}
        </Stack>
      ) : (
        <Box maxWidth={1280} margin='auto'>
          <Stack
            align='center'
            justify='center'
            gap='m'
            style={{
              height: tokenVars.Primitive.Size['2000'],
            }}
          >
            <IconButton
              size='large'
              icon={ChevronLeftOutlined}
              style={{ color: 'white' }}
              onPress={handleGoBack}
            />
            <Box flex={1} />
            {creditRemain && (
              <Typography
                style={{
                  paddingInlineEnd: tokenVars.Semantic.Space.M,
                  color: tokenVars.Semantic.Color.Bg.Body,
                }}
              >
                {Number(creditRemain?.amount) >= 0
                  ? t('exchangeOnStore.banner.creditUse', {
                      RemainingCredit: toCurrency(creditRemain, true),
                    })
                  : t('exchangeOnStore.banner.creditExchange', {
                      RemainingCredit: toCurrency(
                        Math.abs(Number(creditRemain?.amount)),
                        creditRemain?.currency,
                        true,
                      ),
                    })}
                {extraCreditLabel}
              </Typography>
            )}
            <TextField
              type='text'
              className={textFieldClassName}
              placeholder={t('product.search.placeholder')}
              onChange={setKeyword}
              tokens={searchInputTokens}
            />
            <CartNumber value={exchangeItems.length} onPress={() => setOpenSheet(true)} />
          </Stack>
        </Box>
      )}
    </Box>
  );
};

export default SearchBar;
