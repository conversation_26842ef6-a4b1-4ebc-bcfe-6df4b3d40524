import { useTranslation } from 'react-i18next';

import { Box, Stack } from '@aftership/astra';
import { tokenVars } from '@aftership/astra-tokens/Contract.css';

import { Button } from '@/components/Button';
import { Divider } from '@/components/Divider';
import { toCurrency } from '@/utils/price';

import { useExchangeInAppSubFlow } from '../../hooks/useExchangeInAppSubFlow';
import { SummaryItem } from '../SummaryItem';

interface SummaryProps {
  onSubmit?: () => void;
}

const Summary = ({ onSubmit }: SummaryProps) => {
  const { t } = useTranslation();
  const { context } = useExchangeInAppSubFlow();
  const { previewSummary } = context ?? {};
  const {
    creditRemain,
    efaPreDiscountCredit,
    efaItemTotal,
    efaTaxTotal,
    returnCreditTotal,
    extraCredit,
    exchangeTaxIncluded,
  } = previewSummary || {};

  const creditForExchange = {
    amount: `${Number(efaPreDiscountCredit?.amount || 0) + Number(extraCredit?.amount || 0)}`,
    currency: efaPreDiscountCredit?.currency,
  };

  const isShowExchangeTaxSection = Boolean(!exchangeTaxIncluded) && !!Number(efaTaxTotal?.amount);

  const AmountItem = () => {
    const amountLabel =
      Number(creditRemain?.amount) >= 0
        ? t('page.description.amountRemain')
        : t('page.description.amountOwed');
    return (
      <SummaryItem label={amountLabel} variant='bodyLgSemibold' color='primary'>
        {toCurrency(Math.abs(Number(creditRemain?.amount || 0)), creditRemain?.currency)}
      </SummaryItem>
    );
  };

  return (
    <Box padding={tokenVars.Semantic.Space.Xl} backgroundColor={tokenVars.Semantic.Color.Bg.Body}>
      <Stack direction='column' gap='m'>
        <SummaryItem label={t('page.description.summary')} variant='heading2Xs' />
        <Stack direction='column' gap='m' style={{ paddingTop: tokenVars.Semantic.Space.Xs }}>
          {/* exchange items */}
          <SummaryItem label={t('page.general.exchangeItems')} variant='bodyMd' color='secondary'>
            {toCurrency(efaItemTotal)}
          </SummaryItem>

          {/* exchange item tax */}
          {isShowExchangeTaxSection && (
            <SummaryItem
              label={t('page.description.exchangeTax')}
              variant='bodyMd'
              color='secondary'
            >
              {toCurrency(efaTaxTotal?.amount, efaTaxTotal?.currency)}
            </SummaryItem>
          )}

          {/* return credit */}
          <SummaryItem
            variant='bodyMd'
            color='secondary'
            label={`${t('page.general.returnCredits')}`}
          >
            {`-${toCurrency(returnCreditTotal?.amount, returnCreditTotal?.currency)}`}
          </SummaryItem>

          {/* credit for exchange */}
          <SummaryItem
            label={t('page.description.creditForExchange')}
            variant='bodyMd'
            color='secondary'
          >
            {`-${toCurrency(creditForExchange?.amount, creditForExchange?.currency)}`}
          </SummaryItem>
        </Stack>
        <Divider spacing={0} />
        <AmountItem />
        <Button size='large' onPress={onSubmit}>
          {t('page.request.nextStep')}
        </Button>
      </Stack>
    </Box>
  );
};

export default Summary;
