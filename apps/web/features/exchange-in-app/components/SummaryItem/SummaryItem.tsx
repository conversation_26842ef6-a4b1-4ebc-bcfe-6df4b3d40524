import React, { ReactNode } from 'react';

import { Spinner, Stack, Typography, TypographyProps } from '@aftership/astra';

interface SummaryItemProps {
  label: ReactNode;
  children?: ReactNode;
  isLoading?: boolean;
  variant?: TypographyProps<'span'>['variant'];
  color?: TypographyProps<'span'>['color'];
}

const SummaryItem = ({
  label,
  children,
  isLoading,
  variant = 'bodyLg',
  color,
}: SummaryItemProps) => {
  return (
    <Stack justify='space-between'>
      {typeof label === 'string' ? (
        <Typography variant={variant} color={color}>
          {label}
        </Typography>
      ) : (
        label
      )}
      {isLoading ? (
        <Spinner />
      ) : typeof children === 'string' ? (
        <Typography variant={variant} color={color}>
          {children}
        </Typography>
      ) : (
        children
      )}
    </Stack>
  );
};

export default SummaryItem;
