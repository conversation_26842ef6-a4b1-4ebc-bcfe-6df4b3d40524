import { style } from '@vanilla-extract/css';

import { tokenVars } from '@aftership/astra-tokens/Contract.css';

const { Color, Stroke_Width } = tokenVars.Primitive;

export const unselectedVariantItem = style({
  paddingInline: tokenVars.Semantic.Space.M,
  paddingBlock: tokenVars.Semantic.Space.Xs,
  borderRadius: tokenVars.Semantic.Space.Xl,
  borderWidth: Stroke_Width[25],
  borderStyle: 'solid',
  borderColor: Color.Gray[300],
});

export const selectedVariantItem = style({
  paddingInline: `calc(${tokenVars.Semantic.Space.M} - ${Stroke_Width[50]} + ${Stroke_Width[25]})`,
  paddingBlock: `calc(${tokenVars.Semantic.Space.Xs} - ${Stroke_Width[50]} + ${Stroke_Width[25]})`,
  borderRadius: tokenVars.Semantic.Space.Xl,
  borderWidth: Stroke_Width[50],
  borderStyle: 'solid',
});
