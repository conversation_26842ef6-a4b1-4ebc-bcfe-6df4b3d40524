import { Box, Typography, UnstyledButton } from '@aftership/astra';

import { useShopInfo } from '@/features/returns/hooks/useShopInfo';

import { selectedVariantItem, unselectedVariantItem } from './VariantItem.css';

interface VariantItemProps {
  disabled?: boolean;
  name: string;
  isSelected: boolean;
  setValue: (value: string) => void;
}

const VariantItem = ({ disabled, name, isSelected, setValue }: VariantItemProps) => {
  const shopInfo = useShopInfo();
  return (
    <UnstyledButton isDisabled={disabled} onPress={() => setValue(name)}>
      <Box
        className={isSelected ? selectedVariantItem : unselectedVariantItem}
        borderColor={isSelected ? shopInfo.theme_color : undefined}
      >
        <Typography variant='bodySm'>{name}</Typography>
      </Box>
    </UnstyledButton>
  );
};

export default VariantItem;
