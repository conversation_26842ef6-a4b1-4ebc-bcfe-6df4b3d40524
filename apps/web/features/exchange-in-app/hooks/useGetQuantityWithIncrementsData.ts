/*
 * https://aftership.atlassian.net/browse/RTC-15258
 * quantity 字段中已经是乘以 increment 的结果，理论上所以不用再进行额外计算，直接使用 quantity 即可
 * 这里处理方式是将 increment 视为 1 (FIXED_INCREMENT)，实现 quantity 乘以 increment 仍等于 quantity，而不是去掉计算逻辑，避免联动的变更
 * */
import Big from 'big.js';
import { Dispatch, useEffect, useState } from 'react';

import { DEFAULT_INCREMENT, FIXED_INCREMENT } from '../utils/constants';

interface IData {
  // 不带增量的原始数量必须是整数，否则认为数量非法
  quantityError: boolean;
  // 用于输入框的展示，可能带有 [x.] 等格式，通过数字格式转化会丢失小数点，因此采用字符串格式
  quantityStrValue: string;
  // 用户 stepper 组件 onchange 回调中设置输入框展示的值，区分开实际存储时为数字格式的值
  setQuantityStrValue: Dispatch<string>;
  // 用于 stepper 组件 step 属性
  step: number;
  // 用于 stepper 组件 min 属性
  min: number;
  // 用于 stepper 组件 max 属性
  max?: number;
}

export const getQuantityWithIncrements = (quantity: number) => {
  const incrementsNum = Big(FIXED_INCREMENT || DEFAULT_INCREMENT);

  return incrementsNum.mul(quantity).toNumber();
};

export const getQuantityWithoutIncrements = (quantity: number) => {
  return Big(quantity)
    .div(FIXED_INCREMENT || DEFAULT_INCREMENT)
    .toNumber();
};

export const getIsCurrentQuantityInt = (quantity = 0) =>
  Boolean(quantity && Number.isInteger(quantity));

export const getHasQuantityError = (items: Record<string, number>[], itemQuantityName: string) => {
  return items.some((item) => {
    return !getIsCurrentQuantityInt(item[itemQuantityName]);
  });
};

export const useGetQuantityWithIncrementsData = (
  quantity: number,
  returnableQuantity?: number,
): IData => {
  const quantityError = !getIsCurrentQuantityInt(quantity);

  const [quantityStrValue, setQuantityStrValue] = useState(
    getQuantityWithIncrements(quantity).toString(),
  );

  useEffect(() => {
    setQuantityStrValue(getQuantityWithIncrements(quantity).toString());
  }, [quantity]);

  return {
    quantityError,
    quantityStrValue,
    setQuantityStrValue,
    step: FIXED_INCREMENT,
    min: FIXED_INCREMENT,
    max: returnableQuantity ? getQuantityWithIncrements(returnableQuantity) : undefined,
  };
};
