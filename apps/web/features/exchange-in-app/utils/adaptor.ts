export const draftCheckoutItemsAdaptor = <
  T extends { variantId: string; productId: string; quantity: number },
>(
  skuItem: T,
  cartItems: T[],
  cumulative = false,
): T[] => {
  // todo: 还可以降低空间和时间复杂度
  const findIndex = cartItems.findIndex(
    (item) => item.productId === skuItem.productId && item.variantId === skuItem.variantId,
  );

  if (findIndex >= 0) {
    skuItem.quantity > 0
      ? cartItems.splice(findIndex, 1, {
          ...skuItem,
          quantity: cumulative
            ? skuItem.quantity + cartItems[findIndex].quantity
            : skuItem.quantity,
        })
      : cartItems.splice(findIndex, 1);
  } else {
    cartItems.unshift(skuItem);
  }

  return cartItems;
};
