import { ProductDetail, Variant } from '@aftership/returns-logics-core';

import { toCurrency } from '@/utils/price';

export type SpecItem = {
  available: boolean;
  value: string;
  name?: string;
};

export type SKUAttrItem = {
  name: string;
  specList: Array<SpecItem>;
};

const NEED_SORTING_OPTIONS = ['Size'];

export const unique = (arr: any[]) => {
  return Array.from(new Set(arr));
};

/**
 * 提取 option value 中的数字, 用于排序
 * @param str
 */
const extractNumberFromString = (str: string): number => {
  const regex = /[0-9]+(?:\.[0-9]+)?/;
  const match = str.match(regex);
  if (match && match.length > 0) {
    return parseFloat(match[0]);
  }
  return 0;
};

/**
 * 如果当前 optionName 在 NEED_SORTING_OPTIONS 中,则是需要排序的,需要对 specList 进行排序
 * @param optionName
 * @param specList
 */
export const trySortingSpecList = (optionName: string, specList: Array<SpecItem>) => {
  if (NEED_SORTING_OPTIONS.some((name) => optionName.toLowerCase().includes(name.toLowerCase()))) {
    return specList.sort(
      (a, b) => extractNumberFromString(a.value) - extractNumberFromString(b.value),
    );
  } else {
    return specList;
  }
};

export const getInitialSkuAttrItems = (
  groupNames: string[],
  options: { name: string; value: string }[],
  productOptions: ProductDetail['options'],
): Array<SKUAttrItem> => {
  const orderedOptions = productOptions.sort(
    (option1, option2) => Number(option1.position) - Number(option2.position),
  );
  const orderedOptionNames = orderedOptions.map((option) => option.name);
  return groupNames
    ?.sort(
      (groupName1, groupNames2) =>
        orderedOptionNames.indexOf(groupName1) - orderedOptionNames.indexOf(groupNames2),
    )
    .map((name, index) => {
      const optionValues = unique(
        options.filter((option) => option.name === name).map((option) => option.value),
      ).sort(
        (option1, option2) =>
          orderedOptions[index].values.indexOf(option1) -
          orderedOptions[index].values.indexOf(option2),
      );
      let specList = optionValues.map<SpecItem>((value) => {
        return {
          available: true,
          value,
        };
      });
      specList = trySortingSpecList(name, specList);
      return {
        name,
        specList,
      };
    });
};

export const evaluatePriceByVariant = (variant?: Variant) => {
  if (variant) {
    return toCurrency(variant.price.amount, variant.price.currency);
  } else {
    return toCurrency(0);
  }
};
/**
 * 根据选中的"属性-规格", 从 sku列表中找到指定的 sku
 * @param skus sku列表
 * @param selectOptions 当前选择的 "属性-规格" 集合
 * @param validLength 合法的 "属性-规格" 集合长度
 */
export const findSkuByOption = (
  skus: Array<Variant>,
  selectOptions: Array<{ name: string; value: string }>,
  validLength: number,
) => {
  if (selectOptions.length < validLength) {
    return undefined;
  } else {
    return skus.find((sku) =>
      selectOptions.every((option) =>
        sku.options.some(
          (skuOption) => skuOption.name === option.name && skuOption.value === option.value,
        ),
      ),
    );
  }
};
