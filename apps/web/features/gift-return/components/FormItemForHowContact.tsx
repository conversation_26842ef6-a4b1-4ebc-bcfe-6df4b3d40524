import { Control } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

import { Stack, Typography } from '@aftership/astra';

import FormInput from '@/components/Form/FormInput';

export type Props = {
  control: Control<any, any>;
};

export const HowContact = ({ control }: Props) => {
  const { t } = useTranslation();
  return (
    <Stack direction='column' gap='xs'>
      <Typography variant='bodyLgSemibold' style={{ whiteSpace: 'nowrap' }}>
        {t('page.gift.description.howContact')}
      </Typography>
      <Stack gap='xs' justify='space-around'>
        <FormInput
          control={control}
          name='recipient.firstName'
          placeholder={t('page.gift.placeholder.firstName')}
          fullWidth={true}
        />
        <FormInput
          control={control}
          name='recipient.lastName'
          placeholder={t('page.gift.placeholder.lastName')}
          fullWidth={true}
        />
      </Stack>
      <FormInput
        control={control}
        name='recipient.email'
        placeholder={t('page.gift.placeholder.email')}
        fullWidth={true}
      />
    </Stack>
  );
};
