import { Control } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

import { Stack, Typography } from '@aftership/astra';

import FormRadio, { Option } from '@/components/Form/FormRadio';

interface Props {
  resolutions: Option[];
  control: Control<any, any>;
}
export const HowResolve = ({ resolutions, control }: Props) => {
  const { t } = useTranslation();

  return (
    <Stack direction='column' gap='xs'>
      <Typography variant='bodyLgSemibold' style={{ whiteSpace: 'nowrap' }}>
        {t('page.gift.description.howResolve')}
      </Typography>
      <FormRadio control={control} name='resolution' radioItems={resolutions} />
    </Stack>
  );
};
