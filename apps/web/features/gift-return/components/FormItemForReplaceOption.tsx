import { Control, FieldValues } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

import { Stack, Typography } from '@aftership/astra';

import FormInput from '@/components/Form/FormInput';

export type Props<T extends FieldValues> = {
  control: Control<T>;
};

const FormItemForReplaceOption = <T extends FieldValues>({ control }: Props<T>) => {
  const { t } = useTranslation();

  return (
    <Stack direction='column' gap='xs'>
      <Typography variant='bodyLgSemibold' style={{ whiteSpace: 'nowrap' }}>
        {t('page.gift.description.whichOption')}
      </Typography>
      <FormInput
        control={control}
        placeholder={t('page.gift.placeholder.option')}
        name={'option'}
        fullWidth={true}
      />
    </Stack>
  );
};

export default FormItemForReplaceOption;
