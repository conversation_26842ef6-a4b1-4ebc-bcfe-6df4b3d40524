import { Control } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

import { Grid, Stack, Typography } from '@aftership/astra';

import FormInput from '@/components/Form/FormInput';
import FormSelect, { Option } from '@/components/Form/FormSelect';

export type Props = {
  countryOptions: Array<Option>;
  stateOptions: Array<Option>;
  hasStateField: boolean;
  control: Control<any, any>;
};

const FormItemForShippingAddress = ({
  control,
  countryOptions,
  stateOptions,
  hasStateField,
}: Props) => {
  const { t } = useTranslation();

  return (
    <Stack direction='column' gap='xl'>
      <Stack direction='column' gap='xs'>
        <Typography variant='bodyLgSemibold' style={{ whiteSpace: 'nowrap' }}>
          {t('page.gift.description.address')}
        </Typography>
        <Stack gap='xs' justify='space-around'>
          <FormInput
            control={control}
            name='address.firstName'
            placeholder={t('page.gift.placeholder.firstName')}
            fullWidth={true}
          />
          <FormInput
            control={control}
            name='address.lastName'
            placeholder={t('page.gift.placeholder.lastName')}
            fullWidth={true}
          />
        </Stack>
        <FormInput
          control={control}
          name='address.line1'
          placeholder={t('page.gift.placeholder.addressLine1')}
          fullWidth={true}
        />
        <FormInput
          control={control}
          name='address.line2'
          placeholder={t('page.gift.placeholder.addressLine2')}
          fullWidth={true}
        />
        <FormSelect
          control={control}
          name='address.country'
          placeholder={t('page.gift.placeholder.country')}
          options={countryOptions}
        />
        {hasStateField ? (
          <Grid spacing='xs' container>
            <Grid size={6}>
              <FormInput
                control={control}
                name='address.city'
                placeholder={t('page.gift.placeholder.city')}
                fullWidth={true}
              />
            </Grid>
            <Grid size={6}>
              <FormSelect
                control={control}
                name='address.state'
                placeholder={t('page.gift.placeholder.state')}
                options={stateOptions}
              />
            </Grid>
          </Grid>
        ) : (
          <FormInput
            control={control}
            name='address.city'
            placeholder={t('page.gift.placeholder.city')}
            fullWidth={true}
          />
        )}

        <FormInput
          control={control}
          name='address.postalCode'
          placeholder={t('page.gift.placeholder.postalCode')}
          fullWidth={true}
        />
        <FormInput
          control={control}
          name='address.phoneNumber'
          placeholder={t('page.gift.placeholder.phoneNumber')}
          fullWidth={true}
        />
      </Stack>
    </Stack>
  );
};

export default FormItemForShippingAddress;
