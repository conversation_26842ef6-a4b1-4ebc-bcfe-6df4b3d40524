import { Control } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

import { Stack, Typography } from '@aftership/astra';

import FormTextArea from '@/components/Form/FormTextArea';

export type Props = {
  control: Control<any, any>;
};

export const WhatOthers = ({ control }: Props) => {
  const { t } = useTranslation();

  return (
    <Stack direction='column' gap='xs'>
      <Typography variant='bodyLgSemibold' style={{ whiteSpace: 'nowrap' }}>
        {t('page.gift.description.whatOthers')}
      </Typography>

      <FormTextArea control={control} name='notes' />
    </Stack>
  );
};
