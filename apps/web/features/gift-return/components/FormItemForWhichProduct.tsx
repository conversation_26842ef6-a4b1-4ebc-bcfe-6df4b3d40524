import { Control } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

import { Stack, Typography } from '@aftership/astra';

import FormInput from '@/components/Form/FormInput';

export type Props = {
  control: Control<any, any>;
};

export const WhichProduct = ({ control }: Props) => {
  const { t } = useTranslation();

  return (
    <Stack direction='column' gap='xs'>
      <Typography variant='bodyLgSemibold' style={{ whiteSpace: 'nowrap' }}>
        {t('page.gift.description.whichProduct')}
      </Typography>
      <FormInput
        control={control}
        name='productName'
        placeholder={t('page.gift.placeholder.productName')}
        fullWidth={true}
      />
    </Stack>
  );
};
