import { yupResolver } from '@hookform/resolvers/yup';
import { useMemo } from 'react';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

import { Box, Stack, Typography } from '@aftership/astra';
import { tokenVars } from '@aftership/astra-tokens/Contract.css';
import { GiftReturnResolution } from '@aftership/returns-logics-core';
import { useFlow } from 'returns-logics/react';

import { Button } from '@/components/Button';
import FormInput from '@/components/Form/FormInput';
import { FullScreenCard } from '@/components/FullScreenCard';
import { ScrollBox } from '@/components/ScrollBox';
import { StepCard } from '@/components/StepCard';
import useCountry from '@/hooks/useCountry';
import useDevice from '@/hooks/useDevice';
import { useStepCardMinHeight } from '@/hooks/useStepCardMinHeight';

import { HowContact } from './FormItemForHowContact';
import { HowResolve } from './FormItemForHowResolve';
import FormItemForReplaceOption from './FormItemForReplaceOption';
import FormItemForReturnSame from './FormItemForShippingAddress';
import { WhatOthers } from './FormItemForWhatOthers';
import { WhichProduct } from './FormItemForWhichProduct';
import { GiftReturnTitle } from './GiftReturnTitle';

import { FormWithoutOrderInfoFormValues } from '../constant/errorMapping';
import { giftReturnCardContent } from '../giftCard.css';
import useResolutionOptions from '../hooks/useResolutionOptions';
import { useSubmitGiftReturnFormNotMatch } from '../hooks/useSubmitGiftReturnFormNotMatch';
import { NotMatchingOrderSchema } from '../schema';

const FormWithoutOrderInfo = () => {
  const { t } = useTranslation();
  const resolutions = useResolutionOptions();

  const { i18n } = useTranslation();

  const { mobile } = useDevice();

  const flow = useFlow();
  const {
    options: countryOptions,
    findHasState,
    getStateOption,
    countriesMap,
  } = useCountry(flow?.children?.giftReturnSubFlow?.context?.countryMap);

  const { control, watch, handleSubmit } = useForm<FormWithoutOrderInfoFormValues>({
    mode: 'all',
    defaultValues: {
      resolution: resolutions[0].value as GiftReturnResolution,
    },
    resolver: yupResolver(NotMatchingOrderSchema()) as any,
    context: {
      countryMap: countriesMap,
    },
  });

  const countryCode = watch('address.country');
  const currentResolution = watch('resolution');

  const stateOptions = useMemo(() => getStateOption(countryCode), [countryCode, getStateOption]);

  const { submit, isLoading } = useSubmitGiftReturnFormNotMatch();

  const showStateField = findHasState(countryCode);

  const onSubmit = handleSubmit((values) => {
    submit({ ...values, langCode: i18n.language });
  });

  const height = useStepCardMinHeight();

  const onBack = () => {
    flow.children.giftReturnSubFlow?.dispatch?.({ type: 'goBack' });
  };

  const replaceOption = useMemo(() => {
    return (
      currentResolution === GiftReturnResolution.replaceWithTheSameItem && (
        <FormItemForReplaceOption control={control} />
      )
    );
  }, [control, currentResolution]);

  const whoGave = useMemo(() => {
    return (
      <Stack direction='column' gap='xs'>
        <Typography variant='bodyMd'>{t('page.gift.description.whoGave')}</Typography>
        <Stack direction='column' gap='xs'>
          <Stack direction='row' gap='xs'>
            <Box flex={1}>
              <FormInput
                control={control}
                name='customer.firstName'
                placeholder={t('page.gift.placeholder.firstName')}
                fullWidth={true}
              />
            </Box>
            <Box flex={1}>
              <FormInput
                control={control}
                name='customer.lastName'
                placeholder={t('page.gift.placeholder.lastName')}
                fullWidth={true}
              />
            </Box>
          </Stack>
          <FormInput
            control={control}
            name='customer.email'
            placeholder={t('page.gift.placeholder.emailOptional')}
            fullWidth={true}
          />
        </Stack>
      </Stack>
    );
  }, [control, t]);

  if (mobile) {
    return (
      <FullScreenCard
        onBack={onBack}
        footer={
          <Box paddingY={tokenVars.Semantic.Space.M} paddingX={tokenVars.Semantic.Space.M}>
            <Button
              onPress={() => {
                onSubmit();
              }}
              size='large'
              isLoading={isLoading}
              isDisabled={false}
            >
              {t('page.gift.action.submit')}
            </Button>
          </Box>
        }
      >
        <Stack
          direction='column'
          gap='xl'
          style={{
            width: '100%',
            paddingInline: tokenVars.Semantic.Space.Xs,
          }}
        >
          <GiftReturnTitle />
          {whoGave}
          <WhichProduct control={control} />
          <HowResolve resolutions={resolutions} control={control} />
          {replaceOption}
          <FormItemForReturnSame
            control={control}
            countryOptions={countryOptions}
            hasStateField={showStateField}
            stateOptions={stateOptions}
          />
          <HowContact control={control} />
          <WhatOthers control={control} />
        </Stack>
      </FullScreenCard>
    );
  }

  return (
    <StepCard
      height={height}
      width={792}
      title={<GiftReturnTitle />}
      onBack={onBack}
      style={{
        overflow: 'hidden',
      }}
    >
      <Stack
        flex={1}
        direction='column'
        className={giftReturnCardContent}
        style={{
          paddingTop: tokenVars.Semantic.Space['2Xl'],
        }}
      >
        <ScrollBox>
          <Stack
            direction='column'
            gap='xl'
            style={{
              width: '500px',
              margin: 'auto',
            }}
          >
            {whoGave}
            <WhichProduct control={control} />
            <HowResolve resolutions={resolutions} control={control} />
            {replaceOption}
            <FormItemForReturnSame
              control={control}
              countryOptions={countryOptions}
              hasStateField={showStateField}
              stateOptions={stateOptions}
            />
            <HowContact control={control} />
            <WhatOthers control={control} />
          </Stack>
        </ScrollBox>
        <Box width={500} margin={'auto'} paddingY={tokenVars.Semantic.Space.M}>
          <Button
            onPress={() => {
              onSubmit();
            }}
            size='large'
            isLoading={isLoading}
            isDisabled={false}
            isFullWidth
          >
            {t('page.gift.action.submit')}
          </Button>
        </Box>
      </Stack>
    </StepCard>
  );
};

export default FormWithoutOrderInfo;
