import { useTranslation } from 'react-i18next';

import { Stack, Typography } from '@aftership/astra';

export const GiftReturnTitle = () => {
  const { t } = useTranslation();
  return (
    <Stack direction='column' justify='center'>
      <Typography as='p' variant='headingXs' color='primary' style={{ textAlign: 'center' }}>
        {t('page.gift.description.title')}
      </Typography>
    </Stack>
  );
};
