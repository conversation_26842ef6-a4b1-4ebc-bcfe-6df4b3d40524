/* eslint-disable react/no-unescaped-entities */
import { useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Box, Link, Notice, Stack, Typography } from '@aftership/astra';
import { tokenVars } from '@aftership/astra-tokens/Contract.css';
import { OrderLookupType } from '@aftership/returns-logics-core';
import { useFlow } from 'returns-logics/react';

import { Button } from '@/components/Button';
import { Header } from '@/components/Header';
import { StepCard } from '@/components/StepCard';
import OrderLookupForm, {
  FormData,
  ValidateData,
} from '@/features/order-lookup/components/OrderLookupForm';
import ReturnsPageHeader from '@/features/returns/components/ReturnsPageHeader';
import { useReturns } from '@/features/returns/hooks/useReturns';
import { useShopInfo } from '@/features/returns/hooks/useShopInfo';
import useDevice from '@/hooks/useDevice';
import getTrackerInstance from '@/utils/tracker';
import { EventName } from '@/utils/tracker/consts';

import { GiftReturnTitle } from './GiftReturnTitle';

import { changeActions, formItemsCalss } from '../giftCard.css';
import { useQueryGiftReturnOrder } from '../hooks/useQueryGiftReturnOrder';

const OrderLookupInput = () => {
  const { t } = useTranslation();
  const { mobile } = useDevice();
  const shopInfo = useShopInfo();
  const { isCompact } = useReturns() ?? {};

  const { submit, isLoading } = useQueryGiftReturnOrder();

  const [errorMessage, setErrorMessage] = useState('');

  const flow = useFlow();

  const giftReturnDispatch = flow?.children?.giftReturnSubFlow?.dispatch;

  const handleSubmit = (formData: FormData) => {
    if (!formData?.verifyMethod) return;

    submit({
      orderNumber: formData?.orderNumber ?? '',
      orderLookupType: formData?.verifyMethod,
      ...(formData?.verifyMethod === OrderLookupType.EMAIL
        ? { customerEmail: formData.email }
        : {}),
      ...(formData?.verifyMethod === OrderLookupType.PHONE
        ? { phoneNumber: formData.phoneNumber }
        : {}),
      ...(formData?.verifyMethod === OrderLookupType.ZIPCODE
        ? { zipCode: formData.postalCode }
        : {}),
    });
  };

  const handleToNoOrderInfo = () => {
    getTrackerInstance().reportClickEvent({ eventName: EventName.clickGiftReturnNotMatch });
    giftReturnDispatch?.({
      type: 'goToFormWithoutOrderInfo',
    });
  };

  const onBack = () => {
    giftReturnDispatch?.({ type: 'goBack' });
  };

  const onValidate = ({ isValid, errorMessage }: ValidateData) => {
    if (isValid) return;
    setErrorMessage(errorMessage);
  };

  if (mobile) {
    return (
      <Stack direction='column' justify={'end'} flex={1}>
        {!isCompact && <ReturnsPageHeader shopInfo={shopInfo} />}
        <Box flex={1}></Box>
        <Box
          backgroundColor={tokenVars.Semantic.Color.Bg.Body}
          borderTopStartRadius={tokenVars.Primitive.Radius['600']}
          borderTopEndRadius={tokenVars.Primitive.Radius['600']}
          width={'100%'}
          paddingX={tokenVars.Semantic.Space.M}
          paddingY={tokenVars.Semantic.Space['2Xl']}
          overflow='hidden'
        >
          <Header title={<GiftReturnTitle />} onBack={onBack} />
          <Stack
            direction='column'
            gap='2xl'
            style={{
              height: '100%',
            }}
          >
            <Stack
              direction='column'
              align='center'
              gap='m'
              style={{
                paddingTop: tokenVars.Semantic.Space.Xl,
              }}
            >
              <Typography variant='bodyMd' color='secondary' textAlign='center'>
                {t('page.gift.description.inputHint')}
              </Typography>
              {errorMessage && (
                <Notice state='warning'>
                  <Typography variant='bodyMd' style={{ wordBreak: 'break-word' }}>
                    {errorMessage}
                  </Typography>
                </Notice>
              )}
              <Stack direction='column' gap='m' style={{ width: '100%' }}>
                <OrderLookupForm
                  onValidate={onValidate}
                  classNames={{
                    formItems: formItemsCalss,
                    changeAction: changeActions,
                  }}
                  renderActionButton={(isValid, data) => {
                    return (
                      <Box
                        marginTop={tokenVars.Semantic.Space.L}
                        marginBottom={tokenVars.Semantic.Space.M}
                      >
                        <Button
                          size='large'
                          onPress={() => {
                            handleSubmit(data);
                          }}
                          isDisabled={!isValid}
                          isLoading={isLoading}
                          isFullWidth
                        >
                          {t('page.gift.action.startGiftReturn')}
                        </Button>
                      </Box>
                    );
                  }}
                />
              </Stack>
            </Stack>
            <Stack
              direction='column'
              align='center'
              gap='m'
              style={{
                paddingBottom: tokenVars.Primitive.Size['2400'],
              }}
            >
              <Link onPress={handleToNoOrderInfo}>
                <Typography color='primary' variant='bodyLgSemibold'>
                  {t('page.gift.action.startGiftReturnHint')}
                </Typography>
              </Link>
            </Stack>
          </Stack>
        </Box>
      </Stack>
    );
  }

  return (
    <StepCard width={792} title={<GiftReturnTitle />} onBack={onBack} style={{ minHeight: 462 }}>
      <Stack
        direction='column'
        style={{
          paddingBottom: tokenVars.Semantic.Space['3Xl'],
          width: 500,
          margin: 'auto',
        }}
      >
        <Stack direction='column' align='center'>
          <Typography
            variant='bodyMd'
            textAlign='center'
            color='secondary'
            style={{ padding: `${tokenVars.Semantic.Space['2Xl']} 0` }}
          >
            {t('page.gift.description.inputHint')}
          </Typography>
          <Stack direction='column' gap='m' style={{ width: '100%' }}>
            {errorMessage && (
              <Notice state='warning'>
                <Typography variant='bodyMd' style={{ wordBreak: 'break-word' }}>
                  {errorMessage}
                </Typography>
              </Notice>
            )}
            <OrderLookupForm
              onValidate={onValidate}
              classNames={{
                formItems: formItemsCalss,
                changeAction: changeActions,
              }}
              renderActionButton={(isValid, data) => {
                return (
                  <Box
                    marginTop={tokenVars.Semantic.Space.L}
                    marginBottom={tokenVars.Semantic.Space.M}
                  >
                    <Button
                      size='large'
                      onPress={() => {
                        handleSubmit(data);
                      }}
                      isDisabled={!isValid}
                      isLoading={isLoading}
                      isFullWidth
                    >
                      {t('page.gift.action.startGiftReturn')}
                    </Button>
                  </Box>
                );
              }}
            />
          </Stack>
        </Stack>

        <Stack direction='column' align='center'>
          <Link onPress={handleToNoOrderInfo}>
            <Typography variant='bodyLgSemibold'>
              {t('page.gift.action.startGiftReturnHint')}
            </Typography>
          </Link>
        </Stack>
      </Stack>
    </StepCard>
  );
};

export default OrderLookupInput;
