export interface IOption {
  name: string;
  value: string;
}

export const Select = ({
  label,
  value,
  onChange,
  error,
  options,
  placeholder,
}: {
  label?: string;
  value?: string;
  onChange?: (value: string) => void;
  error?: string;
  options: IOption[];
  placeholder?: string;
}) => {
  return (
    <label
      style={{
        display: 'flex',
        flexDirection: 'column',
        gap: '0.5rem',
      }}
    >
      {label}
      <select onChange={(e) => onChange?.(e.target.value)} value={value}>
        {placeholder && <option value={''}>{placeholder}</option>}
        {options.map((option) => (
          <option key={option.value} value={option.value}>
            {option.name}
          </option>
        ))}
      </select>
      {error && (
        <div
          style={{
            color: 'red',
            margin: '10px 0',
            whiteSpace: 'pre',
          }}
        >
          {error}
        </div>
      )}
    </label>
  );
};
