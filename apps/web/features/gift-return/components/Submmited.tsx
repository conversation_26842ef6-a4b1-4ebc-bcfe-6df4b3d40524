import { useTranslation } from 'react-i18next';

import { Box, Icon, Link, Stack, Typography } from '@aftership/astra';
import { CheckCircleOutlined } from '@aftership/astra-icons';
import { tokenVars } from '@aftership/astra-tokens/Contract.css';
import { bodyLgSemiboldTextClassName } from '@aftership/astra-tokens/texts.css';
import { useFlow } from 'returns-logics/react';

import { StepCard } from '@/components/StepCard';
import useDevice from '@/hooks/useDevice';

const Submmited = () => {
  const { t } = useTranslation();

  const { dispatch } = useFlow();

  const { mobile } = useDevice();

  const content = (
    <Box margin={'auto'}>
      <Stack direction='column' gap='xl'>
        <Stack justify='center'>
          <Icon size='80px' source={CheckCircleOutlined} color='success' />
        </Stack>

        <Stack direction='column' gap='xl' align='center'>
          <Stack direction='column' gap='xs' align='center'>
            <Typography variant='headingXs'>
              {t('page.gift.description.submitSuccessTitle')}
            </Typography>
            <Typography variant='bodySm'>{t('page.gift.description.submitSuccessHint')}</Typography>
          </Stack>
          <Box marginTop={tokenVars.Semantic.Space.Xl}>
            <Link
              onPress={() => {
                dispatch({ type: 'GO_TO_ORDER_LOOKUP' });
              }}
              className={bodyLgSemiboldTextClassName}
            >
              {t('page.gift.action.requestAnother')}
            </Link>
          </Box>
        </Stack>
      </Stack>
    </Box>
  );

  if (mobile) {
    return (
      <Box
        backgroundColor='white'
        height={'100%'}
        paddingTop={180}
        paddingX={tokenVars.Semantic.Space.M}
      >
        {content}
      </Box>
    );
  }

  return (
    <StepCard
      hiddenHeader
      width={792}
      title={<></>}
      style={{
        minHeight: 462,
      }}
    >
      {content}
    </StepCard>
  );
};

export default Submmited;
