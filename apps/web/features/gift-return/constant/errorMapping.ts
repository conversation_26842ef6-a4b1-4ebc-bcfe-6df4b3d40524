import { GiftReturnResolution } from '@aftership/returns-logics-core';

export interface FormWithoutOrderInfoFormValues {
  productName: string;
  resolution: GiftReturnResolution;
  option: string;
  address: {
    firstName: string;
    lastName: string;
    line1: string;
    line2: string;
    country: string;
    state?: string;
    city: string;
    postalCode: string;
    phoneNumber: string;
  };
  customer: { firstName: string; lastName: string; email?: string };
  recipient: {
    firstName: string;
    lastName: string;
    email: string;
  };
  notes: string;
}

export interface FormWithOrderInfoFormValues {
  productName: string;
  resolution: GiftReturnResolution;
  option: string;
  address: {
    firstName: string;
    lastName: string;
    line1: string;
    line2: string;
    country: string;
    state?: string;
    city: string;
    postalCode: string;
    phoneNumber: string;
  };
  recipient: {
    firstName: string;
    lastName: string;
    email: string;
  };
  notes: string;
}
