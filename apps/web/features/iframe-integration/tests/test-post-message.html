<!DOCTYPE html>
<html lang='en'>
<head>
<title>test-post-message</title>
<script>window.addEventListener(
  'message',
  event => {
    const allowList = [
      'http://localhost:3000',
      'https://staging-shopper.returnscenter.com',
    ];

    if (!allowList.includes(event.origin)) {
      return;
    }
    const {data} = event;
    console.log(
      `Received message from ${event.origin}: ${JSON.stringify(data)}`
    );
  },
  false
);
</script>
</head>
<body>
    <!-- 仅用于本地测试。 -->
    <!-- <iframe src="https://staging-shopper.returnscenter.com/?hostname=krakenautodea54fe07.returnscenter.com&mode=compat" title="shopper" width="100%" height="1000px">
    </iframe> -->

    <iframe src="http://localhost:9003/?hostname=melonstest.returnscenter.io&mode=compat" title="shopper" width="100%" height="1000px">
      <!-- <iframe src="https://melonstest.returnscenter.io?mode=compat" title="shopper" width="100%" height="1000px"></iframe> -->
    </iframe>

</body>
</html>