export interface ReturnCreatedMessage {
  name: 'return.created';
  version: 'v1';
  payload: {
    /**
     * UUID without dashes in between >= 32 characters <= 32 characters
     * @example 09767741b65e483aa5d6c8d94c9f01ed
     */
    id: string;

    /**
     * Short id for return. Unique per AfterShip organization.
     * It will be likely unique for most of the time within a single store.
     * It's for store customers to identify their returns.
     * @example EUGCX4FL
     */
    rma_number: string;

    /**
     * The moment when a customer succesfully submit a return request.
     */
    created_at: string;

    order: {
      // The Identifier provided by order source platform
      external_id: string;
    };
  };
}
