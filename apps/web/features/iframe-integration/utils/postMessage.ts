import { inWhiteList } from './whiteList';

export default function postMessage(shopId: string, message: any) {
  const parentWindow = window?.parent;
  const referrer = document?.referrer;

  // 无法使用 origin，有跨域问题。
  // const parentOrigin = window?.parent?.location?.origin;

  if (parentWindow && referrer) {
    const { origin } = new URL(referrer);

    if (inWhiteList(shopId, origin)) {
      parentWindow.postMessage(message, origin);
    }
  }
}
