import { t } from 'i18next';
import { useFormContext } from 'react-hook-form';

import { Stack } from '@aftership/astra';

import FormInput from '@/components/Form/FormInput';
import FormSelect from '@/components/Form/FormSelect';

import { MissingShippingAddressFormProps } from './types';

export const MissingShippingAddressForm = ({
  control,
  countryOptions,
  stateOptions,
  hasStateField,
}: MissingShippingAddressFormProps) => {
  const { setValue } = useFormContext();
  return (
    <Stack direction='column' gap='m'>
      <Stack gap='xs'>
        <FormInput
          control={control}
          name='firstName'
          placeholder={t('missingAddress.form.firstName')}
          fullWidth={true}
        />
        <FormInput
          control={control}
          name='lastName'
          placeholder={t('missingAddress.form.lastName')}
          fullWidth={true}
        />
      </Stack>
      <FormSelect
        control={control}
        name='country'
        placeholder={t('missingAddress.form.country')}
        options={countryOptions}
        onSelectChange={() => {
          setValue('state', '');
        }}
      />
      <FormInput
        control={control}
        name='addressLine1'
        placeholder={t('missingAddress.form.addressLine1')}
        fullWidth={true}
        maxLength={200}
      />
      <FormInput
        control={control}
        name='addressLine2'
        placeholder={t('missingAddress.form.addressLine2')}
        fullWidth={true}
      />
      {hasStateField ? (
        <Stack gap='xs'>
          <Stack flex={1} direction='column'>
            <FormInput control={control} name='city' placeholder={t('missingAddress.form.city')} />
          </Stack>
          <Stack flex={1} direction='column'>
            <FormSelect
              control={control}
              name='state'
              placeholder={t('missingAddress.form.state')}
              options={stateOptions}
            />
          </Stack>
        </Stack>
      ) : (
        <FormInput
          control={control}
          name='city'
          placeholder={t('missingAddress.form.city')}
          fullWidth={true}
        />
      )}
      <FormInput
        control={control}
        name='postalCode'
        placeholder={t('missingAddress.form.postalCode')}
        fullWidth={true}
        maxLength={20}
        onInput={(e: React.ChangeEvent<HTMLInputElement>) => {
          e.currentTarget.value = e.currentTarget.value.toUpperCase();
        }}
      />
    </Stack>
  );
};
