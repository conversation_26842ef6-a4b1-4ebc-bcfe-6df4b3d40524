import { useCallback, useMemo } from 'react';

import { ValidateAddress } from '@aftership/returns-logics-core';
import { useFlow } from 'returns-logics/react';

export const useMissingAddressSubFlow = () => {
  const { children } = useFlow();
  const missingAddressSubFlow = children?.missingShippingAddressSubFlow;

  const isValidating =
    missingAddressSubFlow?.matches({
      validateAddress: 'loading',
    }) ?? false;

  const isInvalid = missingAddressSubFlow?.context?.validateStatus === 'invalid';
  const _suggestedAddress = missingAddressSubFlow?.context?.suggestedAddress;

  const suggestedAddress = useMemo<ValidateAddress | undefined>(() => {
    if (!_suggestedAddress) {
      return undefined;
    }

    return {
      addressLine1: _suggestedAddress.address_line_1,
      addressLine2: _suggestedAddress.address_line_2,
      city: _suggestedAddress.city,
      state: _suggestedAddress.state,
      postalCode: _suggestedAddress.postal_code,
      country: _suggestedAddress.country,
    };
  }, [_suggestedAddress]);

  const applySuggestedAddress = useCallback(() => {
    missingAddressSubFlow?.dispatch?.({
      type: 'useSuggestedAddress',
    });
  }, [missingAddressSubFlow]);

  return {
    missingAddressSubFlow,
    isValidating,
    isInvalid,
    suggestedAddress,
    validateStatus: missingAddressSubFlow?.context?.validateStatus,
    applySuggestedAddress,
  };
};
