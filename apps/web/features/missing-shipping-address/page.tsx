import { yupResolver } from '@hookform/resolvers/yup';
import { t } from 'i18next';
import { useEffect, useMemo, useState } from 'react';
import { FormProvider, useForm } from 'react-hook-form';

import { Notice, Stack, Typography } from '@aftership/astra';
import { useFlow } from 'returns-logics/react';

import { Button } from '@/components/Button';
import { NextButton } from '@/components/NextButton';
import { ScrollFlex } from '@/components/ScrollFlex';
import { StepCard } from '@/components/StepCard';
import useCountry from '@/hooks/useCountry';
import { useStepCardMinHeight } from '@/hooks/useStepCardMinHeight';

import { MissingShippingAddressForm } from './MissingShippingAddressForm';
import { useMissingAddressSubFlow } from './hooks/useMissingAddressSubFlow';
import { addressSchema } from './schema';
import { MissingAddressFormValues } from './types';

import { getFullShippingAddress } from '../return-detail/utils/shipment';

export function MissingShippingAddressPage() {
  const { dispatch } = useFlow();
  const minHeight = useStepCardMinHeight();
  const {
    missingAddressSubFlow,
    isValidating,
    isInvalid,
    suggestedAddress,
    applySuggestedAddress,
  } = useMissingAddressSubFlow();
  const {
    options: countryOptions,
    findHasState,
    getStateOption,
  } = useCountry(missingAddressSubFlow?.context?.countryMap);

  const [scrollBoxElement, setScrollBoxElement] = useState<HTMLDivElement | null>(null);

  const form = useForm<MissingAddressFormValues>({
    mode: 'all',
    resolver: yupResolver(addressSchema()) as any,
    context: {
      countryMap: missingAddressSubFlow?.context?.countryMap,
    },
  });

  const { control, watch, handleSubmit, reset, getValues } = form;

  const onSubmit = handleSubmit((values) => {
    missingAddressSubFlow?.dispatch?.({
      type: 'submit',
      data: values,
    });
  });

  const handleSuggestedAddress = () => {
    applySuggestedAddress();
    reset({
      ...getValues(),
      ...suggestedAddress,
    });
  };

  const countryCode = watch('country');
  const stateOptions = useMemo(() => getStateOption(countryCode), [countryCode, getStateOption]);
  const showStateField = findHasState(countryCode);

  useEffect(() => {
    if (isInvalid && scrollBoxElement) {
      scrollBoxElement.scrollTo({
        top: 0,
        behavior: 'smooth',
      });
    }
  }, [isInvalid, scrollBoxElement]);

  return (
    <StepCard
      width={'560px'}
      title={t('missingAddress.page.title')}
      onBack={() => {
        dispatch({
          type: 'BACK_TO_ORDER_LOOKUP',
        });
      }}
      height={minHeight}
    >
      <ScrollFlex innerRef={setScrollBoxElement}>
        <Stack gap='m' direction='column'>
          {isInvalid && (
            <Notice state='warning'>
              <Stack direction='column' gap='m'>
                <Stack direction='column'>
                  <Typography variant='bodyLgSemibold' as='div'>
                    {t('missingAddress.validate.failure.title')}
                  </Typography>
                  <Typography variant='bodyMd'>
                    {t(
                      suggestedAddress
                        ? 'missingAddress.validate.failure.description'
                        : 'missingAddress.validate.failure.description.noSuggestedAddress',
                    )}
                  </Typography>
                  {suggestedAddress && (
                    <Typography variant='bodyLgSemibold'>
                      {getFullShippingAddress(suggestedAddress)}
                    </Typography>
                  )}
                </Stack>
                {suggestedAddress && (
                  <div>
                    <Button
                      onPress={handleSuggestedAddress}
                      variant='outlined'
                      color='default'
                      size='small'
                    >
                      {t('missingAddress.validate.failure.actionText')}
                    </Button>
                  </div>
                )}
              </Stack>
            </Notice>
          )}
          <Stack gap='2xs' direction='column'>
            <Typography variant='bodyLgSemibold'>{t('missingAddress.page.subTitle')}</Typography>
            <Typography variant='bodyMd' color='secondary'>
              {t('missingAddress.page.description')}
            </Typography>
          </Stack>
          <FormProvider {...form}>
            <MissingShippingAddressForm
              control={control}
              countryOptions={countryOptions}
              stateOptions={stateOptions}
              hasStateField={showStateField}
            />
          </FormProvider>
        </Stack>
      </ScrollFlex>
      <NextButton isDisabled={false} isLoading={isValidating} onPress={onSubmit} />
    </StepCard>
  );
}
