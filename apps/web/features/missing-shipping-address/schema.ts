import { t } from 'i18next';
import * as yup from 'yup';

export const addressSchema = () =>
  yup.object({
    firstName: yup.string(),
    lastName: yup.string().required(t('missingAddress.form.validate.lastName')),
    country: yup.string().required(t('missingAddress.form.validate.country')),
    addressLine1: yup.string().required(t('missingAddress.form.validate.addressLine1')),
    addressLine2: yup.string().nullable(),
    state: yup.string().when(['country', '$countryMap'], ([country, countryMap], schema) => {
      if (countryMap && countryMap[country]?.sublist?.length > 0) {
        return schema.required(t('missingAddress.form.validate.state'));
      }
      return schema.notRequired();
    }),
    city: yup.string().required(t('missingAddress.form.validate.city')),
    postalCode: yup.string().required(t('missingAddress.form.validate.postalCode.required')),
  });
