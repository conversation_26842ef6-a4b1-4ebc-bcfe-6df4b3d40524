import { Control } from 'react-hook-form';

import { Option } from '@/components/Form/FormSelect';

export interface MissingShippingAddressFormProps {
  control: Control<MissingAddressFormValues>;
  countryOptions: Array<Option>;
  stateOptions: Array<Option>;
  hasStateField: boolean;
}

export interface MissingAddressFormValues {
  firstName?: string;
  lastName: string;
  country: string;
  addressLine1: string;
  addressLine2: string;
  city: string;
  state: string;
  postalCode: string;
}
