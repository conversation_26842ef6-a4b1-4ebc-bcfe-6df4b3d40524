import { t } from 'i18next';

import { Box, Typography, UnstyledButton, clsx } from '@aftership/astra';
import { OrderLookupType } from '@aftership/returns-logics-core';

import useDevice from '@/hooks/useDevice';

type ChangeVerifyMethodActionProps = {
  currentVerifyMethod: OrderLookupType | null;
  allActiveVerifyMethods: OrderLookupType[];
  onUpdateVarifyMethod: (method: OrderLookupType) => void;
  className?: string;
};

export const getVerifyMethodConfig = () => ({
  [OrderLookupType.EMAIL]: {
    actionLabel: t('verifyMethod.email'),
    errorLabel: t('verifyMethod.emailAddress'),
  },
  [OrderLookupType.PHONE]: {
    actionLabel: t('verifyMethod.phoneNumber'),
    errorLabel: t('verifyMethod.phoneNumber'),
  },
  [OrderLookupType.ZIPCODE]: {
    actionLabel: t('verifyMethod.zipcode'),
    errorLabel: t('verifyMethod.zipcode'),
  },
});

export const ChangeVerifyMethodAction = ({
  currentVerifyMethod,
  allActiveVerifyMethods,
  onUpdateVarifyMethod,
  className,
}: ChangeVerifyMethodActionProps) => {
  const isMobile = useDevice().mobile;
  const remainingMethods = allActiveVerifyMethods.filter(
    (method) => method !== currentVerifyMethod,
  );

  const verifyMethodConfig = getVerifyMethodConfig();

  if (remainingMethods.length > 0) {
    return (
      <Box className={clsx(className)} margin={'auto'}>
        <Typography
          variant={isMobile ? 'bodyMdSemibold' : 'bodySm'}
          color='disabled'
          textAlign='center'
        >
          {t('verifyMethod.verifyBy')}{' '}
          {remainingMethods.map((method, index) => (
            <span key={method}>
              <UnstyledButton onPress={() => onUpdateVarifyMethod(method)}>
                <Typography variant={isMobile ? 'bodyMdSemibold' : 'bodySm'} color='primary'>
                  {verifyMethodConfig[method].actionLabel}
                </Typography>
              </UnstyledButton>
              {index < remainingMethods.length - 1 && ` ${t('common.or')} `}
            </span>
          ))}
        </Typography>
      </Box>
    );
  }

  return null;
};
