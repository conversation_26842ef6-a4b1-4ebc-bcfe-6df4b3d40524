import { useTranslation } from 'react-i18next';

import { Checkbox, Link, Stack, Typography, UnstyledButton } from '@aftership/astra';
import { bodySmDefaultText } from '@aftership/astra-tokens/texts.css';
import { usePreviewContext } from '@aftership/preview-kit/client';

import { useClickwrapContext } from '@/features/returns/components/ClickwrapProvider';

import ClickWrapModal from './ClickwrapModal';

import RichTextRender from '../../../components/RichTextRender/RichTextRender';

interface IClickWrapProps {
  isClickwrapChecked: boolean;
  clickwrapError?: boolean;
}

const ClickWrap = ({ isClickwrapChecked, clickwrapError }: IClickWrapProps) => {
  const { t } = useTranslation();

  const {
    isClickwrapPopupOpen,
    setClickwrapPopupOpen,
    clickwrapConfig,
    setClickwrapChecked,
    setWarningMsgVisible,
  } = useClickwrapContext();

  const { isPreview } = usePreviewContext();

  const handleViewDetail = () => {
    setClickwrapPopupOpen(true);
  };

  const handleCloseClickwrapModal = () => {
    setClickwrapPopupOpen(false);
  };

  const onClickwrapChange = (selected: boolean) => {
    setClickwrapChecked(selected);
    setWarningMsgVisible(!selected);
  };

  return (
    <Stack direction='column' gap='2xs' style={{ width: '100%' }}>
      <Stack align='center' gap='none'>
        <Checkbox isSelected={isClickwrapChecked} onChange={onClickwrapChange}>
          <Typography variant='bodySm'>
            <RichTextRender
              componentType='style'
              content={`.clickwrap-summary p {display: inline; font-size: ${bodySmDefaultText.fontSize}; line-height: ${bodySmDefaultText.lineHeight}; font-family: ${bodySmDefaultText.fontFamily}; font-weight:${bodySmDefaultText.fontWeight}; display: inline; letter-spacing: ${bodySmDefaultText.letterSpacing}} `}
            />
            <RichTextRender
              componentType='span'
              className='clickwrap-summary'
              content={t('page.landing.termsConditions')}
            />
          </Typography>
          &nbsp;
          {!!clickwrapConfig?.popup_enabled && (
            <UnstyledButton>
              <Link onPress={handleViewDetail}>
                <Typography variant='bodySm'>{t('page.landing.viewDetail')}</Typography>
              </Link>
            </UnstyledButton>
          )}
        </Checkbox>
      </Stack>
      {clickwrapError && (
        <Typography variant='bodySm' color='error'>
          {t('page.landing.CheckAgreement')}
        </Typography>
      )}
      <ClickWrapModal
        isOpen={isClickwrapPopupOpen}
        onClose={handleCloseClickwrapModal}
        title={clickwrapConfig?.popup_title}
        content={clickwrapConfig?.popup_body}
        isPreview={isPreview}
      />
    </Stack>
  );
};

export default ClickWrap;
