import { Box } from '@aftership/astra';
import { semanticTokenVar } from '@aftership/astra-tokens/semantic-token/Contract.css';

import { Modal } from '@/components/Modal';
import { useShopInfo } from '@/features/returns/hooks/useShopInfo';
import useDevice from '@/hooks/useDevice';

import RichTextRender from '../../../components/RichTextRender/RichTextRender';
import { clickwrapMobile } from '../css/style.css';

const { Space } = semanticTokenVar;

interface IClickWrapModalProps {
  isOpen: boolean;
  onClose: VoidFunction;
  title?: string;
  content?: string;
  isPreview: boolean;
}

const ClickWrapModal = ({
  isOpen,
  onClose,
  title,
  content = '',
  isPreview,
}: IClickWrapModalProps) => {
  const isMobile = useDevice().mobile;

  const shop = useShopInfo();

  const clickWrapContent = (
    <>
      <RichTextRender componentType='style' content={`#clickwrapBody * { margin: revert }`} />
      <RichTextRender
        id='clickwrapBody'
        componentType='div'
        content={content}
        style={{ fontFamily: shop?.returns_page_body_font }}
      />
    </>
  );

  if (isMobile) {
    return (
      <Modal isOpen={isOpen} title={title} disableFocusManagement={isPreview} onClose={onClose}>
        <Box className={clickwrapMobile}>{clickWrapContent}</Box>
      </Modal>
    );
  }

  return (
    <Modal isOpen={isOpen} title={title} disableFocusManagement={isPreview} onClose={onClose}>
      <Box maxHeight={'70vh'} paddingX={Space.Xl} overflow='auto'>
        {clickWrapContent}
      </Box>
    </Modal>
  );
};

export default ClickWrapModal;
