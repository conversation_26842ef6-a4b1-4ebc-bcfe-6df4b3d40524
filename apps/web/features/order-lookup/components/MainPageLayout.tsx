import React from 'react';

import { Box, Stack } from '@aftership/astra';
import { semanticTokenVar } from '@aftership/astra-tokens/semantic-token/Contract.css';

import { Card } from '@/components/Card';
import { HomeCarousel } from '@/features/order-lookup/components/HomeCarousel';
import useDevice from '@/hooks/useDevice';

import { desktopContainerClassName } from './MainPageLayout.css';

const { Color, Space } = semanticTokenVar;

interface MainPageLayoutProps {
  children: React.ReactNode;
}

const MainPageLayout = ({ children }: MainPageLayoutProps) => {
  const isMobile = useDevice().mobile;

  if (isMobile) {
    return (
      <Stack flex={1} direction='column'>
        <Box flex={1} />
        <Box
          backgroundColor={Color.Bg.Body}
          borderTopStartRadius={Space.Xl}
          borderTopEndRadius={Space.Xl}
        >
          <Stack
            style={{
              minHeight: 460,
              paddingInline: Space.M,
              paddingBlockStart: Space['2Xl'],
              paddingBlockEnd: Space.Xl,
            }}
          >
            {children}
          </Stack>
        </Box>
      </Stack>
    );
  }

  return (
    <Card padding={Space['2Xl']} className={desktopContainerClassName}>
      <Stack direction='row' gap='2xl' align='center'>
        <Box width={336}>{children}</Box>
        <HomeCarousel />
      </Stack>
    </Card>
  );
};

export default MainPageLayout;
