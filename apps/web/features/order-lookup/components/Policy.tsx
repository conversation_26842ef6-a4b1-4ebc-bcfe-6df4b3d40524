import React, { <PERSON> } from 'react';
import { useTranslation } from 'react-i18next';

import { Box, Link, Stack, Typography } from '@aftership/astra';

import ScrollView from '@/components/ScrollView';
import { useGetPrefiedLink } from '@/features/returns/hooks/useUniversalRouting';

interface Props {
  policyTextHeight: number;
  externalReturnPolicyPage?: boolean;
  policyUrl?: string;
  disabled?: boolean;
}

const Policy: FC<Props> = ({
  policyTextHeight,
  externalReturnPolicyPage,
  policyUrl,
  disabled = false,
}) => {
  const { t } = useTranslation();

  const privatePolicyLink = useGetPrefiedLink('/return-policy'); // 兼顾 app proxy 模式
  return (
    <Box>
      <ScrollView style={{ maxHeight: policyTextHeight }}>
        <Typography variant='bodySm' as='p' color='secondary' style={{ textAlign: 'center' }}>
          {t('page.description.acceptReturnsPolicy')}
        </Typography>
      </ScrollView>
      <Stack justify='center'>
        {externalReturnPolicyPage ? (
          policyUrl && (
            <Link isDisabled={disabled} href={policyUrl} target='_blank'>
              <Typography variant='bodySm'>{t('page.landing.viewFullPolicy')}</Typography>
            </Link>
          )
        ) : (
          <Link isDisabled={disabled} href={privatePolicyLink} target='_blank'>
            <Typography variant='bodySm'>{t('page.landing.viewFullPolicy')}</Typography>
          </Link>
        )}
      </Stack>
    </Box>
  );
};

export default Policy;
