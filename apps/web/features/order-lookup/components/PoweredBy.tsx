import React, { <PERSON> } from 'react';

import { <PERSON>, Stack, Typography } from '@aftership/astra';
import { tokenVars } from '@aftership/astra-tokens/Contract.css';

const { Color } = tokenVars.Semantic;

interface Props {
  isPreview?: boolean;
}

const PoweredBy: FC<Props> = () => {
  return (
    <Stack justify='center'>
      <Link
        href='https://www.aftership.com/returns?utm_source=returns_page&utm_medium=referral&utm_content=poweredby'
        target='_blank'
        showUnderline={false}
        style={{ color: Color.Text.Primary }}
      >
        <Typography variant='bodySm'>Powered by AfterShip</Typography>
      </Link>
    </Stack>
  );
};

export default PoweredBy;
