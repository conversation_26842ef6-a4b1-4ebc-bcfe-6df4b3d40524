import { t } from 'i18next';
import React from 'react';
import { Control, Controller } from 'react-hook-form';

import { Stack } from '@aftership/astra';

import FormInput from '@/components/Form/FormInput';
import PhoneInput from '@/components/PhoneInput';

export const PhoneNumberInput = ({ control }: { control: Control<any> }) => (
  <Stack gap='xs'>
    <Controller
      control={control}
      name='callingCodeData'
      render={({ field: { onChange, value } }) => {
        return <PhoneInput value={value} onChange={onChange} />;
      }}
    />
    <FormInput
      control={control}
      name='phoneNumber'
      autoComplete='off'
      isClearable
      placeholder={t('page.landing.phoneNumber')}
      fullWidth
      hiddenError
    />
  </Stack>
);
