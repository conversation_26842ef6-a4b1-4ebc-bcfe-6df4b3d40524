import { style } from '@vanilla-extract/css';

import { semanticTokenVar } from '@aftership/astra-tokens/semantic-token/Contract.css';

const { Space } = semanticTokenVar;

export const clickwrapMobile = style({
  flex: 1,
  overflowY: 'auto',
  display: 'flex',
  paddingLeft: Space.M,
  paddingRight: Space.M,
  paddingBottom: Space.M,
  flexDirection: 'column',
  maxHeight: 'max-content',
  gap: Space['3Xl'],
});

export const carouselImage = style({
  objectFit: 'cover',
});

export const orderFormItems = style({
  display: 'flex',
  flexDirection: 'column',
  gap: Space.Xs,
});

export const changeActions = style({
  paddingTop: Space.Xs,
});
