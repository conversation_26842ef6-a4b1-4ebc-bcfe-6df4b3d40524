import { useCallback, useEffect } from 'react';

import {
  OrderLookupInputError,
  Orders,
  RequestErrorType,
  SubmitPayload,
} from '@aftership/returns-logics-core';
import { useFlow } from 'returns-logics/react';

interface Props {
  onError?: (error: RequestErrorType) => void;
  onSuccess?: (order?: Orders) => void;
  onInputError?: (error: OrderLookupInputError) => void;
}

export const useFindOrder = ({ onError, onSuccess, onInputError }: Props) => {
  const { children } = useFlow();

  const orderLookupSubFlow = children?.orderLookupSubFlow;

  useEffect(() => {
    if (!orderLookupSubFlow?.on || !onError) return;

    const { unsubscribe } = orderLookupSubFlow.on('error', ({ data }) => {
      onError(data.error);
    });

    return unsubscribe;
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [orderLookupSubFlow]);

  useEffect(() => {
    if (!orderLookupSubFlow?.on || !onInputError) return;

    const { unsubscribe } = orderLookupSubFlow.on('inputError', ({ data }) => {
      onInputError(data.error);
    });

    return unsubscribe;
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [orderLookupSubFlow]);

  useEffect(() => {
    if (!orderLookupSubFlow?.on || !onSuccess) return;

    const { unsubscribe } = orderLookupSubFlow.on('success', ({ data }) => {
      onSuccess(data.order?.order);
    });

    return unsubscribe;
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [orderLookupSubFlow]);

  return {
    findOrder: useCallback(
      (data: SubmitPayload) => {
        orderLookupSubFlow?.dispatch({ type: 'SUBMIT', data });
      },
      // eslint-disable-next-line react-hooks/exhaustive-deps
      [orderLookupSubFlow?.dispatch],
    ),
    isLoading: orderLookupSubFlow?.currentStep.isLoading,
  };
};
