import { useSearchParams } from 'next/navigation';

import OrderList from '@/features/app-proxy-order-list/OrderList';
import { Order } from '@/features/app-proxy-order-list/types';
import MainPage from '@/features/order-lookup/components/MainPage';
import { IQueryParamOrderInfo } from '@/features/returns/types/externalConfig';
import decodeBase64JsonString from '@/features/returns/utils/decodeBase64JsonString';

// // 解析 qs 参数，如果有 order number 和 email，跳转到查询页面而不是 order list 页面
const useGetIsRedirectToMainPage = () => {
  const params = useSearchParams();
  const qs = params ? params.get('qs') : null;

  if (!qs) {
    return false;
  }

  const { order_number, email, postal_code } =
    decodeBase64JsonString<IQueryParamOrderInfo>(qs) || {};

  return order_number && (email || postal_code);
};

interface IHomeProps {
  isAppProxy: boolean;
  orders?: Order[];
}

export function OrderLookupPage({ isAppProxy, orders = [] }: IHomeProps) {
  const isRedirectToMainPage = useGetIsRedirectToMainPage();

  if (!isAppProxy) {
    return <MainPage />;
  }

  const isRedirectToOrderListInAppProxy = orders && orders.length > 0 && !isRedirectToMainPage;

  return (
    <>
      {/* 恢复此页面下 shopify 的 footer 标签 */}
      <style
        dangerouslySetInnerHTML={{
          __html: `footer.footer {display: block;}`,
        }}
      />
      {isRedirectToOrderListInAppProxy ? <OrderList orders={orders} /> : <MainPage />}
    </>
  );
}
