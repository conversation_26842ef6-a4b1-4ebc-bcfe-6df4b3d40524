import { PropsWithChildren } from 'react';

import { useListenNavigateTo } from '../hooks/useListenNavigateTo';
import { useListenPreviewDataReceive } from '../hooks/useListenPreviewDataReceive';
import useListenUpdateView from '../hooks/useListenUpdateView';
import { useLogDataFromBridge } from '../hooks/useLogDataFromBridge';

const PreviewContainer = ({ children }: PropsWithChildren<{}>) => {
  useLogDataFromBridge();

  useListenPreviewDataReceive();

  useListenNavigateTo();
  useListenUpdateView();

  return <div>{children}</div>;
};

export default PreviewContainer;
