/* eslint-disable @typescript-eslint/naming-convention */
export enum PreviewSectionName {
  // Main page
  orderNumberPrompt = 'mainOrderNumberInput',
  emailPrompt = 'mainOrderEmailInput',
  MAIN_ORDER_POSTAL_CODE_INPUT = 'mainOrderPostalCodeInput',
  MAIN_ORDER_PHONE_NUMBER_INPUT = 'mainOrderPhoneNumberInput',
  MAIN_FORM_TITLE_TEXT = 'mainFormTitleText',
  MAIN_FIND_YOUR_ORDER_BUTTON = 'mainFindYourOrderButton',
  MAIN_GIFT_RETURN_BUTTON = 'mainGiftReturnButton',
  MAIN_POLICY_SUMMARY_TEXT = 'mainPolicySummaryText',
  MAIN_CLICKWRAP = 'mainClickwrap',
  MAIN_MARKETING_ASSETS = 'mainMarketingAssets',

  // ReturnList
  RETURN_LIST_CREATE_RETURN_BUTTON = 'returnListCreateReturnButton',

  // RequestReturns
  REQUEST_RETURNS_PAGE_TITLE = 'requestReturnsPageTitle',
  NON_RETURNABLE_SECTION = 'nonReturnableSection',
  ITEM_EXCHANGE_OR_REFUND_TITLE = 'itemExchangeOrRefundTitle',
  ITEM_EXCHANGE_OR_REFUND_CHOOSE_EXCHANGE_INSTRUCTION = 'itemExchangeOrRefundChooseExchangeInstruction',
  ITEM_EXCHANGE_OR_REFUND_CHOOSE_EXCHANGE_COMMENT = 'itemExchangeOrRefundChooseExchangeComment',
  ITEM_EXCHANGE_OR_REFUND_CHOOSE_EXCHANGE_COMMENT_INSTRUCTION = 'itemExchangeOrRefundChooseExchangeCommentInstruction',

  // Resolution
  RESOLUTION_PAGE_TITLE = 'resolutionPageTitle',

  // Shipping
  SHIPPING_PAGE_TITLE = 'shippingPageTitle',
  SHIPPING_ITEM = 'shippingItem',

  // Returns Request Review
  REQUEST_REVIEW_PAGE_TITLE = 'requestReviewPageTitle',
  RETURN_ITEMS_SECTION_TITLE = 'returnItemsSectionTitle',
  // RETURN_REASON_LABEL = 'returnReasonLabel',
  EXCHANGE_ITEMS_SECTION_TITLE = 'exchangeItemsSectionTitle',
  RESOLUTION_SECTION_TITLE = 'resolutionSectionTitle',
  SHIPPING_SECTION_TITLE = 'shippingSectionTitle',
  REVIEW_SUMMARY_SECTION = 'reviewSummarySection',

  // Refund Method
  REFUND_METHOD_PAGE_TITLE = 'refundMethodPageTitle',

  // Return Detail
  RETURN_DETAIL_STATUS_TITLE = 'returnDetailStatusTitle',
  RETURN_DETAIL_STATUS_DESCRIPTION = 'returnDetailStatusDescription',
}
