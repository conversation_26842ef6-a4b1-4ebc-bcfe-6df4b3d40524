import { <PERSON>, <PERSON><PERSON>, <PERSON>, Typography } from '@aftership/astra';

import FormInput from '@/components/Form/FormInput';
import ClickWrap from '@/features/order-lookup/components/Clickwrap';
import Policy from '@/features/order-lookup/components/Policy';
import { PhoneNumberInput } from '@/features/order-lookup/components/ValidateInput';

import { PreviewSectionName } from './PreviewSectionName';
import { withPreviewSection } from './withPreviewSection';

export const OrderNumberTextField = withPreviewSection(
  FormInput,
  PreviewSectionName.orderNumberPrompt,
);

export const EmailTextField = withPreviewSection(FormInput, PreviewSectionName.emailPrompt);

export const PostalCodeTextField = withPreviewSection(
  FormInput,
  PreviewSectionName.MAIN_ORDER_POSTAL_CODE_INPUT,
);
export const PhoneNumberTextField = withPreviewSection(
  PhoneNumberInput,
  PreviewSectionName.MAIN_ORDER_PHONE_NUMBER_INPUT,
);

export const OrderLookupFormTitleText = withPreviewSection(
  Typography,
  PreviewSectionName.MAIN_FORM_TITLE_TEXT,
);

export const OrderLookupSubmitButton = withPreviewSection(
  Button,
  PreviewSectionName.MAIN_FIND_YOUR_ORDER_BUTTON,
);

export const MainGiftReturnButton = withPreviewSection(
  Link,
  PreviewSectionName.MAIN_GIFT_RETURN_BUTTON,
);

export const PolicyWithPreviewSection = withPreviewSection(
  Policy,
  PreviewSectionName.MAIN_POLICY_SUMMARY_TEXT,
);

export const ClickWrapWithPreviewSection = withPreviewSection(
  ClickWrap,
  PreviewSectionName.MAIN_CLICKWRAP,
);

export const HomeCarouselWrapperWithPreviewSection = withPreviewSection(
  Box,
  PreviewSectionName.MAIN_MARKETING_ASSETS,
);

export const RequestReturnsCardTitleText = withPreviewSection(
  Typography,
  PreviewSectionName.REQUEST_RETURNS_PAGE_TITLE,
);

export const NonReturnableTitleText = withPreviewSection(
  Typography,
  PreviewSectionName.NON_RETURNABLE_SECTION,
);

export const ResolutionCardTitleText = withPreviewSection(
  Typography,
  PreviewSectionName.RESOLUTION_PAGE_TITLE,
);

export const ReturnMethodPageTitleText = withPreviewSection(
  Typography,
  PreviewSectionName.SHIPPING_PAGE_TITLE,
);

export const ReturnMethodItemBox = withPreviewSection(Box, PreviewSectionName.SHIPPING_ITEM);

export const RequestReviewCardTitleText = withPreviewSection(
  Typography,
  PreviewSectionName.REQUEST_REVIEW_PAGE_TITLE,
);

export const ReturnItemsSectionTitleText = withPreviewSection(
  Typography,
  PreviewSectionName.RETURN_ITEMS_SECTION_TITLE,
);

export const ExchangeItemsSectionTitleText = withPreviewSection(
  Typography,
  PreviewSectionName.EXCHANGE_ITEMS_SECTION_TITLE,
);

export const ReturnMethodSectionTitleText = withPreviewSection(
  Typography,
  PreviewSectionName.SHIPPING_SECTION_TITLE,
);

export const ReturnItemExchangeOrRefundTitleText = withPreviewSection(
  Typography,
  PreviewSectionName.ITEM_EXCHANGE_OR_REFUND_TITLE,
);

export const VariantInstructionTitleText = withPreviewSection(
  Typography,
  PreviewSectionName.ITEM_EXCHANGE_OR_REFUND_CHOOSE_EXCHANGE_INSTRUCTION,
);

export const CommentTitleText = withPreviewSection(
  Typography,
  PreviewSectionName.ITEM_EXCHANGE_OR_REFUND_CHOOSE_EXCHANGE_INSTRUCTION,
);

export const CommentInsTitleText = withPreviewSection(
  Typography,
  PreviewSectionName.ITEM_EXCHANGE_OR_REFUND_CHOOSE_EXCHANGE_COMMENT_INSTRUCTION,
);
export const RefundMethodPageTitleText = withPreviewSection(
  Typography,
  PreviewSectionName.REFUND_METHOD_PAGE_TITLE,
);

export const ReturnListCreateReturnButton = withPreviewSection(
  Link,
  PreviewSectionName.RETURN_LIST_CREATE_RETURN_BUTTON,
);

export const ReturnDetailStatusTitleText = withPreviewSection(
  Typography,
  PreviewSectionName.RETURN_DETAIL_STATUS_TITLE,
);

export const ReturnDetailStatusTitleDescription = withPreviewSection(
  Typography,
  PreviewSectionName.RETURN_DETAIL_STATUS_DESCRIPTION,
);
