import { useEffect } from 'react';

import { EventName, NavigateToType, PreviewMessageData } from '@aftership/preview-kit/business/rc';
import { useFlow } from 'returns-logics/react';

import { useMessageBridge } from '@/features/returns/hooks/useMessageBridge';
import { useUniversalRouting } from '@/features/returns/hooks/useUniversalRouting';

export const useHandleNavigateEvent = () => {
  const flow = useFlow();

  const { navigate } = useUniversalRouting();

  const handleNavigateToPage = (data: PreviewMessageData[EventName.NavigateTo]) => {
    const { type } = data;

    switch (type) {
      case NavigateToType.OrderLookup:
        flow.dispatch({ type: 'GO_TO_ORDER_LOOKUP' });
        break;
      case NavigateToType.ReturnList:
        flow.dispatch({ type: 'GO_TO_RETURN_LIST' });
        break;
      case NavigateToType.RequestReturns:
        flow.dispatch({ type: 'GO_TO_ITEM_SELECTION' });
        break;
      case NavigateToType.GiftReturn:
        flow.dispatch({ type: 'GO_TO_GIFT_RETURN' });
        break;
      case NavigateToType.RefundMethod:
        flow.dispatch({ type: 'GO_TO_RESOLUTION' });
        break;
      case NavigateToType.ReturnResolution:
        flow.dispatch({ type: 'GO_TO_RESOLUTION' });
        break;
      case NavigateToType.ReturnResolutionReplace:
        flow.dispatch({ type: 'GO_TO_REPLACE' });
        break;
      case NavigateToType.ReturnMethod:
        flow.dispatch({ type: 'GO_TO_RETURN_METHOD' });
        break;
      case NavigateToType.ReturnPolicy:
        navigate({ pathname: '/return-policy' });
        break;
      case NavigateToType.OnStoreBanner:
        navigate({ pathname: '/exchange/preview' });
        break;
      case NavigateToType.ReturnReview:
        flow.dispatch({ type: 'GO_TO_REVIEW_PREVIEW' });
        break;
      case NavigateToType.ReturnDetail:
        flow.dispatch({ type: 'GO_TO_RETURN_DETAIL', data: { rmaId: 'test_rma_id' } });
        break;
      default:
        break;
    }
  };

  return handleNavigateToPage;
};
export const useListenNavigateTo = () => {
  const messageBridge = useMessageBridge();

  const handleNavigateToPage = useHandleNavigateEvent();

  useEffect(() => {
    const removeEventListener = messageBridge.onMessage({
      type: EventName.NavigateTo,
      callback: (data: PreviewMessageData[EventName.NavigateTo]) => {
        // eslint-disable-next-line no-console
        console.log(
          '%c [ 💬 client - receive navigate event ]',
          'font-size:13px; background:pink; color:#bf2c9f;',
          data,
        );
        handleNavigateToPage(data);
      },
    });
    return () => {
      removeEventListener();
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [handleNavigateToPage]);
};
