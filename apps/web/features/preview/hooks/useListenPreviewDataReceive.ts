import { useEffect, useRef } from 'react';
import { useTranslation } from 'react-i18next';

import {
  EventName,
  MockOrderCondition,
  PayloadInUpdatePreviewData,
  PreviewMessageData,
  UpdatePreviewDataType,
} from '@aftership/preview-kit/business/rc';
import { NonReturnableReason, waitFor } from '@aftership/returns-logics-core';
import { useFlow } from 'returns-logics/react';

import { useAppTheme } from '@/features/returns/components/AppThemeProvider';
import { useMessageBridge } from '@/features/returns/hooks/useMessageBridge';
import { I18NEXT_NAMESPACE } from '@/i18n/utils';

const EFA_ON_STORE_BANNER_CONFIG = 'EFA_ON_STORE_BANNER_CONFIG';

export const useListenPreviewDataReceive = () => {
  const { ref: mainFlowRef, children } = useFlow();
  const { i18n } = useTranslation();
  // fixme: 临时处理，后续记得优化掉
  const childrenRef = useRef(children);
  childrenRef.current = children;

  const { updateTheme } = useAppTheme();

  const handleEFAOnStoreBanner = (data: PreviewMessageData[EventName.UpdatePreviewData]) => {
    const content = JSON.stringify({ ...data.payload, is_efa_on_store_banner_preview: true });
    sessionStorage.setItem(EFA_ON_STORE_BANNER_CONFIG, content);
    window.postMessage(content);
  };

  const handlePreviewData = async (data: PreviewMessageData[EventName.UpdatePreviewData]) => {
    switch (data.type) {
      case UpdatePreviewDataType.UpdateClickWrap:
        mainFlowRef.send({
          type: 'PREVIEW_UPDATE_CONTEXT',
          data: { type: UpdatePreviewDataType.UpdateClickWrap, value: data.payload },
        });
        break;
      case UpdatePreviewDataType.UpdateI18NextResource:
        i18n.addResourceBundle(i18n.language, I18NEXT_NAMESPACE, data.payload, false, true);
        i18n.changeLanguage(i18n.language);
        break;
      case UpdatePreviewDataType.UpdateShopTheme: {
        const typePayload =
          data.payload as PayloadInUpdatePreviewData[UpdatePreviewDataType.UpdateShopTheme];

        updateTheme({
          primaryFont: typePayload.primary_font,
          secondaryFont: typePayload.body_font,
          primaryColor: typePayload.theme_color,
        });
        mainFlowRef.send({
          type: 'PREVIEW_UPDATE_CONTEXT',
          data: {
            type: UpdatePreviewDataType.UpdateShopTheme,
            value: {
              returns_page_primary_font: typePayload.primary_font,
              returns_page_body_font: typePayload.body_font,
              theme_color: typePayload.theme_color,
            },
          },
        });
        break;
      }
      case UpdatePreviewDataType.UpdateEFAOnStoreBanner:
        handleEFAOnStoreBanner(data);
        break;
      case UpdatePreviewDataType.UpdateOrderByCondition: {
        const payload =
          data.payload as PayloadInUpdatePreviewData[UpdatePreviewDataType.UpdateOrderByCondition];
        const mainFlowState = await waitFor(
          mainFlowRef,
          (state) => !!state.children.itemSelectionSubFlow,
        );

        const itemSelectionSubFlowRef = mainFlowState.children.itemSelectionSubFlow;

        await waitFor(itemSelectionSubFlowRef!, (state) => {
          return state.matches('waitingForSelectItem');
        });
        let reason: NonReturnableReason;
        switch (payload.condition) {
          case MockOrderCondition.DEFAULT:
          case MockOrderCondition.OUT_OF_RETURN_WINDOW:
            reason = NonReturnableReason.ReturnWindowByOrderDate;
            break;
          case MockOrderCondition.MULTIPLE_RETURNS:
            reason = NonReturnableReason.MultipleReturns;
            // 还要改 orders 信息
            mainFlowRef.send({
              type: 'PREVIEW_UPDATE_CONTEXT',
              data: { type: 'set_order_ineligibilities', value: 'multiple_returns' },
            });
            break;
          case MockOrderCondition.ALREADY_REQUESTED:
            reason = NonReturnableReason.ReturnableQuantity;
            break;
          case MockOrderCondition.NOT_COMPLY_RETURN_POLICY:
            reason = NonReturnableReason.BlockByItemDiscountPercentage;
            break;
          case MockOrderCondition.ITEM_UNFULFILLED:
            reason = NonReturnableReason.UnfulfilledItem;
            break;
        }
        itemSelectionSubFlowRef!.send({
          type: 'PREVIEW_UPDATE_NON_RETURN_REASON',
          data: { reason: reason },
        });

        break;
      }

      case UpdatePreviewDataType.UpdatePolicyContent:
        break;
      case UpdatePreviewDataType.UpdateReturnDetailStatus:
        childrenRef.current.returnDetailSubFlow?.dispatch?.({
          type: 'UPDATE_RETURN_DETAIL_IN_PREVIEW',
          data: data.payload as PayloadInUpdatePreviewData['update_return_detail_status'],
        });
        break;
      case UpdatePreviewDataType.UpdateSelectedResolution:
        break;
      case UpdatePreviewDataType.UpdateShopInfo: {
        const shopInfoData =
          data.payload as PayloadInUpdatePreviewData[UpdatePreviewDataType.UpdateShopInfo];

        mainFlowRef.send({
          type: 'PREVIEW_UPDATE_CONTEXT',
          data: {
            type: UpdatePreviewDataType.UpdateShopInfo,
            value: {
              ...shopInfoData,
              ...(shopInfoData.hero_image && { hero_image: { src: shopInfoData.hero_image } }),
            },
          },
        });
        break;
      }
      default:
        break;
    }
  };

  const messageBridge = useMessageBridge();

  useEffect(() => {
    const removeEventListener = messageBridge.onMessage({
      type: EventName.UpdatePreviewData,
      callback: (data: PreviewMessageData[EventName.UpdatePreviewData]) => {
        // eslint-disable-next-line no-console
        console.log(
          '%c [ 💬 client - receive update data event ]',
          'font-size:13px; background:pink; color:#bf2c9f;',
          data,
        );
        handlePreviewData(data);
      },
    });
    return () => {
      removeEventListener();
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
};
