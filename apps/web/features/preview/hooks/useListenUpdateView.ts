import { useContext, useEffect } from 'react';

import {
  EventName,
  PayloadInUpdateView,
  PreviewMessageData,
  UpdateViewType,
} from '@aftership/preview-kit/business/rc';
import { useFlow } from 'returns-logics/react';

import { toggleEfaOrRefundModal } from '@/features/preview/ultils/toggleEfaOrRefundModal.ts';
import { toggleReplaceProductModal } from '@/features/preview/ultils/toggleReplaceProductModal.ts';
import { BreakPointContext, PreviewLayout } from '@/features/returns/components/BreakPointProvider';
import { useMessageBridge } from '@/features/returns/hooks/useMessageBridge';

import { toggleGiftReturnSection } from '../ultils/toggleGiftReturnSection';
import { toggleSelectItemModal } from '../ultils/toggleSelectItemModal';
import { toggleShowOrderErrorMessage } from '../ultils/toggleShowOrderErrorMessage';

// 一般用于打开 modal 这类场景
const useListenUpdateView = () => {
  const { ref: mainFlowRef, context } = useFlow();
  const { updatePreviewLayout } = useContext(BreakPointContext);

  const handleUpdateView = (data: PreviewMessageData[EventName.UpdateView]) => {
    switch (data.type) {
      case UpdateViewType.ToggleMobileView:
        updatePreviewLayout?.(
          (data.payload as PayloadInUpdateView[UpdateViewType.ToggleMobileView])?.isMobile
            ? PreviewLayout.mobile
            : PreviewLayout.desktop,
        );
        break;
      case UpdateViewType.ToggleSelectItemModal:
        toggleSelectItemModal(
          mainFlowRef,
          data.payload as PayloadInUpdateView[UpdateViewType.ToggleSelectItemModal],
        ); // payload 有类型问题，到时候找 turbo 问问
        break;
      case UpdateViewType.ToggleAskQuestionModal:
        toggleSelectItemModal(
          mainFlowRef,
          data.payload as PayloadInUpdateView[UpdateViewType.ToggleAskQuestionModal],
        );
        break;
      case UpdateViewType.ToggleExchangeRecommendProductModal:
        toggleEfaOrRefundModal(
          mainFlowRef,
          data.payload as PayloadInUpdateView[UpdateViewType.ToggleExchangeRecommendProductModal],
          context.isMultipleResolution,
        );
        break;
      case UpdateViewType.ToggleGiftReturnStep:
        toggleGiftReturnSection(
          mainFlowRef,
          data.payload as PayloadInUpdateView[UpdateViewType.ToggleGiftReturnStep],
        );
        break;
      case UpdateViewType.ToggleForbiddenOrderErrorMessage:
        toggleShowOrderErrorMessage(
          mainFlowRef,
          data.payload as PayloadInUpdateView[UpdateViewType.ToggleForbiddenOrderErrorMessage],
        );
        break;
      case UpdateViewType.ToggleReplaceProductModal:
        toggleReplaceProductModal(
          mainFlowRef,
          data.payload as PayloadInUpdateView[UpdateViewType.ToggleReplaceProductModal],
        );
        break;
      case UpdateViewType.ToggleExchangeItemResolutionModal:
        toggleSelectItemModal(
          mainFlowRef,
          data.payload as PayloadInUpdateView[UpdateViewType.ToggleExchangeItemResolutionModal],
        );
        break;
      case UpdateViewType.ToggleExchangeItemResolutionReplacementModal:
        toggleSelectItemModal(
          mainFlowRef,
          data.payload as PayloadInUpdateView[UpdateViewType.ToggleExchangeItemResolutionReplacementModal],
        );
        break;
      default:
        break;
    }
  };
  const messageBridge = useMessageBridge();

  useEffect(() => {
    const removeEventListener = messageBridge.onMessage({
      type: EventName.UpdateView,
      callback: (data: PreviewMessageData[EventName.UpdateView]) => {
        console.log(
          '%c [ 💬 client - update view event]',
          'font-size:13px; background:pink; color:#bf2c9f;',
          data,
        );
        handleUpdateView(data);
      },
    });
    return () => {
      removeEventListener();
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [messageBridge]);
};

export default useListenUpdateView;
