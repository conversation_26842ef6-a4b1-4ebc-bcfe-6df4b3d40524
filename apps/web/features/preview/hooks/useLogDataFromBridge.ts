import { useEffect } from 'react';

import { useMessageBridge } from '@/features/returns/hooks/useMessageBridge';

export const useLogDataFromBridge = () => {
  const messageBridge = useMessageBridge();

  useEffect(() => {
    const removeEventListener = messageBridge.onMessage({
      callback: (data: any) => {
        // 过滤老的事件
        if (!Object.hasOwn(data, 'actionType')) {
          console.log(
            '%c [ 💬 client - receive  event ]',
            'font-size:13px; background:pink; color:#bf2c9f;',
            data,
          );
        }
      },
    });
    return () => {
      removeEventListener();
    };
  }, [messageBridge]);
};
