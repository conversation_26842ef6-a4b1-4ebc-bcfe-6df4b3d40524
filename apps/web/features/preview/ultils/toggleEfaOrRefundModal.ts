import { PayloadInUpdateView, UpdateViewType } from '@aftership/preview-kit/business/rc';
import { MainMachineActorRef, waitFor } from '@aftership/returns-logics-core';

export const toggleEfaOrRefundModal = async (
  mainFlowRef: MainMachineActorRef,
  payload: PayloadInUpdateView[UpdateViewType.ToggleExchangeRecommendProductModal],
  isMultipleResolution: boolean,
) => {
  // 1. 先等 main flow 有 resolution 的 children
  const mainFlowState = isMultipleResolution
    ? await waitFor(mainFlowRef, (state) => !!state.children.itemSelectionSubFlow)
    : await waitFor(mainFlowRef, (state) => !!state.children.resolutionSubFlow);
  const resolutionSubFlowRef = mainFlowState.children.resolutionSubFlow;
  const itemSelectionSubFlowRef = mainFlowState.children.itemSelectionSubFlow;

  if (mainFlowState.context.isMultipleResolution) {
    if (payload.visible) {
      itemSelectionSubFlowRef?.send({ type: 'CHOOSE_EFA_OR_REFUND_DONE' });
      const itemSelectionSubFlowState = await waitFor(itemSelectionSubFlowRef!, (state) => {
        return !!state.children.exchangeOrRefundSubFlow;
      });
      itemSelectionSubFlowState.children.exchangeOrRefundSubFlow!.send({
        type: 'UPDATE_SHOW_RECOMMEND_PRODUCTS_FLAG',
        data: { showRecommendProducts: payload.showRecommendProducts },
      } as any);
    }
    return;
  }

  //  当前不处于 waitingForSelectedEfaOrRefund 才需要响应打开事件
  if (
    payload.visible &&
    resolutionSubFlowRef?.getSnapshot()?.value !== 'waitingForSelectedEfaOrRefund'
  ) {
    await waitFor(resolutionSubFlowRef!, (state) => {
      return state.matches('waitingForSelectResolution');
    });
    // 3. 选择 item
    resolutionSubFlowRef!.send({
      type: 'CHOOSE_EFA_OR_REFUND',
    });
    const itemSelectionSubFlowState = await waitFor(resolutionSubFlowRef!, (state) => {
      return !!state.children.exchangeOrRefundSubFlow;
    });
    itemSelectionSubFlowState.children.exchangeOrRefundSubFlow!.send({
      type: 'UPDATE_SHOW_RECOMMEND_PRODUCTS_FLAG',
      data: { showRecommendProducts: payload.showRecommendProducts },
    });
  } else if (
    !payload.visible &&
    resolutionSubFlowRef?.getSnapshot()?.value === 'waitingForSelectedEfaOrRefund'
  ) {
    const itemSelectionSubFlowState = await waitFor(resolutionSubFlowRef!, (state) => {
      return !!state.children.exchangeOrRefundSubFlow;
    });
    itemSelectionSubFlowState.children.exchangeOrRefundSubFlow!.send({
      type: 'SELECT_CANCEL',
    });
  }
};
