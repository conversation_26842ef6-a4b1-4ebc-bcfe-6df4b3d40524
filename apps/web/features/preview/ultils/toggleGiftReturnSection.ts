import {
  GiftReturnStep,
  PayloadInUpdateView,
  UpdateViewType,
} from '@aftership/preview-kit/business/rc';
import { MainMachineActorRef, waitFor } from '@aftership/returns-logics-core';

export const toggleGiftReturnSection = async (
  mainFlowRef: MainMachineActorRef,
  payload: PayloadInUpdateView[UpdateViewType.ToggleGiftReturnStep],
) => {
  // 1. 先等 main flow 有 gift return 的 children
  const mainFlowSnapshot = await waitFor(mainFlowRef, (state) => {
    return !!state.children.giftReturnSubFlow;
  });

  const giftReturnFlow = mainFlowSnapshot.children.giftReturnSubFlow;
  const giftReturnSnapshot = giftReturnFlow?.getSnapshot();

  // 2. 根据 step 打开对应的 section
  const { step } = payload;

  switch (step) {
    case GiftReturnStep.OrderLookup:
      if (!giftReturnSnapshot?.matches('orderLookupInput')) {
        giftReturnFlow?.send({ type: 'goToOrderLookupInPreview' });
      }
      break;
    case GiftReturnStep.SubmitRequest:
      if (!giftReturnSnapshot?.matches('formWithoutOrderInfo')) {
        giftReturnFlow?.send({ type: 'goToFormWithOrderInfoInPreview' });
      }
      break;
  }
};
