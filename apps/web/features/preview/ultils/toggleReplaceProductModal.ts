import { PayloadInUpdateView, UpdateViewType } from '@aftership/preview-kit/business/rc';
import { MainMachineActorRef, waitFor } from '@aftership/returns-logics-core';

export const toggleReplaceProductModal = async (
  mainFlowRef: MainMachineActorRef,
  payload: PayloadInUpdateView[UpdateViewType.ToggleReplaceProductModal],
) => {
  const mainFlowState = await waitFor(
    mainFlowRef,
    (state) => !!state.children.replaceTheSameItemSubFlow,
  );

  const itemSelectionSubFlowRef = mainFlowState.children.replaceTheSameItemSubFlow;

  if (
    payload.visible &&
    itemSelectionSubFlowRef?.getSnapshot()?.value !== 'selectSingleReplaceItem'
  ) {
    const itemSelectionSubFlowState = await waitFor(itemSelectionSubFlowRef!, (state) => {
      return state.matches('waitingForSelectReplaceItem');
    });
    const selectedItem = itemSelectionSubFlowState.context?.selectedItems?.[0]!;
    if (!itemSelectionSubFlowState.children?.[selectedItem?.itemId]) {
      itemSelectionSubFlowRef!.send({
        type: 'SELECT_ITEM',
        data: { selectedItem: selectedItem },
      });
    }
  } else if (
    !payload.visible &&
    itemSelectionSubFlowRef?.getSnapshot()?.value === 'selectSingleReplaceItem'
  ) {
    const selectedItem = itemSelectionSubFlowRef?.getSnapshot()?.context?.selectedItems?.[0]!;
    if (itemSelectionSubFlowRef?.getSnapshot()?.children?.[selectedItem?.itemId]) {
      itemSelectionSubFlowRef!.send({
        type: 'SELECT_ITEM_CANCEL',
        data: { selectedItem: selectedItem },
      });
    }
  }
};
