import { PayloadInUpdateView, UpdateViewType } from '@aftership/preview-kit/business/rc';
import {
  MainMachineActorRef,
  SelectSingleItemSubFlowActorRef,
  waitFor,
} from '@aftership/returns-logics-core';

// 可以优化一下，如果 flow 已经存在，不做重复操作
export const toggleSelectItemModal = async (
  mainFlowRef: MainMachineActorRef,
  payload:
    | PayloadInUpdateView[UpdateViewType.ToggleSelectItemModal]
    | PayloadInUpdateView[UpdateViewType.ToggleAskQuestionModal]
    | PayloadInUpdateView[UpdateViewType.ToggleExchangeItemResolutionReplacementModal]
    | PayloadInUpdateView[UpdateViewType.ToggleExchangeItemResolutionModal],
) => {
  // 1. 先等 main flow 有 select item 的 children
  const mainFlowState = await waitFor(
    mainFlowRef,
    (state) => !!state.children.itemSelectionSubFlow,
  );

  const itemSelectionSubFlowRef = mainFlowState.children.itemSelectionSubFlow;

  // 2. 等待 item selection sub flow 到达 waitingForItemSelection 状态 --> 其实这里有点问题，看看怎么改一下
  const itemSelectionSubFlowState = await waitFor(itemSelectionSubFlowRef!, (state) => {
    return state.matches('waitingForSelectItem') || state.matches('selectSingleItem');
  });

  const selectedItem = itemSelectionSubFlowState.context?.returnableItems?.[0]!;
  if (!itemSelectionSubFlowState.children?.[selectedItem?.external_id]) {
    // 3. 选择 item
    itemSelectionSubFlowRef!.send({
      type: 'SELECT_ITEM',
      data: { orderItem: selectedItem },
    });
  }

  // 4. 等待 item selection sub flow 到达 waitingForSelectItemDone 状态 且 存在 select single sub flow 的 children
  const itemSelectionSubFlowAfterSelectItem = await waitFor(itemSelectionSubFlowRef!, (state) => {
    return !!state.children?.[selectedItem?.external_id];
  });

  const selectSingleItemSubFlowRef = itemSelectionSubFlowAfterSelectItem.children[
    selectedItem.external_id
  ]! as SelectSingleItemSubFlowActorRef;

  // 5. 触发 select single item sub flow 到达指定状态

  if (payload.visible) {
    if ('step' in payload) {
      switch (payload.step) {
        case 'quantity':
          selectSingleItemSubFlowRef.send({
            type: 'GO_TO_SELECT_QUANTITY_PREVIEW',
          });
          break;
        case 'reason':
          selectSingleItemSubFlowRef.send({ type: 'GO_TO_SELECT_REASON_PREVIEW' });
          break;
        case 'description':
          selectSingleItemSubFlowRef.send({
            type: 'GO_TO_FILL_MORE_DETAIL_PREVIEW',
            data: {
              selectedReasonId: '045c026f42004a12a22488a2927ea9f6',
            },
          });
          break;
        case 'resolution':
          selectSingleItemSubFlowRef.send({
            type: 'GO_TO_SELECT_RESLUTION_PREVIEW',
          });
          break;
        case 'replacement':
          selectSingleItemSubFlowRef.send({
            type: 'GO_TO_SELECT_RESLUTION_REPLACEMENT_PREVIEW',
          });
          break;
        default:
          break;
      }
    } else {
      selectSingleItemSubFlowRef.send({ type: 'GO_TO_SELECT_ANSWER_PREVIEW' });
    }
  } else {
    itemSelectionSubFlowRef?.send({
      type: 'CANCEL_ITEM',
      data: { itemId: selectedItem.external_id },
    });
  }
};
