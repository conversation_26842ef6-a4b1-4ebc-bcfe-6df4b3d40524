import { PayloadInUpdateView, UpdateViewType } from '@aftership/preview-kit/business/rc';
import { MainMachineActorRef, waitFor } from '@aftership/returns-logics-core';

export const toggleShowOrderErrorMessage = async (
  mainFlowRef: MainMachineActorRef,
  payload: PayloadInUpdateView[UpdateViewType.ToggleForbiddenOrderErrorMessage],
) => {
  const mainFlowState = await waitFor(mainFlowRef, (state) => !!state.children.orderLookupSubFlow);

  const orderLookupSubflow = mainFlowState.children.orderLookupSubFlow;

  if (payload.visible) {
    orderLookupSubflow?.send?.({
      type: 'SHOW_ORDER_ERROR_MESSAGE',
    });
  }
};
