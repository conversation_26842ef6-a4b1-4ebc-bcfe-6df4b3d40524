import React from 'react';
import { useTranslation } from 'react-i18next';

import { Box, NumberField, Stack, StackProps, useBreakpointValue } from '@aftership/astra';

import WarningTips from '../WarningTips/WarningTips';

export interface ChooseQuantityProps {
  isLimitQuantityExactlyOne: boolean;
  returnableQuantity: number;
  selectedQuantity: number;
  onSelectedQuantity: (quantity: number) => void;
  onQuantityChange: (quantity: number) => void;
}
const ChooseQuantity = ({
  isLimitQuantityExactlyOne,
  returnableQuantity,
  selectedQuantity,
  onQuantityChange,
}: ChooseQuantityProps) => {
  const { t } = useTranslation();
  const align = useBreakpointValue<'base' | 'm', StackProps['align']>({
    base: 'center',
    m: 'start',
  });

  return (
    <Stack flex={1} gap='xl' direction={'column'} align={align}>
      <Box width={260}>
        <NumberField
          variant='outlined'
          size='medium'
          defaultValue={selectedQuantity}
          value={selectedQuantity}
          onChange={(value) => {
            onQuantityChange(value);
          }}
          minValue={1}
          maxValue={isLimitQuantityExactlyOne ? 1 : returnableQuantity}
        />
      </Box>
      {isLimitQuantityExactlyOne && <WarningTips tips={t('page.description.limitPlusQuantity')} />}
    </Stack>
  );
};

export default ChooseQuantity;
