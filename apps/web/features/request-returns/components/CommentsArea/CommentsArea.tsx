import { t } from 'i18next';
import React from 'react';

import { Stack, TextArea, Typography } from '@aftership/astra';

import { ReturnReasonDetail, genReturnReasonCode } from '@/i18n/dynamic';

export interface CommentsAreaProps {
  reasonId: string;
  commentsDescription: Maybe<string>;
  required: boolean;
  comments?: string;
  onCommentsChange: (comments: string) => void;
}

const CommentsArea = ({
  reasonId,
  commentsDescription,
  comments,
  required,
  onCommentsChange,
}: CommentsAreaProps) => {
  const placeholder = t(
    genReturnReasonCode({
      reasonId: reasonId,
      detail: ReturnReasonDetail.Comment,
    }),
    {
      rawValue: commentsDescription,
      defaultValue: commentsDescription,
    },
  );
  return (
    <Stack gap='2xs' direction={'column'}>
      <Typography variant={'bodyMd'}>
        {t('page.description.comment')}
        {required ? '*' : ''}
      </Typography>
      <TextArea
        height={'120px'}
        placeholder={placeholder}
        showCounter
        maxLength={500}
        value={comments}
        onChange={onCommentsChange}
      />
    </Stack>
  );
};

export default CommentsArea;
