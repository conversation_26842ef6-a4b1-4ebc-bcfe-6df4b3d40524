import React from 'react';
import { useTranslation } from 'react-i18next';

import { Box, Typography } from '@aftership/astra';
import { tokenVars } from '@aftership/astra-tokens/Contract.css';

import { Modal } from '@/components/Modal';

const { Space } = tokenVars.Semantic;

export interface ConfirmCompProps {
  isOpen: boolean;
  onClose: VoidFunction;
  onConfirm: VoidFunction;
}
export interface Props extends ConfirmCompProps {
  title: string;
  description: string;
  primaryBtnText: string;
  secondaryBtnText: string;
}

const ConfirmComp = (compProps: ConfirmCompProps) => {
  const { t } = useTranslation();
  const { isOpen, onClose, onConfirm } = compProps;
  const primaryBtnText = t('v2.page.action.confirm.remove');
  const secondaryBtnText = t('v2.page.action.confirm.cancel');
  const title = t('v2.page.description.selectItem.removeTitle');
  const description = t('v2.page.description.selectItem.removeDescription');

  return (
    <Modal
      isOpen={isOpen}
      title={title}
      size={'small'}
      onClose={onClose}
      primaryAction={{ content: primaryBtnText, onAction: onConfirm }}
      secondaryAction={{ content: secondaryBtnText, onAction: onClose }}
    >
      <Box style={{ paddingBlock: Space.L }}>
        <Typography variant={'bodyLg'} textAlign='center' as='p'>
          {description}
        </Typography>
      </Box>
    </Modal>
  );
};

export default ConfirmComp;
