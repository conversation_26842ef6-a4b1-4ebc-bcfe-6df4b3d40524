import React from 'react';

import { Stack, Typography } from '@aftership/astra';

export interface Props {
  tags: Array<string>;
}
const CustomizationsTags = ({ tags }: Props) => {
  return (
    <Stack direction={'column'}>
      <Typography variant={'bodySm'}>Customizations added:</Typography>

      {tags.map((tag, index) => (
        <Typography key={`${tag}-${index}`} variant={'bodySm'}>{`• ${tag}`}</Typography>
      ))}
    </Stack>
  );
};

export default CustomizationsTags;
