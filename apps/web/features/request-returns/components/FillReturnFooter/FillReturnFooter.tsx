import React from 'react';

import { Box } from '@aftership/astra';
import { tokenVars } from '@aftership/astra-tokens/Contract.css';

import { Button } from '@/components/Button';
import useDevice from '@/hooks/useDevice';

const { Space } = tokenVars.Semantic;

export interface FillReturnFooterProps {
  isDisabled: boolean;
  onPress: VoidFunction;
  btnText: string;
  isLoading?: boolean;
}

const FillReturnFooter = ({
  isDisabled,
  onPress,
  btnText,
  isLoading = false,
}: FillReturnFooterProps) => {
  const isMobile = useDevice().mobile;
  return (
    <Box width='100%' paddingY={isMobile ? Space.M : 0} paddingX={Space.M}>
      <Button
        isFullWidth
        size={'large'}
        isDisabled={isDisabled}
        onPress={onPress}
        isLoading={isLoading}
      >
        {btnText}
      </Button>
    </Box>
  );
};

export default FillReturnFooter;
