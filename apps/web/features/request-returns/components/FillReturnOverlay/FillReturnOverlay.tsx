import React, { FC, PropsWith<PERSON><PERSON>dren, ReactNode, useMemo } from 'react';
import { useTranslation } from 'react-i18next';

import { Box, Overlay, Spinner, Stack, clsx, useBreakpointValue, useToast } from '@aftership/astra';
import { ChevronLeftOutlined } from '@aftership/astra-icons';
import { primitiveTokenVar } from '@aftership/astra-tokens/primitive-token/Contract.css';
import { semanticTokenVar } from '@aftership/astra-tokens/semantic-token/Contract.css';
import { usePreviewContext } from '@aftership/preview-kit/client';
import { ItemResolution, Resolution, ReturnableOrderItem } from '@aftership/returns-logics-core';

import { Uploader } from '@/components/FileUpload';
import { SheetHeader } from '@/components/Sheet';
import Sheet, { SheetProps } from '@/components/Sheet/Sheet.tsx';
import { ChooseAnswer } from '@/features/request-returns/components/ChooseAnswer';
import { ChooseQuantity } from '@/features/request-returns/components/ChooseQuantity';
import { ChooseReason } from '@/features/request-returns/components/ChooseReason';
import { ChooseSubReason } from '@/features/request-returns/components/ChooseSubReason';
import { FillMoreDetail } from '@/features/request-returns/components/FillMoreDetail';
import { FillReturnFooter } from '@/features/request-returns/components/FillReturnFooter';
import Header from '@/features/request-returns/components/FillReturnOverlay/components/Header.tsx';
import { checkFillDetailInfoBtnAvailable } from '@/features/request-returns/components/FillReturnOverlay/utls/reasonUtls.ts';
import { ReturnItemInfoCard } from '@/features/request-returns/components/ReturnItemInfoCard';
import { translate } from '@/features/resolution/components/ResolutionList/ResolutionList';
import useDerivedState from '@/hooks/useDerivedState.ts';
import { useShopInfo } from '@/hooks/useShopInfo.ts';
import { ResolutionSuffix } from '@/i18n/dynamic';
import { customScrollbarDesktopStyle, customScrollbarMobileStyle } from '@/styles/common.css.ts';
import { decodeHtmlEntities } from '@/utils/products.ts';
import { EventName } from '@/utils/tracker/consts.ts';
import getTrackerInstance from '@/utils/tracker/index.ts';

import { useReturnInfoModal } from './hooks/useReturnInfoModal';
import { bodyMobileClassName, containerClass, overlayModalClassName } from './styles.css.ts';

import FillReturnTitle from '../FillReturnTitle/FillReturnTitle.tsx';
import MakeExchangeOrRefund from '../MakeExchangeOrRefund/index.tsx';

interface ContainerProps extends PropsWithChildren {
  isOpen: boolean;
  returnInfoCard: ReactNode;
  title?: ReactNode;
  footer?: ReactNode;
  canBack?: boolean;
  footerExtra?: ReactNode;
  onBack?: () => void;
  onClose?: SheetProps['onClose'];
}

const { Space, Color } = semanticTokenVar;
const { Color: PrimitiveColor } = primitiveTokenVar;

export const ContainerComp: FC<ContainerProps> = ({
  children,
  isOpen,
  footer,
  returnInfoCard,
  title,
  footerExtra,
  canBack = false,
  onBack = () => {},
  onClose = () => {},
}) => {
  const { isPreview } = usePreviewContext();
  const isMobile = useBreakpointValue({ base: true, m: false });
  const customScrollbarStyle = isMobile ? customScrollbarMobileStyle : customScrollbarDesktopStyle;

  return isMobile ? (
    <Sheet
      isOpen={isOpen}
      isClosable={false}
      disableFocusManagement={isPreview}
      footer={footer}
      style={{ height: '90%', overflow: 'hidden' }}
      bodyStyle={{ padding: 0 }}
      footerExtra={footerExtra}
    >
      <Stack direction='column' style={{ height: '100%' }}>
        <Stack direction='column' style={{ backgroundColor: Color.Bg.Base }}>
          <SheetHeader
            isClosable
            leadingAction={canBack ? { icon: ChevronLeftOutlined, onPress: onBack } : undefined}
            wrapperStyle={{ height: 68, paddingInline: Space.M }}
            onClose={onClose}
          />
          {returnInfoCard}
        </Stack>
        <Stack
          direction={'column'}
          gap='xl'
          className={clsx(bodyMobileClassName, customScrollbarStyle)}
        >
          {title}
          {children}
        </Stack>
      </Stack>
    </Sheet>
  ) : (
    <Overlay isOpen={isOpen} disableFocusManagement={isPreview} className={overlayModalClassName}>
      <Stack direction={'row'} style={{ height: '540px' }}>
        <Box
          flexShrink={0}
          width={288}
          paddingX={'44px'}
          height={'100%'}
          backgroundColor={PrimitiveColor.Gray[100]}
        >
          {returnInfoCard}
        </Box>
        <Box flex={1} height={'100%'} backgroundColor={Color.Bg.Body}>
          <Stack direction={'column'} style={{ height: '100%' }}>
            <Header canBack={canBack} backPreState={onBack} cancelFillReturnInfo={onClose} />
            <Box paddingX={Space['3Xl']}>{title}</Box>
            <Box className={clsx(containerClass, customScrollbarStyle)}>{children}</Box>
            {footer && (
              <Stack style={{ paddingInline: Space['3Xl'], paddingBlock: Space['2Xl'] }}>
                {footer}
              </Stack>
            )}
          </Stack>
        </Box>
      </Stack>
    </Overlay>
  );
};

export interface FillReturnInfoProps {
  orderItem?: ReturnableOrderItem;
  isOpen: boolean;
}
const FillReturnOverlay = ({
  orderItem = {} as ReturnableOrderItem,
  isOpen,
}: FillReturnInfoProps) => {
  const {
    external_id: itemId,
    returnableQuantity: returnableQuantity,
    product_title: productTitleOrigin = '',
    variant_title: variantTitleOrigin,
    image_urls,
    frontEndPrice: price,
  } = orderItem;
  const productCoverUrl = image_urls?.[0];
  const { t } = useTranslation();
  const {
    isSelectExchangeOrRefundLoading,
    questionInfo,
    productsInfoMap,
    isLimitQuantityExactlyOne,
    backPreState,
    cancelFillReturnInfo,
    addToBlockList,
    selectReason,
    selectSubReason,
    selectQuantity,
    fillMoreDetail,
    selectItemResolution,
    context,
    currentStep,
    matches,
    canBack,
  } = useReturnInfoModal(itemId);
  const { multipleResolutionEnabled } = useShopInfo();
  const { showToast } = useToast();
  const reasons = context?.reasonGroup?.return_reasons ?? [];
  const selectedReasonId = context?.selectedReasonId;
  const selectedItemResolution = context?.resolution;
  const subReasons = reasons.find((reason) => reason.id === selectedReasonId)?.subreasons ?? [];
  const selectedSubReasonId = context?.selectedSubReasonId;
  const [selectedQuantity, setSelectedQuantity] = useDerivedState(context?.quantity ?? 1);
  const [comments, setComments] = useDerivedState(context?.comment ?? '');

  const [images, setImages] = useDerivedState(context?.returnImages);
  const [uploadState, setIsUploadState] = React.useState<Uploader['status'] | 'idle'>('idle');

  const resolutions = context?.resolutions || [];
  const exchangeResolution = resolutions.find(
    (resolution) => resolution.type === Resolution.ReplaceTheSameItem,
  );
  const itemResolutionIterable = [
    {
      id: ItemResolution.Exchange,
      name: exchangeResolution
        ? translate(exchangeResolution.name, exchangeResolution.type, ResolutionSuffix.Name)
        : '',
      description: exchangeResolution
        ? translate(
            exchangeResolution.description,
            exchangeResolution.type,
            ResolutionSuffix.Description,
          )
        : '',
      allow: resolutions.some((resolution) => resolution.type === Resolution.ReplaceTheSameItem),
    },
    {
      id: ItemResolution.Refund,
      name: t('resolution.multiple.refund.title'),
      allow:
        resolutions.some((resolution) => resolution.type === Resolution.StoreCredit) ||
        resolutions.some((resolution) => resolution.type === Resolution.OriginalPayment),
    },
  ];

  const clearMoreDetailInfo = () => {
    setImages([]);
    setComments('');
  };
  const reasonsId = reasons.map((reason) => reason.id).join(',');
  const footer = useMemo(() => {
    if (currentStep?.name === 'selectQuantity') {
      return (
        <FillReturnFooter
          isDisabled={Number.isNaN(selectedQuantity)}
          onPress={() => selectQuantity(selectedQuantity)}
          btnText={t('page.request.nextStep')}
        />
      );
    }
    if (currentStep?.name === 'fillMoreDetail' && selectedReasonId) {
      const isButtonDisable = checkFillDetailInfoBtnAvailable(
        reasons,
        selectedReasonId,
        uploadState,
        images,
        comments,
      );
      return (
        <FillReturnFooter
          isDisabled={isButtonDisable}
          onPress={() => fillMoreDetail(comments, images)}
          btnText={multipleResolutionEnabled ? t('page.request.nextStep') : t('popup.request.done')}
          isLoading={uploadState === 'uploading'}
        />
      );
    }

    return null;
    // fixme: hook 依赖有问题
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    currentStep?.name,
    selectedReasonId,
    reasonsId,
    selectedQuantity,
    uploadState,
    images,
    comments,
    multipleResolutionEnabled,
  ]);

  const handleUploadError = (error: Uploader['uploadError']) => {
    if (error) {
      let errorI18nKey = '';
      switch (error.type) {
        case 'limit-length':
          errorI18nKey = 'v2.page.error.exceedImageLength';
          break;
        case 'overSize':
          errorI18nKey = 'v2.page.error.exceedImageSize';
          break;
        case 'non-repeatable':
          errorI18nKey = 'v2.page.error.nonRepeatableImage';
          break;
        case 'request':
        default:
          errorI18nKey = 'v2.page.error.uploadImageNetError';
          break;
      }
      showToast(t(errorI18nKey));
    }
  };
  const productTitle = useMemo(() => decodeHtmlEntities(productTitleOrigin), [productTitleOrigin]);
  const variantTitle = useMemo(() => decodeHtmlEntities(variantTitleOrigin), [variantTitleOrigin]);

  return (
    <ContainerComp
      isOpen={isOpen}
      returnInfoCard={
        <ReturnItemInfoCard
          productTitle={productTitle}
          variantTitle={variantTitle}
          price={price}
          selectedQuantity={selectedQuantity}
          productCoverUrl={productCoverUrl}
        />
      }
      title={
        currentStep?.name && (
          <FillReturnTitle
            stepName={currentStep?.name}
            questionTitle={questionInfo?.questionTitle}
            isSelectQuestionAnswer={!!matches?.('selectQuestionAnswer')}
            questionId={questionInfo?.questionId}
          />
        )
      }
      footer={footer}
      canBack={canBack}
      onBack={backPreState}
      onClose={cancelFillReturnInfo}
    >
      <>
        {matches?.('selectQuestionAnswer') && questionInfo && (
          <ChooseAnswer {...questionInfo} itemId={itemId} onBtnClick={addToBlockList} />
        )}
        {currentStep?.name === 'selectQuantity' && (
          <ChooseQuantity
            isLimitQuantityExactlyOne={isLimitQuantityExactlyOne}
            returnableQuantity={returnableQuantity}
            selectedQuantity={selectedQuantity}
            onQuantityChange={(quantity) => setSelectedQuantity(quantity)}
            onSelectedQuantity={selectQuantity}
          />
        )}
        {currentStep?.name === 'selectReason' && (
          <ChooseReason
            reasons={reasons.map((reason) => ({ name: reason.name, id: reason.id }))}
            selectedReasonId={selectedReasonId}
            onSelectReason={(reasonId) => {
              if (reasonId !== selectedReasonId) {
                clearMoreDetailInfo();
              }

              getTrackerInstance().reportClickEvent({ eventName: EventName.selectReturnReason });

              selectReason(reasonId);
            }}
          />
        )}
        {currentStep?.name === 'selectSubReason' && (
          <ChooseSubReason
            reasonId={selectedReasonId ?? ''}
            subReasons={subReasons
              .filter((subReason) => subReason.enabled)
              .map((subReason) => ({
                name: subReason.name,
                id: subReason.key,
              }))}
            selectedSubReasonId={selectedSubReasonId}
            onSelectSubReason={(subReasonId) => {
              if (subReasonId !== selectedSubReasonId) {
                clearMoreDetailInfo();
              }

              getTrackerInstance().reportClickEvent({ eventName: EventName.selectReturnSubReason });
              selectSubReason(subReasonId);
            }}
          />
        )}
        {currentStep?.name === 'fillMoreDetail' && selectedReasonId && (
          <FillMoreDetail
            selectedReason={reasons.find((reason) => reason.id === selectedReasonId)!}
            comments={comments}
            images={images}
            onCommentsChange={(comments) => setComments(comments)}
            onImagesChange={(images) => setImages(images)}
            onUploadingChange={(status) => {
              setIsUploadState(status);
            }}
            onUploadError={handleUploadError}
          />
        )}

        {isSelectExchangeOrRefundLoading && (
          <Stack direction='column' align='center' justify='center' flex={1}>
            <Spinner size='large' />
            {/* 带上 Header 的高度，居中一些 */}
            <Box height={84} />
          </Stack>
        )}

        {currentStep?.name === 'makeExchangeOrRefund' && (
          <MakeExchangeOrRefund
            selectedItemResolution={selectedItemResolution}
            selectItemResolution={selectItemResolution}
            productsInfoMap={productsInfoMap}
            itemId={itemId}
            itemResolutionIterable={itemResolutionIterable}
          />
        )}
      </>
    </ContainerComp>
  );
};

export default FillReturnOverlay;
