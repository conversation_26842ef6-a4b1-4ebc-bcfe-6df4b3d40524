import React from 'react';

import { Box, IconButton, Stack, useBreakpointValue } from '@aftership/astra';
import { ChevronLeftOutlined, CloseOutlined } from '@aftership/astra-icons';
import { semanticTokenVar } from '@aftership/astra-tokens/semantic-token/Contract.css';

export interface Props {
  canBack: boolean;
  backPreState: () => void;
  cancelFillReturnInfo: () => void;
}

const { Space } = semanticTokenVar;

const Header = ({ canBack, backPreState, cancelFillReturnInfo }: Props) => {
  const padding = useBreakpointValue({
    base: Space.M,
    m: Space.Xl,
  });

  return (
    <Box padding={padding}>
      <Stack justify={'space-between'}>
        {canBack ? (
          <IconButton
            icon={ChevronLeftOutlined}
            size='large'
            onPress={() => {
              setTimeout(() => {
                backPreState();
              }, 20);
            }}
          />
        ) : (
          <Box />
        )}
        <IconButton
          icon={CloseOutlined}
          size='large'
          onPress={() => {
            setTimeout(() => {
              cancelFillReturnInfo();
            }, 20);
          }}
        />
      </Stack>
    </Box>
  );
};

export default Header;
