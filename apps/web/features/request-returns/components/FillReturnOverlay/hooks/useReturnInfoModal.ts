import { useCallback } from 'react';

import {
  ItemResolution,
  QuestionWorkFlowItem,
  ReturnImage,
  SelectSingleItemSubFlowActorRef,
  getNextSnapshot,
} from '@aftership/returns-logics-core';
import { useSelector } from 'returns-logics/react';

import useFlowSelector from '@/hooks/useFlowSelector';

export const extractQuestionInfo = (questionWorkFlowItem?: QuestionWorkFlowItem) => {
  if (questionWorkFlowItem) {
    const { id, question, answer_for_terminate_flow, answer_for_continue_flow } =
      questionWorkFlowItem;
    const questionId = id ?? '';
    const questionTitle = question ?? '';
    const primaryBtnText =
      answer_for_terminate_flow && answer_for_terminate_flow.length
        ? answer_for_terminate_flow
        : 'Yes';
    const secondaryBtnText =
      answer_for_continue_flow && answer_for_continue_flow.length ? answer_for_continue_flow : 'No';
    return { questionId, questionTitle, primaryBtnText, secondaryBtnText };
  }
};

export const useReturnInfoModal = (itemId: string) => {
  const { flow: itemSelectionSubFlow } = useFlowSelector('itemSelectionSubFlow');

  const itemSelectionSubFlowActiveChildren = useSelector(
    itemSelectionSubFlow,
    (state) => state?.children?.[itemId],
  ) as SelectSingleItemSubFlowActorRef;

  const dispatch = itemSelectionSubFlowActiveChildren?.send;
  const context = useSelector(itemSelectionSubFlowActiveChildren, (state) => state?.context);
  const snapshot = useSelector(itemSelectionSubFlowActiveChildren, (state) => state);

  const currentStepName = useSelector(itemSelectionSubFlowActiveChildren, (state) => state?.value);
  const isLoading = useSelector(itemSelectionSubFlowActiveChildren, (state) =>
    state?.hasTag?.('loading'),
  );
  const isSelectExchangeOrRefundLoading = useSelector(itemSelectionSubFlowActiveChildren, (state) =>
    state?.matches?.('beforeMakeExchangeOrRefund'),
  );

  const productsInfoMap = context?.productsInfoMap;

  const currentStep = {
    name: currentStepName,
    isLoading,
  };
  const matches = useSelector(itemSelectionSubFlowActiveChildren, (state) =>
    state?.matches?.bind?.(state),
  );
  const on = itemSelectionSubFlowActiveChildren?.on.bind(itemSelectionSubFlowActiveChildren);

  // 是否限制每次退货只能退一种商品
  const isLimitSingleItemPerReturn =
    Boolean(context?.shopEligibilityRules?.product_limit?.feature_applied) &&
    Boolean(context?.shopEligibilityRules?.product_limit?.limit_single_item_per_return?.enabled);
  // 是否限制每次退货数量为 1
  const isLimitQuantityExactlyOne =
    isLimitSingleItemPerReturn &&
    Boolean(
      context?.shopEligibilityRules?.product_limit?.limit_single_item_per_return
        ?.quantity_exactly_one,
    );

  const questionInfo = extractQuestionInfo(context?.questionWorkFlowItem);

  const canBack =
    !!snapshot &&
    (getNextSnapshot(snapshot?.machine, snapshot, {
      type: 'BACK',
    }).value !== currentStep?.name ||
      currentStep?.name === 'replaceTheSameItem');

  const selectQuantity = useCallback(
    (quantity: number) => {
      dispatch?.({
        type: 'SELECT_QUANTITY',
        data: {
          quantity: quantity,
        },
      });
    },
    [dispatch],
  );
  const selectReason = useCallback(
    (reasonId: string) => {
      dispatch?.({
        type: 'SELECT_REASON',
        data: {
          selectedReasonId: reasonId,
        },
      });
    },
    [dispatch],
  );
  const selectSubReason = useCallback(
    (subReasonId: string) => {
      dispatch?.({
        type: 'SELECT_SUB_REASON',
        data: {
          selectedSubReasonId: subReasonId,
        },
      });
    },
    [dispatch],
  );
  const addToBlockList = useCallback(
    (addToBlockList: boolean) => {
      dispatch?.({
        type: 'SELECT_QUESTION_ANSWER',
        data: {
          confirm: addToBlockList,
        },
      });
    },
    [dispatch],
  );
  const fillMoreDetail = useCallback(
    (comment?: string, images?: Array<ReturnImage>) => {
      dispatch?.({
        type: 'FILL_MORE_DETAIL',
        data: {
          comment: comment,
          returnImages: images,
        },
      });
    },
    [dispatch],
  );
  const selectItemResolution = useCallback(
    (type: ItemResolution) => {
      dispatch?.({
        type: 'MAKE_EXCHANGE_OR_REFUND',
        data: {
          type,
        },
      });
    },
    [dispatch],
  );
  const backPreState = useCallback(() => {
    dispatch?.({
      type: 'BACK',
    });
  }, [dispatch]);
  const cancelFillReturnInfo = useCallback(() => {
    dispatch?.({ type: 'CANCEL' });
  }, [dispatch]);

  return {
    isSelectExchangeOrRefundLoading,
    questionInfo,
    productsInfoMap,
    isLimitQuantityExactlyOne,
    context,
    currentStep,
    snapshot,
    isLoading,
    matches,
    selectQuantity,
    selectReason,
    selectSubReason,
    addToBlockList,
    fillMoreDetail,
    selectItemResolution,
    backPreState,
    cancelFillReturnInfo,
    on,
    canBack,
  };
};
