import { style } from '@vanilla-extract/css';

import { tokenVars } from '@aftership/astra-tokens/Contract.css';
import { semanticTokenVar } from '@aftership/astra-tokens/semantic-token/Contract.css';

const { Space } = semanticTokenVar;

export const containerClass1 = style({
  display: 'flex',
  flexDirection: 'column',
  flex: 1,
  flexBasis: 0,
  overflowY: 'auto',
  paddingLeft: Space.Xl,
  paddingRight: Space.Xl,
  paddingBottom: Space.S,
  marginLeft: Space.M,
  marginRight: Space.M,
  gap: Space.Xl,
});

export const containerClass = style({
  display: 'flex',
  flexDirection: 'column',
  flex: 1,
  flexBasis: 0,
  overflowY: 'auto',
  paddingLeft: Space.Xl,
  paddingRight: Space.Xl,
  paddingTop: Space.Xl,
  paddingBottom: Space.Xl,
  marginLeft: Space.M,
  marginRight: Space.M,
  gap: Space.Xl,
});

export const bodyMobileClassName = style({
  display: 'flex',
  flex: 1,
  flexBasis: 0,
  flexDirection: 'column',
  overflowY: 'auto',
  gap: Space['2Xl'],
  padding: Space.Xl,
});

export const overlayModalClassName = style({
  width: 880,
  height: 540,
  margin: 'auto',
  position: 'relative',
  top: '50%',
  transform: 'translateY(-50%)',
  borderRadius: tokenVars.Semantic.Radius.M,
  overflow: 'hidden',
});
