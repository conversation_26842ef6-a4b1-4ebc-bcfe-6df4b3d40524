import { Box, Stack, Typography } from '@aftership/astra';
import { semanticTokenVar } from '@aftership/astra-tokens/semantic-token/Contract.css';
import { SelectSingleItemSubFlowSnapshot } from '@aftership/returns-logics-core';

import { ReturnItemExchangeOrRefundTitleText } from '@/features/preview/components/WithPreviewSection';
import { generateTitle } from '@/features/request-returns/utils/returnInfoModalHelper.ts';
import useDevice from '@/hooks/useDevice.ts';
import { useWarrantyTranslation } from '@/hooks/useTranslation.ts';

export interface FillReturnTitleProps {
  isSelectQuestionAnswer: Boolean;
  stepName: SelectSingleItemSubFlowSnapshot['value'];
  questionId?: string;
  questionTitle?: string;
}

const { Space } = semanticTokenVar;

const FillReturnTitle = ({
  isSelectQuestionAnswer,
  stepName,
  questionId,
  questionTitle,
}: FillReturnTitleProps) => {
  const isMobile = useDevice().mobile;
  const { t } = useWarrantyTranslation();
  const paddingX = isMobile ? Space.M : 0;
  const textAlign = isMobile ? 'center' : 'left';
  const variant = isMobile ? 'headingXs' : 'heading2Xs';

  return (
    <Box paddingX={paddingX}>
      <Stack direction={'column'} gap='xs'>
        {isSelectQuestionAnswer && (
          <Typography variant={'bodyLg'} color='primary' style={{ textAlign: textAlign }}>
            {t('popup.description.answerQuestion')}
          </Typography>
        )}
        {stepName === 'makeExchangeOrRefund' ? (
          <ReturnItemExchangeOrRefundTitleText
            variant={variant}
            color='primary'
            style={{ textAlign: textAlign }}
          >
            {`${generateTitle(stepName, questionId, questionTitle)}`}
          </ReturnItemExchangeOrRefundTitleText>
        ) : (
          <Typography variant={variant} color='primary' style={{ textAlign: textAlign }}>
            {`${generateTitle(stepName, questionId, questionTitle)}`}
          </Typography>
        )}
      </Stack>
    </Box>
  );
};

export default FillReturnTitle;
