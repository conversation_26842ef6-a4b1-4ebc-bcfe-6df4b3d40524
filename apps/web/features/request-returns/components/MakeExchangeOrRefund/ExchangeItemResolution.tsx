import { Icon, Stack, Typography } from '@aftership/astra';
import { ChevronRightOutlined } from '@aftership/astra-icons';
import { ProductForReplace } from '@aftership/returns-logics-core';

import ImageList from '@/components/ImageList';
import useDevice from '@/hooks/useDevice';

const IMAGE_SIZE_DESKTOP = 80;
const IMAGE_SIZE_MOBILE = 60;
const MAX_IMAGE_COUNT_DESKTOP = 5;
const MAX_IMAGE_COUNT_MOBILE = 4;

interface IExchangeItemResolutionProps {
  itemId: string;
  name: string;
  description?: string;
  productsInfoMap?: Record<string, ProductForReplace>;
}

const ExchangeItemResolution = ({
  itemId,
  name,
  description,
  productsInfoMap,
}: IExchangeItemResolutionProps) => {
  const isMobile = useDevice().mobile;

  const productInfo = productsInfoMap?.[itemId] ?? null;
  const variantImages = productsInfoMap
    ? new Array(...new Set(productInfo?.variants?.map((variant) => variant?.variantCoverUrl)))
    : [];

  return (
    <Stack gap='2xs'>
      <Stack direction={'column'} gap='s' flex={1}>
        <Stack direction='column'>
          <Typography variant='bodyLgMediumBold' color='primary'>
            {name}
          </Typography>
          {description && (
            <Typography variant='bodyMd' color='secondary'>
              {description}
            </Typography>
          )}
        </Stack>
        {variantImages.length > 0 && (
          <ImageList
            gap='s'
            showMore={false}
            max={isMobile ? MAX_IMAGE_COUNT_MOBILE : MAX_IMAGE_COUNT_DESKTOP}
            list={variantImages}
            imageSize={isMobile ? IMAGE_SIZE_MOBILE : IMAGE_SIZE_DESKTOP}
          />
        )}
      </Stack>
      <Icon source={ChevronRightOutlined} />
    </Stack>
  );
};

export default ExchangeItemResolution;
