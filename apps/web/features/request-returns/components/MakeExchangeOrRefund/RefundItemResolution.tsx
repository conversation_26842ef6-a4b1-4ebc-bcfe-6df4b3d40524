import { Icon, Stack, Typography } from '@aftership/astra';
import { ChevronRightOutlined } from '@aftership/astra-icons';

interface IRefundItemResolutionProps {
  name: string;
}

const RefundItemResolution = ({ name }: IRefundItemResolutionProps) => {
  return (
    <Stack align='center'>
      <Typography variant='bodyLgMediumBold' style={{ flex: 1 }}>
        {name}
      </Typography>
      <Icon source={ChevronRightOutlined} />
    </Stack>
  );
};

export default RefundItemResolution;
