import { ExchangeMode, ReplaceTheSameItemSubFlowActorRef } from '@aftership/returns-logics-core';
import { useSelector } from 'returns-logics/react';

import { useMainFlow } from '@/hooks/useMainFlow';

export const useReplacementPage = () => {
  const mainFlow = useMainFlow();

  const { context: mainContext, children: mainChildren } = mainFlow;
  const activeOrderItem = useSelector(
    mainChildren.itemSelectionSubFlow,
    (state) => state?.context.activeOrderItem,
  );
  const itemId = activeOrderItem?.external_id;

  const itemSelectionSubFlowActiveChildren = useSelector(
    mainChildren.itemSelectionSubFlow,
    (state) => state?.children?.[itemId!],
  );
  const replaceTheSameItemSubFlow = useSelector(
    itemSelectionSubFlowActiveChildren,
    // @ts-ignore
    (state) => state?.children?.replaceTheSameItemSubFlow,
  ) as ReplaceTheSameItemSubFlowActorRef;

  const isMerchantMode = !!mainContext.orderLookup?.isMerchantMode;

  const replaceTheSameItemContextMemory = useSelector(
    replaceTheSameItemSubFlow,
    (state) => state?.context,
  );

  const enableVariant =
    mainContext?.storeConfig?.shopInfo?.exchange_mode === ExchangeMode.DifferentPrice ||
    mainContext?.storeConfig?.shopInfo?.exchange_mode === ExchangeMode.SamePrice;

  const activeSelectedItem = replaceTheSameItemContextMemory?.activeSelectedItem;

  const isShowComment = Boolean(
    mainContext?.storeConfig?.shopInfo?.exchange_mode === ExchangeMode.NotesOnly ||
      mainContext?.storeConfig?.shopInfo?.exchange_rule_allow_add_notes,
  );

  const replaceTheSameItemFlowStep = useSelector(
    replaceTheSameItemSubFlow,
    (state) => state?.value,
  );
  const replaceTheSameItemDispatch = replaceTheSameItemSubFlow?.send;
  return {
    mainFlowStepName: mainFlow?.currentStep?.name,
    replaceTheSameItemFlowStep,
    enableVariant,
    activeSelectedItem,
    isMerchantMode,
    selectedItems: replaceTheSameItemContextMemory?.selectedItems ?? [],
    replaceTheSameItemDispatch,
    isShowComment,
  };
};
