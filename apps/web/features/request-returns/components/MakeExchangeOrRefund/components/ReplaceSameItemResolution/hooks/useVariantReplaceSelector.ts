import {
  ReplaceTheSameItemSubFlowActorRef,
  SelectSingleReplaceItemSubFlowActorRef,
} from '@aftership/returns-logics-core';
import { useSelector } from 'returns-logics/react';

import useFlowSelector from '@/hooks/useFlowSelector';

export const useVariantReplaceSelector = (itemId: string) => {
  const { flow: itemSelectionSubFlow } = useFlowSelector('itemSelectionSubFlow');
  const itemSelectionSubFlowActiveChildren = useSelector(
    itemSelectionSubFlow,
    (state) => state?.children?.[itemId!],
  );

  const replaceTheSameItemSubFlowFromItemSelection = useSelector(
    itemSelectionSubFlowActiveChildren,
    // @ts-ignore
    (state) => state?.children?.replaceTheSameItemSubFlow,
  ) as ReplaceTheSameItemSubFlowActorRef;

  const selectSingleReplaceItemSubFlow = useSelector(
    replaceTheSameItemSubFlowFromItemSelection,
    // @ts-ignore
    (state) => state?.children?.[itemId!],
  ) as unknown as SelectSingleReplaceItemSubFlowActorRef;

  const { send: dispatch } = selectSingleReplaceItemSubFlow || {};

  const context = useSelector(selectSingleReplaceItemSubFlow, (state) => state?.context);

  return {
    context,
    dispatch,
  };
};
