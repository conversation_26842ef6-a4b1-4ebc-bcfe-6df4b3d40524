import { ReplaceTheSameItemSubFlowActorRef } from '@aftership/returns-logics-core';
import { useSelector } from 'returns-logics/react';

import { useMainFlow } from '@/hooks/useMainFlow';

export const useWithoutVariantReplaceSelector = (itemId: string) => {
  const mainFlow = useMainFlow();

  const mainChildren = mainFlow?.children;
  const itemSelectionSubFlow = mainChildren?.itemSelectionSubFlow;
  const itemSelectionSubFlowActiveChildren = useSelector(
    itemSelectionSubFlow,
    (state) => state?.children?.[itemId!],
  );

  const replaceTheSameItemSubFlowFromItemSelection = useSelector(
    itemSelectionSubFlowActiveChildren,
    // @ts-ignore
    (state) => state?.children?.replaceTheSameItemSubFlow,
  ) as ReplaceTheSameItemSubFlowActorRef;

  const { send: dispatch } = replaceTheSameItemSubFlowFromItemSelection || {};

  const context = useSelector(
    replaceTheSameItemSubFlowFromItemSelection,
    (state) => state?.context,
  );

  return {
    context,
    dispatch,
  };
};
