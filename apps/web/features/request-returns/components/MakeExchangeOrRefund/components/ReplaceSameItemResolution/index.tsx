import { useCallback } from 'react';

import { ReplaceItemOverlay } from '@/features/return-replacement/components/ReplaceItemOverlay';
import getTrackerInstance from '@/utils/tracker';
import { EventName } from '@/utils/tracker/consts';

import { useReplacementPage } from './hooks/useReplacementPage';
import { useVariantReplaceSelector } from './hooks/useVariantReplaceSelector';

const ReplaceSameItemResolution = ({
  isOpen,
  onClose,
  onBack,
}: {
  isOpen: boolean;
  onClose: () => void;
  onBack: () => void;
}) => {
  const { activeSelectedItem } = useReplacementPage();

  const { context, dispatch } = useVariantReplaceSelector(activeSelectedItem?.itemId!);

  const handleDone = (comment: string) => {
    dispatch?.({
      type: 'SELECT_ITEM_DONE',
      data: {
        itemId: activeSelectedItem?.itemId!,
        variant: context?.selectedVariant!,
        replaceComment: comment,
      },
    });
    getTrackerInstance().reportClickEvent({
      eventName: EventName.clickItemReplaceDone,
    });
  };

  const handleSelectOption = useCallback(
    (optionName: string, value: string) => {
      dispatch?.({
        type: 'SELECT_OPTION_ITEM',
        data: {
          name: optionName,
          value: value,
        },
      });
    },
    [dispatch],
  );

  if (!context) {
    return null;
  }

  return (
    <ReplaceItemOverlay
      isOpen={isOpen}
      onDone={handleDone}
      onClose={onClose}
      onBack={onBack}
      context={context}
      onSelectOption={handleSelectOption}
    />
  );
};

export default ReplaceSameItemResolution;
