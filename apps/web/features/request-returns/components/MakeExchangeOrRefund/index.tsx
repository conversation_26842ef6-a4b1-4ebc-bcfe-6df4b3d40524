import { Stack } from '@aftership/astra';
import { tokenVars } from '@aftership/astra-tokens/Contract.css';
import { usePreviewContext } from '@aftership/preview-kit/client';
import { ItemResolution, ProductForReplace } from '@aftership/returns-logics-core';

import { ListBox, ListBoxItem } from '@/components/ListBox';
import { SelectableItem } from '@/features/request-returns/components/SelectableItem';
import getTrackerInstance from '@/utils/tracker';
import { EventName } from '@/utils/tracker/consts';

import ExchangeItemResolution from './ExchangeItemResolution';
import RefundItemResolution from './RefundItemResolution';

const MakeExchangeOrRefund = ({
  selectItemResolution,
  selectedItemResolution,
  productsInfoMap,
  itemId,
  itemResolutionIterable,
}: {
  selectedItemResolution?: ItemResolution;
  selectItemResolution: (type: ItemResolution) => void;
  itemId: string;
  productsInfoMap?: Record<string, ProductForReplace>;
  itemResolutionIterable: {
    id: ItemResolution;
    name: string;
    description?: string;
    allow: boolean;
  }[];
}) => {
  const { isPreview } = usePreviewContext();

  return (
    <Stack direction={'column'} gap='s'>
      <ListBox
        selectionMode='single'
        rowGap={tokenVars.Semantic.Space.S}
        items={itemResolutionIterable?.filter((item) => item.allow)}
        selectedKeys={selectedItemResolution ? [selectedItemResolution] : []}
      >
        {(item) => {
          if (item.id === ItemResolution.Exchange) {
            return (
              <ListBoxItem>
                <SelectableItem
                  key={item.id}
                  content={
                    <ExchangeItemResolution
                      itemId={itemId}
                      name={item.name}
                      description={item.description}
                      productsInfoMap={productsInfoMap}
                    />
                  }
                  onClick={() => {
                    if (!isPreview) {
                      selectItemResolution(item.id);
                      getTrackerInstance().reportClickEvent({
                        eventName: EventName.selectItemResolution,
                        payload: {
                          choice: 'replace_item',
                        },
                      });
                    }
                  }}
                />
              </ListBoxItem>
            );
          }

          if (item.id === ItemResolution.Refund) {
            return (
              <ListBoxItem>
                <SelectableItem
                  key={item.id}
                  content={<RefundItemResolution name={item.name} />}
                  onClick={() => {
                    if (!isPreview) {
                      selectItemResolution(item.id);
                      getTrackerInstance().reportClickEvent({
                        eventName: EventName.selectItemResolution,
                        payload: {
                          choice: 'return_item',
                        },
                      });
                    }
                  }}
                />
              </ListBoxItem>
            );
          }

          return null;
        }}
      </ListBox>
    </Stack>
  );
};

export default MakeExchangeOrRefund;
