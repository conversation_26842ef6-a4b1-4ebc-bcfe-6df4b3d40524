import { t } from 'i18next';
import React from 'react';

import { Box, Icon, Link, Stack, Typography } from '@aftership/astra';
import { ChevronRightOutlined } from '@aftership/astra-icons';
import { semanticTokenVar } from '@aftership/astra-tokens/semantic-token/Contract.css';

import { Divider } from '@/components/Divider';
import { NonReturnableTitleText } from '@/features/preview/components/WithPreviewSection';
import { NonReturnableItem } from '@/features/request-returns/components/NonReturnableItem';
import { NonReturnableItemProps } from '@/features/request-returns/components/NonReturnableItem/NonReturnableItem';
import { NonReturnableContainerStyle } from '@/features/request-returns/components/NonReturnableList/styles.css.ts';
import { useGetPrefiedLink } from '@/features/returns/hooks/useUniversalRouting';
import useDevice from '@/hooks/useDevice';

export interface NonReturnableListProps {
  policy: {
    policyEnable: boolean;
    policyUrl?: string;
  };
  nonReturnableItems: Array<NonReturnableItemProps>;
}

const { Space, Color } = semanticTokenVar;

const NonReturnableList = ({
  policy: { policyEnable, policyUrl },
  nonReturnableItems,
}: NonReturnableListProps) => {
  const isMobile = useDevice().mobile;
  const policyInternalLink = useGetPrefiedLink('/return-policy');

  let url;
  if (policyEnable && !!policyUrl) {
    url = policyUrl;
  } else if (!policyEnable) {
    url = policyInternalLink;
  }

  return (
    <Stack
      gap='m'
      direction={'column'}
      style={
        isMobile
          ? { marginBlockStart: Space['2Xl'] }
          : { marginBlockStart: Space.L, paddingBlockStart: Space['2Xs'] }
      }
    >
      <Stack direction={'column'}>
        <NonReturnableTitleText variant='heading2Xs'>
          {t('page.request.nonReturnableTitle')}
        </NonReturnableTitleText>
        {url && (
          <Stack direction={'row'} align={'center'} gap='2xs'>
            <Link
              href={url}
              target={'_blank'}
              showUnderline={false}
              style={{ color: Color.Text.Primary }}
            >
              <Typography variant='bodyMd'>{t('page.landing.viewFullPolicy')}</Typography>
            </Link>
            <Icon source={ChevronRightOutlined} color='secondary' />
          </Stack>
        )}
      </Stack>
      <Box className={NonReturnableContainerStyle} padding={Space.M}>
        {nonReturnableItems.map((item, index, array) => (
          <React.Fragment key={item.itemId}>
            <NonReturnableItem {...item} />
            {index !== array.length - 1 && <Divider spacing={Space.M} />}
          </React.Fragment>
        ))}
      </Box>
    </Stack>
  );
};

export default NonReturnableList;
