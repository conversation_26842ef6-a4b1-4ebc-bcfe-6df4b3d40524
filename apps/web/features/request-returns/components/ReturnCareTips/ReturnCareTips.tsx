import { t } from 'i18next';
import Image from 'next/image';
import React from 'react';

import { Box, Stack, Typography } from '@aftership/astra';
import { tokenVars } from '@aftership/astra-tokens/Contract.css';

import useDevice from '@/hooks/useDevice.ts';

import { containerStyle } from './styles.css.ts';

const { Space, Radius } = tokenVars.Semantic;
const { Color } = tokenVars.Primitive;

const ReturnCareTips = () => {
  const isMobile = useDevice().mobile;
  return (
    <Box className={containerStyle} paddingX={Space.M} paddingY={Space.Xs} borderRadius={Radius.M}>
      <Stack gap={isMobile ? '2xs' : 'xs'} align={'center'}>
        <Image
          src={require('@/assets/ic-return-care.png').default?.src}
          width={24}
          height={24}
          alt='returnCare'
        />
        <Typography variant='bodyLg' style={{ color: Color.Orange[1200] }}>
          {t('page.banner.returnCare')}
        </Typography>
      </Stack>
    </Box>
  );
};

export default ReturnCareTips;
