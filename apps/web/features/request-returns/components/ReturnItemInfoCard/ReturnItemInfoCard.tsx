import React from 'react';

import { Stack, Typography } from '@aftership/astra';
import { semanticTokenVar } from '@aftership/astra-tokens/semantic-token/Contract.css';
import { PresentmentMoney } from '@aftership/returns-logics-core';

import { EllipsisText } from '@/components/EllipsisText';
import { ImageWithFallback } from '@/components/ImageWithFallback';
import { productTitleStyle } from '@/features/request-returns/components/ReturnItemInfoCard/styles.css.ts';
import useDevice from '@/hooks/useDevice';
import { generatePriceWithQuantityString } from '@/utils/price.ts';

const { Radius, Space } = semanticTokenVar;

export interface ReturnItemInfoCardProps {
  productTitle: string;
  selectedQuantity?: number;
  productCoverUrl?: string;
  variantTitle?: string;
  price?: PresentmentMoney;
}

const ReturnItemInfoCard = ({
  productTitle,
  variantTitle,
  price,
  productCoverUrl,
  selectedQuantity,
}: ReturnItemInfoCardProps) => {
  const isMobile = useDevice().mobile;

  if (isMobile) {
    return (
      <Stack gap='m' style={{ paddingInline: Space.M, paddingBlockEnd: Space.Xl }}>
        <ImageWithFallback
          usingShopifyPreview
          width={'100'}
          height={'100'}
          src={productCoverUrl || ''}
          alt={productTitle}
          style={{ borderRadius: Radius.Xs }}
        />

        <Stack direction='column' gap='2xs' style={{ width: '100%' }}>
          <Stack direction='column' gap='none'>
            <EllipsisText variant='bodyLgSemibold' color='primary' text={productTitle} />
            {variantTitle && (
              <EllipsisText variant={'bodyMd'} color='secondary' text={variantTitle} />
            )}
          </Stack>
          <Typography variant='bodyMd' color='primary'>
            {generatePriceWithQuantityString(price, selectedQuantity)}
          </Typography>
        </Stack>
      </Stack>
    );
  }

  return (
    <Stack justify={'center'} style={{ height: '100%', width: '100%' }}>
      <Stack
        gap='xl'
        direction={'column'}
        justify={'center'}
        align={'center'}
        style={{ height: '100%', width: '100%' }}
      >
        <ImageWithFallback
          usingShopifyPreview
          width={'200'}
          height={'200'}
          src={productCoverUrl || ''}
          alt={productTitle}
          style={{ borderRadius: Radius.Xs }}
        />
        <Stack direction={'column'} gap='xs' style={{ width: '100%' }}>
          <Stack direction={'column'}>
            <Typography variant='bodyLgSemibold' color='primary' className={productTitleStyle}>
              {productTitle}
            </Typography>
            {variantTitle && (
              <Typography variant={'bodyMd'} color='secondary'>
                {variantTitle}
              </Typography>
            )}
          </Stack>
          <Typography variant={'bodyMd'} color='primary'>
            {generatePriceWithQuantityString(price, selectedQuantity)}
          </Typography>
        </Stack>
      </Stack>
    </Stack>
  );
};

export default ReturnItemInfoCard;
