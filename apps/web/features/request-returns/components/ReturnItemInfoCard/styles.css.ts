import { ComplexStyleRule, style } from '@vanilla-extract/css';

import { primitiveTokenVar } from '@aftership/astra-tokens/primitive-token/Contract.css';

const { Size, Space } = primitiveTokenVar;

export const productTitleStyle = style({
  display: '-webkit-box',
  '-webkit-box-orient': 'vertical',
  '-webkit-line-clamp': '3',
  overflow: 'hidden',
  maxHeight: Size[2400],
  'text-overflow': 'ellipsis',
} as ComplexStyleRule);

export const productContainerMobileStyle = style({
  overflowX: 'hidden',
  flex: 1,
  flexBasis: 0,
  display: 'flex',
  flexDirection: 'column',
  gap: Space[100],
});
