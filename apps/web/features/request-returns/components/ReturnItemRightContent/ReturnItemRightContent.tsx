import { t } from 'i18next';
import React from 'react';

import { Icon, IconButton, Stack } from '@aftership/astra';
import { ChevronRightOutlined, ChevronUpOutlined, CloseOutlined } from '@aftership/astra-icons';

import { Button } from '@/components/Button';
import useDevice from '@/hooks/useDevice';

export interface ReturnItemRightContentProps {
  isSelected: boolean;
  expanded: boolean;
  expandability: boolean;
  onEdit: () => void;
  onRemove: () => void;
}

const ReturnItemRightContent = ({
  isSelected,
  expanded,
  expandability,
  onRemove,
  onEdit,
}: ReturnItemRightContentProps) => {
  const isMobile = useDevice().mobile;
  if (isSelected) {
    return isMobile ? (
      <IconButton
        icon={CloseOutlined}
        size='medium'
        onPress={onRemove}
        style={{ alignSelf: 'flex-start' }}
      />
    ) : (
      <Stack align={'center'} gap='none' style={{ alignSelf: 'flex-start' }}>
        <Button variant='plain' color='default' size='small' onPress={onRemove}>
          {t('page.request.remove')}
        </Button>
        <Button variant='plain' color='default' size='small' onPress={onEdit}>
          {t('page.request.edit')}
        </Button>
      </Stack>
    );
  } else if (expanded && !expandability) {
    return null;
  } else if (expanded && expandability) {
    return (
      <Stack direction={'column'} justify={'center'} style={{ alignSelf: 'center' }}>
        <Icon source={ChevronUpOutlined} />
      </Stack>
    );
  } else {
    return (
      <Stack direction={'column'} justify={'center'} style={{ alignSelf: 'center' }}>
        <Icon source={expanded ? ChevronUpOutlined : ChevronRightOutlined} />
      </Stack>
    );
  }
};

export default ReturnItemRightContent;
