import { t } from 'i18next';
import React from 'react';

import { Box, Pressable, Stack, Typography, useToast } from '@aftership/astra';
import { primitiveTokenVar } from '@aftership/astra-tokens/primitive-token/Contract.css';
import { semanticTokenVar } from '@aftership/astra-tokens/semantic-token/Contract.css';
import { SelectionGroupingItem } from '@aftership/returns-logics-core';

import { Button } from '@/components/Button';
import { ItemCardContent } from '@/components/ItemCardContent';
import { ListBox, ListBoxItem } from '@/components/ListBox';
import { lineItemBoxStyle } from '@/features/request-returns/components/ReturnableItem/styles.css.ts';
import { SelectedItem } from '@/features/request-returns/types';
import { queryReturnItemStatus } from '@/features/request-returns/utils/returnableItemHelper';
import useDevice from '@/hooks/useDevice.ts';

import { ReturnItemRightContent } from '../ReturnItemRightContent';
import { SelectedReturnInfoBanner } from '../SelectedReturnInfoBanner';
import WarningTips from '../WarningTips/WarningTips.tsx';

export interface Props {
  /**
   * 商品的基础信息
   */
  returnableItem: RequestReturn.ReturnableItemProps;
  /**
   * 选中的 item  + line item 列表
   */
  selectedItems: Array<SelectedItem>;
  /**
   * 所有 item  + line item 可退的组合
   */
  itemSelectionGrouping: Array<SelectionGroupingItem>;
  /**
   * 是否限制每次退货只能退一种商品
   */
  isLimitSingleItemPerReturn: boolean;
  /**
   * 是否开启了可选 item 受 routing rule 影响
   */
  isLimitSelectionByRoutingRule: boolean;
  onEdit: (itemId: string, parentId: Maybe<string>) => void;
  onRemove: (itemId: string, parentId: Maybe<string>) => void;
  /**
   * 展开中的 id
   */
  expandId: string;
  /**
   * warning 的提示文案
   */
  warningTips?: string;
  onExpand?: (itemId: string) => void;
}

const { Space } = semanticTokenVar;
const { Color } = primitiveTokenVar;

const ReturnableItem = ({
  returnableItem,
  selectedItems,
  itemSelectionGrouping,
  isLimitSelectionByRoutingRule,
  isLimitSingleItemPerReturn,
  onRemove,
  onEdit,
  expandId,
  onExpand,
}: Props) => {
  const {
    returnInfo,
    isSelected,
    cantSelectedByLimit,
    canWholeReturn,
    cantSelectedByRoutingRule,
    expanded,
    expandability,
  } = queryReturnItemStatus(
    returnableItem,
    selectedItems,
    itemSelectionGrouping,
    expandId,
    isLimitSingleItemPerReturn,
  );
  const { showToast } = useToast();
  const {
    itemId,
    parentId,
    productTitle,
    productCoverUrl,
    variantTitle,
    returnableQuantity,
    price,
    originPrice,
    productTags,
    bundles,
    overridingReason,
  } = returnableItem;
  const isMobile = useDevice().mobile;

  const footer = () => {
    if (returnInfo) {
      return (
        <SelectedReturnInfoBanner
          selectedCount={returnInfo.returnedQuantity}
          itemId={itemId}
          onEdit={() => onEdit(itemId, parentId)}
        />
      );
    } else if (expanded && bundles) {
      const selectedKeys = selectedItems.map((item) => item.itemId);
      return (
        <Stack direction={'column'} gap='m' style={{ marginTop: Space.Xs }}>
          <ListBox
            rowGap={Space.S}
            selectionMode='single'
            items={bundles.map((item) => ({
              id: item.itemId,
              ...item,
            }))}
            selectedKeys={selectedKeys}
          >
            {(item) => {
              return (
                <ListBoxItem className={lineItemBoxStyle}>
                  {() => {
                    return (
                      <ReturnableItem
                        key={item.itemId}
                        // line item 不存在展开的情况
                        expandId={''}
                        returnableItem={item}
                        selectedItems={selectedItems}
                        itemSelectionGrouping={itemSelectionGrouping}
                        onRemove={onRemove}
                        onEdit={onEdit}
                        isLimitSelectionByRoutingRule={isLimitSelectionByRoutingRule}
                        isLimitSingleItemPerReturn={isLimitSingleItemPerReturn}
                      />
                    );
                  }}
                </ListBoxItem>
              );
            }}
          </ListBox>

          {canWholeReturn && (
            <Box width={'auto'} paddingX={Space ? 0 : '134px'}>
              <Button
                variant={'basic'}
                isFullWidth={true}
                size={'large'}
                onPress={() => {
                  onEdit?.(itemId, parentId);
                }}
              >
                {t('page.description.ReturnBundleAction')}
              </Button>
            </Box>
          )}
        </Stack>
      );
    } else if (cantSelectedByRoutingRule || cantSelectedByLimit) {
      return (
        <WarningTips
          tips={t('page.description.limitReturn.separate')}
          marginTop={isMobile ? 0 : Space['2Xs']}
        />
      );
    }
    return null;
  };

  return (
    <Pressable
      width={'100%'}
      onPress={() => {
        if (cantSelectedByLimit || cantSelectedByRoutingRule) {
          // 如果不可选,需要提醒
          if (!expanded) {
            showToast(t('page.description.limitReturnWarn'));
          }
        } else if (expandability) {
          onExpand?.(itemId);
        } else if (!expanded) {
          onEdit(itemId, parentId);
        }
      }}
    >
      <Box padding={Space.M} width={'100%'}>
        <Stack direction={'column'} gap={isMobile ? 'm' : 'xs'}>
          {/* Content */}
          <div style={{ opacity: cantSelectedByRoutingRule || cantSelectedByLimit ? '0.5' : '1' }}>
            <ItemCardContent
              productTitleVariant='bodyLg'
              productInfo={{
                productTitle: productTitle,
                variantTitle: variantTitle,
                productTags: productTags,
                price: price,
                quantity: returnableQuantity,
                originPrice: originPrice,
                productCoverUrl: productCoverUrl,
              }}
              rightContent={
                <ReturnItemRightContent
                  expanded={expanded}
                  expandability={expandability}
                  isSelected={isSelected}
                  onRemove={() => onRemove(itemId, parentId)}
                  onEdit={() => onEdit(itemId, parentId)}
                />
              }
            />
          </div>
          {overridingReason && (
            <Typography variant='bodyLg' style={{ color: Color.Teal[700] }}>
              {`${t('page.tip.overrideRulePrefix')}: ${overridingReason}`}
            </Typography>
          )}
          {footer()}
        </Stack>
      </Box>
    </Pressable>
  );
};

export default ReturnableItem;
