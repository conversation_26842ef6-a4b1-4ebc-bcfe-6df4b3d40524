import React from 'react';

import { Box, Pressable, Typography } from '@aftership/astra';
import { semanticTokenVar } from '@aftership/astra-tokens/semantic-token/Contract.css';

import useDevice from '@/hooks/useDevice';

export interface SelectableItemProps {
  content: React.ReactNode;
  onClick: VoidFunction;
}

const { Space } = semanticTokenVar;

const SelectableItem = ({ content, onClick }: SelectableItemProps) => {
  const isMobile = useDevice().mobile;
  return (
    <Pressable onPress={onClick} width={'100%'}>
      <Box padding={isMobile ? Space.M : Space.L} width={'100%'}>
        <Typography style={{ wordBreak: 'break-word' }}>{content}</Typography>
      </Box>
    </Pressable>
  );
};

export default SelectableItem;
