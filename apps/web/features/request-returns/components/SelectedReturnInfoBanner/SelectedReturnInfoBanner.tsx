import React, { useMemo } from 'react';
import { useTranslation } from 'react-i18next';

import { Box, Icon, Stack, Typography, UnstyledButton } from '@aftership/astra';
import { BagCheckedFilled, PackageFilled } from '@aftership/astra-icons';
import { primitiveTokenVar } from '@aftership/astra-tokens/primitive-token/Contract.css';
import { semanticTokenVar } from '@aftership/astra-tokens/semantic-token/Contract.css';

import useDevice from '@/hooks/useDevice';
import useFlowSelector from '@/hooks/useFlowSelector';
import { useMainFlow } from '@/hooks/useMainFlow';

export interface Props {
  selectedCount: number;
  onEdit?: () => void;
  itemId: string;
}

const { Space } = semanticTokenVar;
const { Color: PrimitiveColor } = primitiveTokenVar;

const SelectedReturnInfoBanner = ({ selectedCount, itemId, onEdit }: Props) => {
  const { context } = useFlowSelector('itemSelectionSubFlow');
  const { t } = useTranslation();
  const isMobile = useDevice().mobile;
  const flow = useMainFlow();
  const isMultipleResolution = flow?.context?.isMultipleResolution;

  const currentItem = context?.selectedItems?.find((item) => item.itemId == itemId);

  const text = useMemo(() => {
    const isExchange = !!currentItem?.exchangeVariant;
    if (!isExchange || !isMultipleResolution) {
      return selectedCount > 1
        ? t('v2.page.description.selectedCount_other', {
            selectedCount: `× ${selectedCount}`,
          })
        : t('v2.page.description.selectedCount_one', {
            selectedCount: `× ${selectedCount}`,
          });
    }

    if (isExchange) {
      const variantTitle =
        currentItem?.exchangeVariant?.variantTitle || currentItem?.exchangeVariant?.productTitle;
      return t('v2.page.description.exchangeCount', {
        variant: variantTitle,
        selectedCount,
      });
    }
  }, [isMultipleResolution, selectedCount, t, currentItem]);

  return (
    <Box
      backgroundColor={PrimitiveColor.Black_Alpha[100]}
      borderRadius={Space.Xs}
      paddingX={Space.M}
      paddingY={isMobile ? Space.S : Space.Xs}
    >
      <Stack gap='xs' align={'center'}>
        <Icon source={currentItem?.exchangeVariant ? BagCheckedFilled : PackageFilled} />
        <Box flex={1} flexBasis={0}>
          <Typography variant={'bodyMdSemibold'} color='primary'>
            {text}
          </Typography>
        </Box>
        {isMobile && (
          <UnstyledButton
            onPress={() => onEdit?.()}
            style={{
              paddingBlock: Space.S,
              paddingInline: Space.M,
              marginBlock: `calc(-1 * ${Space.S})`,
              marginInline: `calc(-1 * ${Space.M})`,
            }}
          >
            <Typography variant='bodyMd' color='primary'>
              {t('page.request.edit')}
            </Typography>
          </UnstyledButton>
        )}
      </Stack>
    </Box>
  );
};

export default SelectedReturnInfoBanner;
