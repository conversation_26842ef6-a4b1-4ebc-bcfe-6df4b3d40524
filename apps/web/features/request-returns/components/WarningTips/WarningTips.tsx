import React from 'react';

import { Box, DimensionValue, Icon, Stack, Typography } from '@aftership/astra';
import { AlertTriangleFilled } from '@aftership/astra-icons';

export interface Props {
  tips: string;
  color?: string;
  alignItems?: 'center' | 'start';
  marginTop?: DimensionValue;
}

const WarningTips = ({ tips, alignItems = 'center', marginTop = 0 }: Props) => {
  return (
    <Stack gap='xs' align={alignItems} style={{ marginTop }}>
      <Box paddingY={'2px'}>
        <Icon source={AlertTriangleFilled} color='secondary' />
      </Box>
      <Typography variant='bodyLg' color='primary'>
        {tips}
      </Typography>
    </Stack>
  );
};

export default WarningTips;
