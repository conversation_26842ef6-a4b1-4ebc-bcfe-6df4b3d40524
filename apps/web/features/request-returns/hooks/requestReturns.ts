import { useFieldAWithMemo } from '@/hooks/useFieldAWithMemo';
import useFlowSelector from '@/hooks/useFlowSelector';
import { useMainFlow } from '@/hooks/useMainFlow';

export const useRequestReturnsInfo = () => {
  const mainFlow = useMainFlow();
  const mainContext = mainFlow.context;
  const mainCurrentStep = mainFlow.currentStep;

  const policyExternalLink = mainContext?.storeConfig?.shopInfo?.policy_url;
  const policyEnable = mainContext?.storeConfig?.shopInfo?.external_return_policy_page ?? false;
  const isMerchantMode = !!mainContext?.orderLookup?.isMerchantMode;

  const {
    dispatch,
    context: itemSelectionSubFlowSnapshot,
    currentStep,
    matches,
  } = useFlowSelector('itemSelectionSubFlow');

  const isEFAModalDecisionLoading =
    matches?.({ postReturnPreviewActor: 'loading' }) ||
    matches?.({ getMatchingResolutions: 'loading' });

  const itemSelectionContext = useFieldAWithMemo(itemSelectionSubFlowSnapshot);

  const isReturnCare = itemSelectionContext?.orderInfo?.return_care?.eligible ?? false;
  return {
    mainCurrentStep,
    itemSelectionCurrentStep: currentStep,
    itemSelectionDispatch: dispatch,
    policyExternalLink,
    policyEnable,
    isMerchantMode,
    isReturnCare,
    orderIneligibility: mainContext.request?.orders?.order_ineligibilities,
    itemSelectionSubFlowSnapshot,
    isEFAModalDecisionLoading,
  };
};
