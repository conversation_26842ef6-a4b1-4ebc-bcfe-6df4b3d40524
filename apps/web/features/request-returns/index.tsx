import { t } from 'i18next';
import React, { useEffect, useState } from 'react';

import { Box, Spinner, Stack } from '@aftership/astra';
import { tokenVars } from '@aftership/astra-tokens/Contract.css';
import { EfaMethod, SelectSingleItemSubFlowActorRef } from '@aftership/returns-logics-core';
import { useSelector } from 'returns-logics/react';

import { SomethingWentWrong } from '@/components/SomethingWentWrong';
import { RequestReturnsCardTitleText } from '@/features/preview/components/WithPreviewSection';
import { ConfirmComp } from '@/features/request-returns/components/ConfirmComp';
import { FillReturnOverlay } from '@/features/request-returns/components/FillReturnOverlay';
import { NonReturnableList } from '@/features/request-returns/components/NonReturnableList';
import { ReturnableList } from '@/features/request-returns/components/ReturnableList';
import { useRequestReturnsInfo } from '@/features/request-returns/hooks';
import { RefundOrEfaOverlay } from '@/features/resolution/components/RefundOrEfaOverlay';
import useDevice from '@/hooks/useDevice.ts';
import { useFieldAWithMemo } from '@/hooks/useFieldAWithMemo';
import useFlowSelector from '@/hooks/useFlowSelector.ts';
import { useItemSelectionExchangeOrRefundFlow } from '@/hooks/useItemSelectionExchangeOrRefundFlow.ts';
import { useShopInfo } from '@/hooks/useShopInfo';
import { EventName } from '@/utils/tracker/consts.ts';
import getTrackerInstance from '@/utils/tracker/index.ts';

import ReplaceSameItemResolution from './components/MakeExchangeOrRefund/components/ReplaceSameItemResolution/index.tsx';
import { OrderWarningTips } from './components/OrderWarningTips';
import ReturnCareTips from './components/ReturnCareTips/ReturnCareTips.tsx';
import { wrapperStyle } from './style.css.ts';
import {
  convertISelectedItem2SelectedItem,
  convertOderItem2NonReturnableItem,
  convertOderItem2ReturnableItem,
} from './utils/convertor';

import NextButton from '../../components/NextButton/NextButton.tsx';
import ScrollFlex from '../../components/ScrollFlex/ScrollFlex.tsx';
import { useScrollUpdate } from '../atta/useSrollUpdate';
import { xStateMetaData } from '../returns/hooks/useSyncXStateAndRoute';

const { Space } = tokenVars.Semantic;

const RequestReturns = () => {
  const isMobile = useDevice().mobile;
  const [pendingDeleteItemId, setPendingDeleteItemId] = useState<string>('');

  const { exchangeForAnythingHeroImage, exchangeForAnythingShoppingChannel } = useShopInfo();
  const {
    policyEnable,
    policyExternalLink,
    mainCurrentStep,
    isMerchantMode,
    isReturnCare,
    orderIneligibility,
    itemSelectionCurrentStep,
    itemSelectionDispatch,
    itemSelectionSubFlowSnapshot,
    isEFAModalDecisionLoading,
  } = useRequestReturnsInfo();

  const contextMemory = useFieldAWithMemo(itemSelectionSubFlowSnapshot);
  const efaMethod = exchangeForAnythingShoppingChannel ?? EfaMethod.inApp;
  const [hasUnexpectedError, setUnexpectedError] = useState(false);

  const { flow: itemSelectionSubFlow } = useFlowSelector('itemSelectionSubFlow');

  useEffect(() => {
    const errorSubscription = itemSelectionSubFlow?.on('error', () => {
      setUnexpectedError(true);
    });
    return () => {
      errorSubscription?.unsubscribe();
    };
  }, [itemSelectionSubFlow]);

  const itemSelectionSubFlowActiveChildren = useSelector(
    itemSelectionSubFlow,
    (state) => state?.children?.[contextMemory?.activeOrderItem?.external_id!],
  ) as SelectSingleItemSubFlowActorRef;

  const itemSelectionSubFlowStepName = useSelector(
    itemSelectionSubFlowActiveChildren,
    (state) => state?.value,
  );

  const {
    isLoading,
    isChooseEFAOrRefund,
    isJumpingEFA,
    extraCreditAmount,
    selectedItemsWithCreditAmountString,
    efaPreDiscountCreditAmount,
    selectedItemsAmount,
    products,
    dispatch,
  } = useItemSelectionExchangeOrRefundFlow();

  const itemSelectionSubFlowStepDispatch = itemSelectionSubFlowActiveChildren?.send;

  const onBack = () => {
    itemSelectionSubFlowStepDispatch?.({
      type: 'BACK',
    });
  };

  const onClose = () => {
    itemSelectionDispatch?.({
      type: 'CANCEL_ITEM',
      data: {
        itemId: contextMemory?.activeOrderItem?.external_id!,
      },
    });
  };

  const scrollClassName = useScrollUpdate();

  if (itemSelectionCurrentStep?.isLoading && !isEFAModalDecisionLoading) {
    return (
      <Stack flex={1} align={'center'} justify={'center'}>
        <Spinner size='large' />
      </Stack>
    );
  }

  const returnableItems =
    contextMemory?.returnableItems?.map<RequestReturn.ReturnableItemProps>((item) =>
      convertOderItem2ReturnableItem(item),
    ) ?? [];
  const itemSelectionGrouping = contextMemory?.itemSelectionGrouping ?? [];

  const nonReturnableItems =
    contextMemory?.nonReturnableItems?.map((item) => convertOderItem2NonReturnableItem(item)) ?? [];
  const selectedItems =
    contextMemory?.selectedItems?.map((item) => convertISelectedItem2SelectedItem(item)) ?? [];
  const activeOrderItem = contextMemory?.activeOrderItem;

  const eligibilityRules = contextMemory?.shopEligibilityRules;
  // 是否限制每次退货只能退一种商品
  const isLimitSingleItemPerReturn =
    Boolean(eligibilityRules?.product_limit?.feature_applied) &&
    Boolean(eligibilityRules?.product_limit?.limit_single_item_per_return?.enabled);
  // 是否开启了可选 item 受 routing rule 影响
  const isLimitSelectionByRoutingRule =
    Boolean(eligibilityRules?.product_limit?.feature_applied) &&
    Boolean(eligibilityRules?.product_limit?.limit_items_with_common_routing_rule?.enabled);

  const handleEditItem = (itemId: string, parentId: Maybe<string>) => {
    let currentSelectedOrderItem;
    if (parentId) {
      currentSelectedOrderItem = contextMemory?.returnableItems
        ?.find((item) => item.external_id === parentId)
        ?.bundledItems?.find((item) => item.external_id === itemId);
    } else {
      currentSelectedOrderItem = contextMemory?.returnableItems?.find(
        (item) => item.external_id === itemId,
      );
    }

    if (currentSelectedOrderItem) {
      getTrackerInstance().reportClickEvent({ eventName: EventName.selectItem });
      itemSelectionDispatch?.({
        type: 'SELECT_ITEM',
        data: {
          orderItem: currentSelectedOrderItem,
        },
      });
    }
  };
  const handleRemoveItem = (itemId: string) => {
    getTrackerInstance().reportClickEvent({ eventName: EventName.removeSelectedItem });

    itemSelectionDispatch?.({
      type: 'REMOVE_ITEM',
      data: {
        itemId,
      },
    });
  };
  const isMultipleReturn =
    orderIneligibility &&
    orderIneligibility.some((ineligibility) => ineligibility.reason === 'multiple_returns');
  const showBannerContainer = isMerchantMode || isMultipleReturn || isReturnCare;

  const renderOrderWarningBanner = () => {
    if (isMerchantMode) {
      return <OrderWarningTips tips={t('page.banner.overrideRule')} />;
    } else if (isMultipleReturn) {
      return <OrderWarningTips tips={t('page.error.orderCannotReturn')} />;
    }
  };

  const renderReturnCareBanner = () => {
    if (isReturnCare) {
      return <ReturnCareTips />;
    }
  };

  const onClickRefund = () => {
    dispatch?.({ type: 'SELECT_REFUND_ME' });
    getTrackerInstance().reportClickEvent({
      eventName: EventName.clickEFAOrRefund,
      payload: { choice: 'refund' },
    });
  };
  const onClickShopNow = () => {
    dispatch?.({ type: 'SELECT_SHOP_NOW' });
    getTrackerInstance().reportClickEvent({
      eventName: EventName.clickEFAOrRefund,
      payload: { choice: 'shop_now', efaMethod },
    });
  };

  const onClickProduct = (productId: string, productUrl: string) => {
    getTrackerInstance().reportClickEvent({
      eventName: EventName.clickEFAOrRefund,
      payload: { choice: 'EFA_product_image', efaMethod },
    });

    xStateMetaData.set('exchangeInApp', { productId });
    dispatch?.({ type: 'SELECT_PRODUCT', data: { productId, productUrl } });
  };

  const onEfaModalClose = () => {
    itemSelectionDispatch?.({
      type: 'CANCEL_CHOOSE_EFA_OR_REFUND',
    });
  };

  const isButtonDisable = selectedItems.length === 0;
  const isButtonLoading =
    mainCurrentStep.name === 'afterItemSelection' || isEFAModalDecisionLoading;

  return (
    <>
      <Stack className={wrapperStyle} flex={1} direction={'column'}>
        {hasUnexpectedError ? (
          <SomethingWentWrong
            type='request-reutrn'
            style={{
              paddingBottom: isMobile
                ? tokenVars.Primitive.Size['2400']
                : tokenVars.Primitive.Size['2000'],
            }}
          />
        ) : (
          <>
            <ScrollFlex className={scrollClassName}>
              {isMobile && (
                <Box paddingBottom={Space.L}>
                  <RequestReturnsCardTitleText variant='headingXs' textAlign={'center'} as='p'>
                    {t('page.request.whatItems')}
                  </RequestReturnsCardTitleText>
                </Box>
              )}
              <Stack direction={'column'} gap='xl'>
                {showBannerContainer && (
                  <Stack direction={'column'} gap='xs'>
                    {renderOrderWarningBanner()}
                    {renderReturnCareBanner()}
                  </Stack>
                )}

                {
                  <ReturnableList
                    items={returnableItems}
                    selectedItems={selectedItems}
                    itemSelectionGrouping={itemSelectionGrouping}
                    isLimitSingleItemPerReturn={isLimitSingleItemPerReturn}
                    isLimitSelectionByRoutingRule={isLimitSelectionByRoutingRule}
                    onEditItem={handleEditItem}
                    onRemoveItem={setPendingDeleteItemId}
                  />
                }
              </Stack>
              {Boolean(nonReturnableItems.length) && (
                <NonReturnableList
                  policy={{
                    policyUrl: policyExternalLink,
                    policyEnable,
                  }}
                  nonReturnableItems={nonReturnableItems}
                />
              )}
            </ScrollFlex>
            <NextButton
              isLoading={isButtonLoading}
              isDisabled={isButtonDisable}
              onPress={() => {
                getTrackerInstance().reportClickEvent({
                  eventName: EventName.clickItemSelectionPageNext,
                });
                itemSelectionDispatch?.({
                  type: 'NEXT',
                });
              }}
            />
          </>
        )}
      </Stack>
      <FillReturnOverlay
        orderItem={activeOrderItem}
        isOpen={
          itemSelectionCurrentStep?.name === 'selectSingleItem' &&
          itemSelectionSubFlowStepName !== 'replaceTheSameItem'
        }
      />
      <RefundOrEfaOverlay
        isOpen={isChooseEFAOrRefund || isJumpingEFA}
        isLoading={isLoading}
        isJumpingEFA={isJumpingEFA}
        extraCreditAmount={extraCreditAmount}
        selectedItemsWithCreditAmountString={selectedItemsWithCreditAmountString}
        preDiscountCreditAmount={efaPreDiscountCreditAmount}
        selectedItemsAmount={selectedItemsAmount}
        products={products}
        brandingImage={exchangeForAnythingHeroImage?.src}
        onClickRefund={onClickRefund}
        onClickShopNow={onClickShopNow}
        onClickProduct={onClickProduct}
        onClose={onEfaModalClose}
      />

      <ReplaceSameItemResolution
        isOpen={itemSelectionSubFlowStepName === 'replaceTheSameItem'}
        onBack={onBack}
        onClose={onClose}
      />

      <ConfirmComp
        isOpen={!!pendingDeleteItemId}
        onClose={() => {
          setPendingDeleteItemId('');
        }}
        onConfirm={() => {
          setPendingDeleteItemId((pendingDeleteItemId) => {
            handleRemoveItem(pendingDeleteItemId);
            return '';
          });
        }}
      />
    </>
  );
};

export default RequestReturns;
