import React from 'react';
import { useTranslation } from 'react-i18next';

import { Stack } from '@aftership/astra';
import { useMainFlowContext, useSelector } from 'returns-logics/react';

import { StepCard } from '@/components/StepCard';
import { RequestReturnsCardTitleText } from '@/features/preview/components/WithPreviewSection';
import RequestReturns from '@/features/request-returns';
import { useStepCardMinHeight } from '@/hooks/useStepCardMinHeight';
import { PageId } from '@/utils/tracker/consts';
import { useReportPageViewEvent } from '@/utils/tracker/useReportPageViewEvent';

export function RequestReturnsPage() {
  const mainFlow = useMainFlowContext();
  const dispatch = mainFlow.send;
  const itemSelectionSubFlow = useSelector(
    mainFlow,
    (state) => state.children.itemSelectionSubFlow,
  );

  const returnsLength = useSelector(
    itemSelectionSubFlow,
    (state) => state?.context?.returns?.length,
  );

  const minHeight = useStepCardMinHeight();
  const { t } = useTranslation();

  useReportPageViewEvent(PageId.itemSelection);

  return (
    <StepCard
      width={800}
      title={
        <Stack direction='column' flex={1} justify='center' align='center'>
          <RequestReturnsCardTitleText variant='headingXs' textAlign='center' color='primary'>
            {t('page.request.whatItems')}
          </RequestReturnsCardTitleText>
        </Stack>
      }
      onBack={() => {
        if (returnsLength) {
          dispatch({
            type: 'BACK_TO_RETURN_LIST',
          });
        } else {
          dispatch({
            type: 'BACK_TO_ORDER_LOOKUP',
          });
        }
      }}
      height={minHeight}
    >
      <RequestReturns />
    </StepCard>
  );
}
