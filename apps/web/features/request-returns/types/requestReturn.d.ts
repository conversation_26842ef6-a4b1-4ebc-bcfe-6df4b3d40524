import { BundleReturnType, OrderItemType, PresentmentMoney } from '@aftership/returns-logics-core';

declare global {
  namespace RequestReturn {
    interface ReturnableItemProps {
      /**
       * 商品id
       */
      itemId: string;
      /**
       * returnable 为 false 也可能出现在 returnable list 中
       * 比如 partial return 类型的 bundle, 尽管 parent item 不可退,但是存在可退的 line item, 此时不展示 whole return 按钮
       */
      returnable: boolean;
      /**
       * 该 item 是 bundle 类型 还是普通类型
       * 如果是 bundle 类型,肯定会有 bundles 和 bundleReturnType 字段
       */
      type: OrderItemType;
      returnRule?: BundleReturnType;
      bundles?: ReturnableItemProps[];
      overridingReason?: string;
      /**
       * 如果是Line item,这个字段是parent id
       */
      parentId: Maybe<string>;
      /**
       * 商品标题
       */
      productTitle: string;
      /**
       * 商品封面链接
       */
      productCoverUrl?: string;
      /**
       * 商品variant标题
       */
      variantTitle?: string;

      /**
       * 商品价格信息
       */
      price?: PresentmentMoney;
      /**
       * returnable Quantity
       */
      returnableQuantity: number;
      /**
       * 商品的原始价格
       */
      originPrice?: PresentmentMoney;
      /**
       * 商品标签列表
       */
      productTags?: Array<string>;
    }
  }
}
