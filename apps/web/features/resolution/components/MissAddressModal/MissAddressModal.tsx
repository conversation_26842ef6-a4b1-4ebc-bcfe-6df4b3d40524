import Image from 'next/image';
import React from 'react';
import { useTranslation } from 'react-i18next';

import { Stack, Typography } from '@aftership/astra';

import { Modal } from '@/components/Modal';

const warningImage = require('@/assets/warning_outlined.svg').default;

export interface MissAddressModalProps {
  isOpen: boolean;
  onClose: () => void;
}
const MissAddressModal = ({ isOpen, onClose }: MissAddressModalProps) => {
  const { t } = useTranslation();
  return (
    <Modal isOpen={isOpen} title={t('page.error.missingShippingAddress')} onClose={onClose}>
      <Stack direction={'column'} align={'center'} gap='xl'>
        <Image src={warningImage.src} width={100} height={100} alt={'warning'} />
        <Typography textAlign={'center'}>{t('page.details.missingShippingAddress')}</Typography>
      </Stack>
    </Modal>
  );
};

export default MissAddressModal;
