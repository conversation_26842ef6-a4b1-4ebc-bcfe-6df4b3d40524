import { style } from '@vanilla-extract/css';

import { semanticTokenVar } from '@aftership/astra-tokens/semantic-token/Contract.css';

const { Space } = semanticTokenVar;

export const bodyContainersMobileClassName = style({
  display: 'grid',
  gridTemplateColumns: 'repeat(2, 1fr)',
  gap: `${Space.M} ${Space.Xl}`,
});
export const bodyContainersDesktopClassName = style({
  display: 'flex',
  flexDirection: 'row',
  gap: Space.Xl,
  justifyContent: 'center',
  width: '100%',
  paddingBlockStart: '40px',
});
