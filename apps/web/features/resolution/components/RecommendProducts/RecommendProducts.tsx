import { t } from 'i18next';
import React from 'react';

import {
  Box,
  Pressable,
  Stack,
  StackProps,
  Typography,
  useBreakpointValue,
} from '@aftership/astra';
import { semanticTokenVar } from '@aftership/astra-tokens/semantic-token/Contract.css';
import { PresentmentMoney } from '@aftership/returns-logics-core';

import { EllipsisText } from '@/components/EllipsisText';
import { ImageWithFallback } from '@/components/ImageWithFallback';
import {
  bodyContainersDesktopClassName,
  bodyContainersMobileClassName,
} from '@/features/resolution/components/RecommendProducts/RecommendProducts.css.ts';
import { toCurrency } from '@/utils/price';

export interface RecommendProductItem {
  productId: string;
  productUrl: string;
  productCoverUrl?: string;
  productTitle: string;
  price: PresentmentMoney;
}
export interface RecommendProductsProps {
  items: RecommendProductItem[];
  isDisabled?: boolean;
  onItemClick: (productId: string, productUrl: string) => void;
}

const { Radius } = semanticTokenVar;

const RecommendProducts = ({ items, onItemClick, isDisabled }: RecommendProductsProps) => {
  const size = useBreakpointValue({
    base: 100,
    m: 172,
  });

  const imageStyle = useBreakpointValue({
    base: { autoFitWidth: true },
    m: { width: 140, height: 140 },
  });

  const containerCls = useBreakpointValue({
    base: bodyContainersMobileClassName,
    m: bodyContainersDesktopClassName,
  });

  const stackStyle = useBreakpointValue<
    'base' | 'm',
    {
      gap: StackProps['gap'];
      direction: StackProps['direction'];
    }
  >({
    base: { gap: 'none', direction: 'column' },
    m: { gap: '2xs', direction: 'row' },
  });

  if (!items.length) {
    return <Box width={size} height={size} />;
  }

  return (
    <Box className={containerCls}>
      {items.map((item) => {
        return (
          <Pressable
            key={item.productId}
            isDisabled={isDisabled}
            onPress={() => onItemClick(item.productId, item.productUrl)}
          >
            <Stack direction={'column'} gap='xs' align={'center'}>
              <Box borderRadius={Radius.Xs} overflow='hidden' width={'100%'}>
                <ImageWithFallback
                  {...imageStyle}
                  usingShopifyPreview
                  src={item.productCoverUrl || ''}
                  alt={item.productTitle}
                />
              </Box>
              <Stack
                gap={stackStyle?.gap}
                align='center'
                justify='center'
                direction={stackStyle?.direction}
                style={{ width: '140px' }}
              >
                <Typography variant='bodyLgMediumBold' style={{ maxWidth: '96px' }}>
                  {toCurrency(item.price.amount, item.price.currency)}
                </Typography>
                <EllipsisText
                  variant='bodySm'
                  text={t('page.description.afterCredit')}
                  color='secondary'
                />
              </Stack>
            </Stack>
          </Pressable>
        );
      })}
    </Box>
  );
};

export default RecommendProducts;
