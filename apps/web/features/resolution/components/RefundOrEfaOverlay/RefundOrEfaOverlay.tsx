import Image from 'next/image';
import { FC, PropsWithChildren, ReactNode, useEffect, useState } from 'react';
import { Trans, useTranslation } from 'react-i18next';

import {
  Box,
  IconButton,
  Overlay,
  Spinner,
  Stack,
  Typography,
  useBreakpointValue,
} from '@aftership/astra';
import { CloseOutlined } from '@aftership/astra-icons';
import { semanticTokenVar } from '@aftership/astra-tokens/semantic-token/Contract.css';
import { usePreviewContext } from '@aftership/preview-kit/client';
import { PresentmentMoney } from '@aftership/returns-logics-core';

import { Button } from '@/components/Button';
import Sheet, { SheetProps } from '@/components/Sheet';
import { RecommendProducts } from '@/features/resolution/components/RecommendProducts';
import { useGeneratePlatformEfaUrl } from '@/features/resolution/utils/url';
import { useCachedAppProxy } from '@/features/returns/hooks/useCachedAppProxy';
import useDevice from '@/hooks/useDevice';
import { useMainFlow } from '@/hooks/useMainFlow';
import { toCurrency } from '@/utils/price';

import { closeButtonClassName, overlayModalClassName } from './styles.css';

const { Space, Color } = semanticTokenVar;

interface ButtonsProps {
  selectedItemsWithCreditAmountString: string;
  selectedItemsAmount: PresentmentMoney;
  onClickRefund: VoidFunction;
  onClickShopNow: VoidFunction;
  isJumpingEFA?: boolean;
  isJumpingRefund?: boolean;
}

interface EfaTitleProps {
  selectedItemsWithCreditAmountString: string;
  extraCreditAmount?: PresentmentMoney | null;
  preDiscountCreditAmount: PresentmentMoney;
  showEmptySpacing?: boolean;
}

interface ContainerProps extends PropsWithChildren {
  isOpen: boolean;
  isPreview: boolean;
  isLoading?: boolean;
  title?: ReactNode;
  footer?: ReactNode;
  footerExtra?: ReactNode;
  onBack?: () => void;
  onClose?: SheetProps['onClose'];
  brandingImage?: string;
}

export interface ExchangeShopNowOverlayProps {
  isOpen: boolean;
  isLoading: boolean;
  onClose: VoidFunction;
  onClickRefund: VoidFunction;
  onClickShopNow: VoidFunction;
  onClickProduct: (productId: string, productUrl: string) => void;
  isJumpingEFA?: boolean;
  isJumpingRefund?: boolean;
  selectedItemsAmount: PresentmentMoney;
  selectedItemsWithCreditAmountString: string;
  extraCreditAmount?: PresentmentMoney | null;
  preDiscountCreditAmount: PresentmentMoney;
  products: {
    productUrl: string;
    productId: string;
    productTitle: string;
    productCoverUrl: string;
    price: PresentmentMoney;
  }[];
  brandingImage?: string;
}

const Buttons = ({
  selectedItemsWithCreditAmountString,
  selectedItemsAmount,
  onClickRefund,
  onClickShopNow,
  isJumpingEFA = false,
  isJumpingRefund = false,
}: ButtonsProps) => {
  const { t } = useTranslation();
  const isMobile = useBreakpointValue({ base: true, m: false });
  const width = isMobile ? '100%' : 404;
  const padding = isMobile ? Space.M : '56px 0 0 0';

  return (
    <Stack direction='column' gap='m' style={{ width, padding }}>
      <Button
        isFullWidth
        size={'large'}
        onPress={onClickShopNow}
        isLoading={isJumpingEFA}
        isDisabled={isJumpingRefund}
      >
        {t('page.request.shopNow', {
          creditTotal: selectedItemsWithCreditAmountString,
        })}
      </Button>
      <Button
        size={'large'}
        variant='basic'
        onPress={onClickRefund}
        isLoading={isJumpingRefund}
        isDisabled={isJumpingEFA}
        isFullWidth
      >
        {t('page.request.refundMeDirectlyWithAmount', {
          refund: `${toCurrency(selectedItemsAmount.amount, selectedItemsAmount.currency, true)}`,
        })}
      </Button>
    </Stack>
  );
};

export const EfaTitle = ({
  selectedItemsWithCreditAmountString,
  extraCreditAmount,
  preDiscountCreditAmount,
  showEmptySpacing,
}: EfaTitleProps) => {
  const { t } = useTranslation();
  const isMobile = useDevice().mobile;
  const extraCreditTotalAmount: PresentmentMoney = {
    amount: `${
      Number(extraCreditAmount?.amount ?? 0) + Number(preDiscountCreditAmount?.amount ?? 0)
    }`,
    currency: extraCreditAmount?.currency || preDiscountCreditAmount.currency,
  };

  return (
    <Stack direction={'column'} gap={isMobile ? 'm' : 'xs'}>
      <Typography variant={'headingXs'} textAlign={'center'}>
        {t('resolution.description.exchangeCredit', {
          creditTotal: selectedItemsWithCreditAmountString,
        })}
      </Typography>
      {extraCreditTotalAmount && !!Number(extraCreditTotalAmount.amount) ? (
        <Typography
          variant='heading2Xs'
          textAlign={'center'}
          color='secondary'
          as={'i'}
          style={{ width: '100%' }}
        >
          <Trans
            i18nKey='v2.resolution.description.exchangeCredit'
            components={[
              <Typography key={'amount'} variant='heading2Xs' color='primary'>
                {toCurrency(extraCreditTotalAmount.amount, extraCreditTotalAmount.currency, true)}
              </Typography>,
            ]}
          >
            {'Extra <0>${extraCredit}</0> included'}
          </Trans>
        </Typography>
      ) : null}
      {showEmptySpacing && <Box height={100} />}
    </Stack>
  );
};

export const ContainerComp: FC<ContainerProps> = ({
  children,
  isOpen,
  isLoading = false,
  footer,
  brandingImage,
  footerExtra,
  isPreview,
  onClose = () => {},
}) => {
  const isMobile = useBreakpointValue({ base: true, m: false });

  return isMobile ? (
    <Sheet
      isOpen={isOpen}
      leadingAction={void 0}
      onClose={onClose}
      disableFocusManagement={isPreview}
      footer={footer}
      minHeight='90%'
      bodyStyle={{ paddingBlockStart: Space.Xs, paddingInline: Space.M, paddingBlockEnd: 0 }}
      footerExtra={footerExtra}
    >
      <Stack direction='column' gap='xl'>
        {children}
      </Stack>
    </Sheet>
  ) : (
    <Overlay isOpen={isOpen} disableFocusManagement={isPreview} className={overlayModalClassName}>
      <Stack direction='row' style={{ minHeight: 600 }}>
        <IconButton
          size='large'
          icon={CloseOutlined}
          isDisabled={isLoading}
          className={closeButtonClassName}
          onPress={onClose}
        />
        {brandingImage && (
          <Box
            position='relative'
            width={352}
            flexBasis={352}
            flexGrow={0}
            flexShrink={0}
            backgroundColor={Color.Bg.Body_Secondary}
          >
            <Image fill src={brandingImage} style={{ objectFit: 'cover' }} alt='brandingImage' />
          </Box>
        )}
        <Stack
          flex={1}
          align='center'
          justify='center'
          direction='column'
          style={{ width: 0, paddingBlock: '84px 48px' }}
        >
          {children}
          {footer}
        </Stack>
      </Stack>
    </Overlay>
  );
};

const ExchangeShopNowOverlay = ({
  isOpen,
  isLoading,
  isJumpingEFA = false,
  isJumpingRefund = false,
  extraCreditAmount,
  selectedItemsWithCreditAmountString,
  preDiscountCreditAmount,
  selectedItemsAmount,
  products,
  onClickRefund,
  onClickShopNow,
  onClickProduct,
  onClose,
  brandingImage,
}: ExchangeShopNowOverlayProps) => {
  const [productUrl, setProductUrl] = useState<string | undefined>(void 0);
  const isMobile = useBreakpointValue({ base: true, m: false });
  const showProducts = Boolean(products?.length);
  const usingMinHeight = isMobile && !products?.length;
  const mainFlow = useMainFlow();
  const mainContext = mainFlow.context;
  const { generatePlatformEfaUrl } = useGeneratePlatformEfaUrl(mainContext);

  const isPreview = usePreviewContext()?.isPreview ?? false;
  const { pathPrefix } = useCachedAppProxy();

  useEffect(() => {
    if (mainFlow.currentStep.name === 'exchangeOnStore') {
      const url = generatePlatformEfaUrl({
        productUrl: productUrl,
        continuePath: mainContext?.skipReturnMethodAfterEFA ? '/review' : '/return-method',
        redirectUrl: `${location.origin}${
          mainContext?.skipReturnMethodAfterEFA ? '/review' : '/return-method'
        }`,
      });
      location.href = url;
    }
  }, [
    productUrl,
    mainFlow.currentStep.name,
    mainContext?.skipReturnMethodAfterEFA,
    mainContext?.request?.orders?.store?.url,
    generatePlatformEfaUrl,
    pathPrefix,
  ]);

  return (
    <ContainerComp
      isOpen={isOpen}
      isPreview={isPreview}
      brandingImage={brandingImage}
      isLoading={isJumpingEFA || isJumpingRefund}
      onClose={onClose}
      footer={
        !isLoading && (
          <Buttons
            selectedItemsWithCreditAmountString={selectedItemsWithCreditAmountString}
            selectedItemsAmount={selectedItemsAmount}
            onClickRefund={onClickRefund}
            onClickShopNow={onClickShopNow}
            isJumpingEFA={isJumpingEFA}
            isJumpingRefund={isJumpingRefund}
          />
        )
      }
    >
      {isLoading ? (
        <Stack align='center' justify='center' style={{ height: 360 }}>
          <Spinner size='large' />
        </Stack>
      ) : (
        <>
          <EfaTitle
            extraCreditAmount={extraCreditAmount}
            selectedItemsWithCreditAmountString={selectedItemsWithCreditAmountString}
            preDiscountCreditAmount={preDiscountCreditAmount}
            showEmptySpacing={usingMinHeight}
          />
          {showProducts && (
            <RecommendProducts
              isDisabled={isJumpingEFA || isJumpingRefund}
              items={[...products]?.slice(0, brandingImage ? 4 : 6)}
              onItemClick={(productId, productUrl) => {
                onClickProduct(productId, productUrl);
                setProductUrl(productUrl);
              }}
            />
          )}
        </>
      )}
    </ContainerComp>
  );
};

export default ExchangeShopNowOverlay;
