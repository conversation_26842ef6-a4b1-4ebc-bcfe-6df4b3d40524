import { style } from '@vanilla-extract/css';

import { tokenVars } from '@aftership/astra-tokens/Contract.css';
import { semanticTokenVar } from '@aftership/astra-tokens/semantic-token/Contract.css';

const { Space, Color } = semanticTokenVar;

export const overlayModalClassName = style({
  width: 1040,
  margin: 'auto',
  position: 'relative',
  top: '50%',
  transform: 'translateY(-50%)',
  borderRadius: tokenVars.Semantic.Radius.M,
  overflow: 'hidden',
  backgroundColor: Color.Bg.Body,
});

export const closeButtonClassName = style({
  position: 'absolute',
  top: Space.M,
  right: Space.M,
});
