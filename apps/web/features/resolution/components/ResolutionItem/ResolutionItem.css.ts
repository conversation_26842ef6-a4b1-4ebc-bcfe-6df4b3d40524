import { style } from '@vanilla-extract/css';

import { tokenVars } from '@aftership/astra-tokens/Contract.css';

const { Space, Radius } = tokenVars.Semantic;

export const resolutionItemWrap = style({
  whiteSpace: 'pre-wrap',
});

export const refundCard = style({
  width: 196,
  height: 112,
  paddingTop: Space.S,
  paddingInline: Space.M,
  paddingBottom: Space.M,
  borderRadius: Radius.M,
  position: 'relative',
  boxShadow: `0px 4px 12px 0px rgba(0, 0, 0, 0.06), 0px 2px 4px 0px rgba(0, 0, 0, 0.03), 0px 0px 4px 0px rgba(0, 0, 0, 0.03)`,
});
