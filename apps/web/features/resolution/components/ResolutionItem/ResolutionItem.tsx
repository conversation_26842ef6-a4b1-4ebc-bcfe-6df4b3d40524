import Image from 'next/image';
import React from 'react';
import { useTranslation } from 'react-i18next';

import { Box, Icon, Stack, Typography } from '@aftership/astra';
import { CheckFilled } from '@aftership/astra-icons';
import { tokenVars } from '@aftership/astra-tokens/Contract.css';
import { PresentmentMoney, Resolution } from '@aftership/returns-logics-core';

import { EllipsisText } from '@/components/EllipsisText';
import useDevice from '@/hooks/useDevice';
import { useResolutionFlow } from '@/hooks/useResolutionFlow';
import { useShopInfo } from '@/hooks/useShopInfo';
import { toCurrency } from '@/utils/price';

import * as styles from './ResolutionItem.css';

const { Space, Color } = tokenVars.Semantic;
const { Color: PrimitiveColor } = tokenVars.Primitive;

export interface ResolutionItemProps {
  type: Resolution;
  title: string;
  description?: string;
  isSelected?: boolean;
  renderChildren?: () => React.ReactNode;
}

export interface IRefundCardProps {
  price?: PresentmentMoney | null;
}

const ReturnsPageHeaderMobile = ({
  title,
  description,
  isSelected,
  renderChildren,
}: ResolutionItemProps) => {
  return (
    <Stack gap='2xs' direction={'column'} style={{ position: 'relative', padding: Space.M }}>
      {isSelected && (
        <Box position='absolute' top={Space.M} right={Space.M}>
          <Icon source={CheckFilled} size={Space.Xl} color='brand' />
        </Box>
      )}

      <Box style={{ paddingInlineEnd: Space['2Xl'] }}>
        <Typography
          variant={'bodyLgSemibold'}
          color='primary'
          className={styles.resolutionItemWrap}
        >
          {title}
        </Typography>
      </Box>
      {renderChildren?.()}
      {description && (
        <Typography variant={'bodyMd'} color='secondary' className={styles.resolutionItemWrap}>
          {description}
        </Typography>
      )}
    </Stack>
  );
};

const ResolutionItemDesktop = ({
  title,
  description,
  isSelected,
  renderChildren,
}: ResolutionItemProps) => {
  return (
    <Stack gap='2xs' direction={'column'} style={{ position: 'relative', padding: Space.M }}>
      {isSelected && (
        <Box position='absolute' top={Space.M} right={Space.M}>
          <Icon source={CheckFilled} size={Space.Xl} color='brand' />
        </Box>
      )}

      <Box style={{ paddingInlineEnd: Space['2Xl'] }}>
        <Typography
          variant={'bodyLgSemibold'}
          color='primary'
          className={styles.resolutionItemWrap}
        >
          {title}
        </Typography>
      </Box>
      {renderChildren?.()}
      {description && (
        <Typography variant={'bodyMd'} color='secondary' className={styles.resolutionItemWrap}>
          {description}
        </Typography>
      )}
    </Stack>
  );
};

const StoreCreditCard = ({ price }: IRefundCardProps) => {
  const { t } = useTranslation();

  return (
    <Box className={styles.refundCard}>
      <Image
        width={196}
        height={112}
        src={require('@/assets/gift-card-black.png').default?.src}
        style={{ position: 'absolute', top: 0, left: 0, right: 0, bottom: 0 }}
        alt='gift'
      />
      <Stack
        direction='column'
        justify='space-between'
        style={{ height: '100%', position: 'relative' }}
      >
        <Stack align='center' gap='xs'>
          <Image
            width={20}
            height={20}
            alt='gift'
            src={require('@/assets/gift.png').default?.src}
          />
          <EllipsisText
            variant='bodyLgSemibold'
            text={t('resolution.refund_card.store_credit.title')}
            style={{ color: PrimitiveColor.Gray[400] }}
          />
        </Stack>
        <Typography variant='headingSm' style={{ color: Color.Text.White }}>
          {toCurrency(price)}
        </Typography>
      </Stack>
    </Box>
  );
};

const OriginalPaymentCard = ({ price }: IRefundCardProps) => {
  const { t } = useTranslation();

  return (
    <Box className={styles.refundCard} style={{ backgroundColor: 'white' }}>
      <Stack direction='column' justify='space-between' style={{ height: '100%' }}>
        <EllipsisText
          variant='bodyLgSemibold'
          text={t('resolution.refund_card.original_payment.title')}
          style={{ color: PrimitiveColor.Gray[1000] }}
        />
        <Typography variant='headingSm' color='primary'>
          {toCurrency(price)}
        </Typography>
      </Stack>
    </Box>
  );
};

const MultipleResolutionItemDesktop = ({
  type,
  title,
  description,
  isSelected,
  renderChildren,
}: ResolutionItemProps) => {
  const { context } = useResolutionFlow();
  const isRefund = type === Resolution.OriginalPayment || type === Resolution.StoreCredit;

  return (
    <Stack style={{ position: 'relative' }}>
      {isSelected && (
        <Box position='absolute' top={Space.M} right={Space.M}>
          <Icon source={CheckFilled} size={Space.Xl} color='brand' />
        </Box>
      )}

      {isRefund && (
        <Stack
          align='center'
          justify='center'
          style={{
            width: 236,
            height: 164,
            backgroundColor: PrimitiveColor.Gray[100],
          }}
        >
          {type === Resolution.StoreCredit && (
            <StoreCreditCard price={context?.refundCreditToStoreCredit} />
          )}
          {type === Resolution.OriginalPayment && (
            <OriginalPaymentCard price={context?.refundCreditToOriginalPayment} />
          )}
        </Stack>
      )}
      <Stack
        flex={1}
        gap='2xs'
        direction='column'
        align={'start'}
        style={{
          paddingBlock: Space.Xl,
          paddingInlineStart: Space.M,
        }}
      >
        <Box style={{ paddingInlineEnd: Space['3Xl'] }}>
          <Typography variant='bodyLgSemibold'>{title}</Typography>
        </Box>
        {renderChildren?.()}
        {description && (
          <Typography variant='bodyMd' color='secondary'>
            {description}
          </Typography>
        )}
      </Stack>
    </Stack>
  );
};

const ResolutionItem = (props: ResolutionItemProps) => {
  const isMobile = useDevice().mobile;
  const { multipleResolutionEnabled } = useShopInfo();

  if (isMobile) {
    return <ReturnsPageHeaderMobile {...props} />;
  }

  if (multipleResolutionEnabled) {
    return <MultipleResolutionItemDesktop {...props} />;
  }

  return <ResolutionItemDesktop {...props} />;
};

export default ResolutionItem;
