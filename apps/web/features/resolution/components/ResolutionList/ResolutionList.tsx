import clsx from 'clsx';
import { t } from 'i18next';
import React from 'react';

import { semanticTokenVar } from '@aftership/astra-tokens/semantic-token/Contract.css';
import { usePreviewContext } from '@aftership/preview-kit/client';
import {
  PresentmentMoney,
  Resolution,
  ResolutionDescription,
  isRefundDestination,
} from '@aftership/returns-logics-core';

import { ListBox, ListBoxItem } from '@/components/ListBox';
import ResolutionItem from '@/features/resolution/components/ResolutionItem/ResolutionItem';
import useDevice from '@/hooks/useDevice';
import { useShopInfo } from '@/hooks/useShopInfo';
import { ResolutionSuffix, genRefundExchangeCode } from '@/i18n/dynamic';
import { disablePointerEventsStyle } from '@/styles/common.css';
import { toCurrency } from '@/utils/price';

import { StoreCreditIncentiveTag } from './StoreCreditIncentiveTag';

const { Space } = semanticTokenVar;

/**
 * UI 要按此排序
 */
const ResolutionSortedType = [
  Resolution.ReplaceTheSameItem,
  Resolution.StoreCredit,
  Resolution.OriginalPayment,
  Resolution.ExchangeForAnything,
  Resolution.Refundid,
];

export enum ResolutionMode {
  onlyRefund,
  hideRefundWhenHaveEfa,
}

export interface ResolutionItemData {
  type: Resolution;
  name: string;
  description: string;
  extraDescription?: React.ReactNode;
  renderChildren?: () => React.ReactNode;
}

/**
 * 翻译 resolution 的 name 和 description
 * @param defaultValue
 * @param type
 * @param suffix
 */
export const translate = (defaultValue: string, type: Resolution, suffix: ResolutionSuffix) => {
  return t(
    genRefundExchangeCode({
      type,
      suffix: suffix,
    }),
    {
      rawValue: defaultValue,
      defaultValue: defaultValue,
    },
  );
};

/**
 * 排序并翻译 resolution
 * @param items
 */
const sortAndTranslate = (
  items: ResolutionDescription[],
  showRefundEstimatedTotal: boolean = true,
  storeCreditIncentive?: PresentmentMoney | null,
): ResolutionItemData[] => {
  return items
    .sort((a, b) => {
      return ResolutionSortedType.indexOf(a.type) - ResolutionSortedType.indexOf(b.type);
    })
    .map((item) => {
      // 只有在有 store credit bonus 时才显示价格
      const hasStoreCreditBonus =
        storeCreditIncentive &&
        storeCreditIncentive.amount &&
        Number(storeCreditIncentive.amount) > 0;

      // 只有当有 store credit bonus 时才显示价格
      const shouldShowPrice =
        hasStoreCreditBonus && showRefundEstimatedTotal && item.refund_estimated_total;

      const priceDisplay = shouldShowPrice ? ` (${toCurrency(item.refund_estimated_total)})` : '';

      return {
        type: item.type,
        name: `${translate(item.name, item.type, ResolutionSuffix.Name)}${priceDisplay}`,
        description: translate(item.description, item.type, ResolutionSuffix.Description),
        renderChildren:
          item.type === Resolution.StoreCredit
            ? () => <StoreCreditIncentiveTag storeCreditIncentive={storeCreditIncentive} />
            : undefined,
      };
    });
};

export interface ResolutionListProps {
  items: ResolutionDescription[];
  selectedResolution?: Resolution;
  mode: ResolutionMode;
  onSelectedResolution: (resolution: Resolution) => void;
  storeCreditIncentive?: PresentmentMoney | null;
}

const ResolutionList = ({
  items,
  selectedResolution,
  mode,
  onSelectedResolution,
  storeCreditIncentive,
}: ResolutionListProps) => {
  const isMobile = useDevice().mobile;
  const { isPreview } = usePreviewContext();
  const { multipleResolutionEnabled } = useShopInfo();

  let resolutionItems: ResolutionDescription[];
  const haveEfaResolution = items.some((item) => item.type === Resolution.ExchangeForAnything);
  switch (mode) {
    case ResolutionMode.onlyRefund:
      resolutionItems = items.filter((item) => isRefundDestination(item.type));
      break;
    case ResolutionMode.hideRefundWhenHaveEfa:
      resolutionItems = items.filter(
        (item) => !haveEfaResolution || !isRefundDestination(item.type),
      );
      break;
    default:
      throw new Error('Invalid mode');
  }
  // 多 resolution 在桌面端时，title 上不显示 refund_estimated_total
  const translateResolutionItems = sortAndTranslate(
    resolutionItems,
    !(multipleResolutionEnabled && !isMobile),
    storeCreditIncentive,
  );

  const selectedKeys = selectedResolution ? [selectedResolution] : [];

  return (
    <ListBox
      rowGap={Space.M}
      selectionMode='single'
      items={translateResolutionItems.map((item) => ({
        id: item.type,
        ...item,
      }))}
      selectedKeys={selectedKeys}
      onSelectionChange={(selectedKeys) => {
        if (typeof selectedKeys !== 'string' && selectedKeys.size > 0) {
          onSelectedResolution(selectedKeys.keys().next().value as Resolution);
        }
      }}
    >
      {(item) => {
        return (
          <ListBoxItem key={item.type} className={clsx({ [disablePointerEventsStyle]: isPreview })}>
            {({ isSelected }) => {
              return (
                <ResolutionItem
                  type={item.type}
                  title={item.name}
                  description={item.description}
                  isSelected={isSelected}
                  renderChildren={item.renderChildren}
                />
              );
            }}
          </ListBoxItem>
        );
      }}
    </ListBox>
  );
};

export default ResolutionList;
