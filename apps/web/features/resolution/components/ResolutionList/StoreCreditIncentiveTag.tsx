import { useTranslation } from 'react-i18next';

import { Badge } from '@aftership/astra';
import { AmountPrice } from '@aftership/returns-logics-core';

import { toCurrency } from '@/utils/price';

type StoreCreditIncentiveTagProps = {
  storeCreditIncentive?: AmountPrice | null;
};

export const StoreCreditIncentiveTag = ({ storeCreditIncentive }: StoreCreditIncentiveTagProps) => {
  const { t } = useTranslation();

  if (!storeCreditIncentive?.amount || !storeCreditIncentive?.currency) return null;

  return (
    <Badge
      color={'warning'}
      style={{
        width: 'fit-content',
      }}
    >
      {t('bonus.included', {
        price: toCurrency(storeCreditIncentive?.amount ?? '', storeCreditIncentive?.currency),
      })}
    </Badge>
  );
};
