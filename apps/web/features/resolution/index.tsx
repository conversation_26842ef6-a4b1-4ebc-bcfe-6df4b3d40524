import { t } from 'i18next';
import React, { useEffect, useState } from 'react';

import { Box, Spinner, Stack } from '@aftership/astra';
import { tokenVars } from '@aftership/astra-tokens/Contract.css';
import { EfaMethod, Resolution } from '@aftership/returns-logics-core';

import { NextButton } from '@/components/NextButton';
import { ScrollFlex } from '@/components/ScrollFlex';
import { SomethingWentWrong } from '@/components/SomethingWentWrong';
import {
  RefundMethodPageTitleText,
  ResolutionCardTitleText,
} from '@/features/preview/components/WithPreviewSection';
import { OrderWarningTips } from '@/features/request-returns/components/OrderWarningTips';
import ResolutionList, {
  ResolutionMode,
} from '@/features/resolution/components/ResolutionList/ResolutionList';
import { useGeneratePlatformEfaUrl } from '@/features/resolution/utils/url';
import useDevice from '@/hooks/useDevice';
import { useExchangeOrRefundFlow } from '@/hooks/useExchangeOrRefundFlow';
import { useMainFlow } from '@/hooks/useMainFlow';
import { useResolutionFlow } from '@/hooks/useResolutionFlow';
import { useShopInfo } from '@/hooks/useShopInfo';
import getTrackerInstance from '@/utils/tracker';
import { EventName, PageId } from '@/utils/tracker/consts';
import { useReportPageViewEvent } from '@/utils/tracker/useReportPageViewEvent';

import { RefundOrEfaOverlay } from './components/RefundOrEfaOverlay';
import { useMemoExchangeOrRefundContext } from './hooks/resolutions';

import { xStateMetaData } from '../returns/hooks/useSyncXStateAndRoute';

const { Space } = tokenVars.Semantic;

const ResolutionPage = () => {
  const isMobile = useDevice().mobile;
  const { context: mainContext, currentStep: mainCurrentStep } = useMainFlow();
  const { isLoading, isConfirmingResolution, dispatch, context } = useResolutionFlow();
  const { isLoading: isExchangeOrRefundLoading, dispatch: exchangeOrRefundDispatch } =
    useExchangeOrRefundFlow();
  const { generatePlatformEfaUrl } = useGeneratePlatformEfaUrl(mainContext);
  const { multipleResolutionEnabled, exchangeForAnythingHeroImage } = useShopInfo();

  const {
    isJumpingEFA,
    selectedItemsAmount,
    selectedItemsWithCreditAmountString,
    extraCreditAmount,
    efaPreDiscountCreditAmount,
    products,
  } = useMemoExchangeOrRefundContext();

  const [selectedResolution, setSelectedResolution] = useState<Resolution | undefined>(
    context?.selectedResolution,
  );
  const [mode, setMode] = useState(ResolutionMode.hideRefundWhenHaveEfa);

  const efaMethod =
    mainContext.storeConfig?.shopInfo?.exchange_for_anything_shopping_channel ?? EfaMethod.inApp;
  const isMerchantMode = !!mainContext.orderLookup?.isMerchantMode;
  // 匹配到的 resolutions
  const resolutions = context?.matchingResolutions ?? [];
  const isWrongInput = resolutions.length <= 0;

  // 如果开启多 resolution，默认只显示 refund
  useEffect(() => {
    if (multipleResolutionEnabled) {
      setMode(ResolutionMode.onlyRefund);
    } else {
      setMode(ResolutionMode.hideRefundWhenHaveEfa);
    }
  }, [multipleResolutionEnabled]);

  useReportPageViewEvent(
    mode === ResolutionMode.hideRefundWhenHaveEfa
      ? PageId.resolutionSelection
      : PageId.refundResolutionSelection,
  );

  useEffect(() => {
    if (mainCurrentStep.name === 'exchangeOnStore') {
      const continuePath = mainContext?.skipReturnMethodAfterEFA ? '/review' : '/return-method';

      const url = generatePlatformEfaUrl({
        productUrl: context?.productUrl,
        continuePath,
        redirectUrl: `${location.origin}${continuePath}`,
      });

      getTrackerInstance().reportPageEnterEvent({ pageId: PageId.EFAOnStore });

      location.href = url;
    }
  }, [
    context?.productUrl,
    generatePlatformEfaUrl,
    mainContext?.skipReturnMethodAfterEFA,
    mainContext?.shopHostName,
    mainCurrentStep.name,
  ]);

  useEffect(() => {
    if (context) {
      setSelectedResolution(context?.selectedResolution);
    }
  }, [context]);

  const onSelectedResolution = (resolution?: Resolution) => {
    setSelectedResolution(resolution);
    getTrackerInstance().reportClickEvent({
      eventName: EventName.selectResolution,
      payload: {
        resolution,
      },
    });
  };

  if (isLoading) {
    return (
      <Stack flex={1} align={'center'} justify={'center'}>
        <Spinner size='large' />
      </Stack>
    );
  }

  const isButtonDisable =
    !selectedResolution ||
    (mode === ResolutionMode.onlyRefund &&
      ![Resolution.Refundid, Resolution.StoreCredit, Resolution.OriginalPayment].includes(
        selectedResolution,
      ));

  return (
    <>
      <Stack flex={1} direction={'column'} style={{ height: 0 }}>
        <ScrollFlex>
          {isMobile && (
            <Box paddingBottom={Space.L}>
              {multipleResolutionEnabled ? (
                <RefundMethodPageTitleText
                  as='p'
                  variant='headingXs'
                  textAlign='center'
                  color='primary'
                >
                  {t('page.refund.method.title')}
                </RefundMethodPageTitleText>
              ) : (
                <ResolutionCardTitleText
                  as='p'
                  variant='headingXs'
                  textAlign='center'
                  color='primary'
                >
                  {t('page.request.howToResolve')}
                </ResolutionCardTitleText>
              )}
            </Box>
          )}
          <Stack direction={'column'} gap='xl'>
            {isMerchantMode && <OrderWarningTips tips={t('page.banner.overrideRule')} />}
            {isWrongInput ? (
              <SomethingWentWrong
                type='resolution'
                style={{
                  paddingBottom: isMobile
                    ? tokenVars.Primitive.Size['2400']
                    : tokenVars.Primitive.Size['2000'],
                }}
              />
            ) : (
              <ResolutionList
                items={resolutions}
                mode={mode}
                storeCreditIncentive={context?.storeCreditIncentive}
                onSelectedResolution={onSelectedResolution}
                selectedResolution={selectedResolution}
              />
            )}
          </Stack>
        </ScrollFlex>
        {!isWrongInput && (
          <NextButton
            isLoading={isConfirmingResolution}
            isDisabled={isButtonDisable}
            onPress={() => {
              getTrackerInstance().reportClickEvent({
                eventName:
                  mode === ResolutionMode.hideRefundWhenHaveEfa
                    ? EventName.clickResolutionPageNext
                    : EventName.clickRefundResolutionNext,
              });

              if (selectedResolution) {
                if (selectedResolution === Resolution.ExchangeForAnything) {
                  dispatch?.({
                    type: 'CHOOSE_EFA_OR_REFUND',
                  });
                } else {
                  dispatch?.({
                    type: 'CONFIRM_RESOLUTION',
                    data: { selectedResolution: selectedResolution },
                  });
                }
              }
            }}
          />
        )}
      </Stack>
      <RefundOrEfaOverlay
        isOpen={!!context?.isWaitingSelectEfaOrRefund}
        isLoading={isExchangeOrRefundLoading}
        isJumpingEFA={isJumpingEFA}
        selectedItemsAmount={selectedItemsAmount}
        selectedItemsWithCreditAmountString={selectedItemsWithCreditAmountString}
        extraCreditAmount={extraCreditAmount}
        preDiscountCreditAmount={efaPreDiscountCreditAmount}
        products={products}
        brandingImage={exchangeForAnythingHeroImage?.src}
        onClose={() => {
          exchangeOrRefundDispatch?.({
            type: 'SELECT_CANCEL',
          });
        }}
        onClickShopNow={() => {
          getTrackerInstance().reportClickEvent({
            eventName: EventName.clickEFAOrRefund,
            payload: {
              choice: 'shop_now',
              efaMethod,
            },
          });
          exchangeOrRefundDispatch?.({ type: 'SELECT_SHOP_NOW' });
        }}
        onClickRefund={() => {
          setMode(ResolutionMode.onlyRefund);
          getTrackerInstance().reportClickEvent({
            eventName: EventName.clickEFAOrRefund,
            payload: {
              choice: 'refund',
            },
          });
          exchangeOrRefundDispatch?.({ type: 'SELECT_REFUND_ME' });
        }}
        onClickProduct={(productId, productUrl) => {
          getTrackerInstance().reportClickEvent({
            eventName: EventName.clickEFAOrRefund,
            payload: {
              choice: 'EFA_product_image',
              efaMethod,
            },
          });

          xStateMetaData.set('exchangeInApp', { productId });
          exchangeOrRefundDispatch?.({
            type: 'SELECT_PRODUCT',
            data: {
              productId,
              productUrl,
            },
          });
        }}
      />
    </>
  );
};

export default ResolutionPage;
