import React from 'react';
import { useTranslation } from 'react-i18next';

import { Stack } from '@aftership/astra';
import { useFlow } from 'returns-logics/react';

import { StepCard } from '@/components/StepCard';
import {
  RefundMethodPageTitleText,
  ResolutionCardTitleText,
} from '@/features/preview/components/WithPreviewSection';
import ResolutionPage from '@/features/resolution';
import { useShopInfo } from '@/hooks/useShopInfo';
import { useStepCardMinHeight } from '@/hooks/useStepCardMinHeight';

export function ReturnResolutionPage() {
  const flow = useFlow();
  const { dispatch: mainDispatch } = flow;
  const minHeight = useStepCardMinHeight();
  const { t } = useTranslation();
  const { multipleResolutionEnabled } = useShopInfo();

  return (
    <StepCard
      title={
        <Stack direction={'column'} flex={1} justify='center' align='center'>
          {multipleResolutionEnabled ? (
            <RefundMethodPageTitleText
              as='p'
              variant='headingXs'
              color='primary'
              textAlign={'center'}
            >
              {t('page.refund.method.title')}
            </RefundMethodPageTitleText>
          ) : (
            <ResolutionCardTitleText
              as='p'
              variant='headingXs'
              color='primary'
              textAlign={'center'}
            >
              {t('page.request.howToResolve')}
            </ResolutionCardTitleText>
          )}
        </Stack>
      }
      onBack={() => {
        mainDispatch?.({
          type: 'BACK_TO_REQUEST_RETURN',
        });
      }}
      width={'800px'}
      height={minHeight}
    >
      <ResolutionPage />
    </StepCard>
  );
}
