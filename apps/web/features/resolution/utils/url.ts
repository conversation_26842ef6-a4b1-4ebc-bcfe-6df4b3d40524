import { useTranslation } from 'react-i18next';

import { IMainContext } from '@aftership/returns-logics-core';

import { useCachedAppProxy } from '@/features/returns/hooks/useCachedAppProxy';

export const RC_SHOP_NOW = 'rc_sn'; // rc_shopnow

interface GeneratePlatformEfaUrlParams {
  productUrl?: string;
  continuePath: string; // 继续流程路径 (如 '/review' 或 '/return-method')

  // @deprecated
  redirectUrl: string; // mejuri 用的 on store sdk 还在使用，仍然需要保留
}

export const queryParamsToPersist = ['mode', 'lang', 'returns_page_access'] as const;

export function attachPersistQueryParamsToUrl(
  url: string,
  options?: { includeHostname?: boolean; hostname?: string },
) {
  const urlObject = new URL(url, location.origin);

  queryParamsToPersist.forEach((key) => {
    const value = new URLSearchParams(window.location.search).get(key);
    if (value) {
      urlObject.searchParams.set(key, value);
    }
  });

  // 在非 app proxy 的情况下添加 hostname
  if (options?.includeHostname && options.hostname) {
    urlObject.searchParams.set('hostname', options.hostname);
  }

  return urlObject.toString();
}

export const useGeneratePlatformEfaUrl = (mainContext: IMainContext) => {
  const { order: orders } = mainContext?.request?.orders ?? {};
  const shopHostName = mainContext?.shopHostName;
  const shops = mainContext?.storeConfig?.shopInfo;
  const intentionId = mainContext?.intentionId ?? '';
  const sessionToken = (mainContext?.token ?? '').replace(/^Bearer\s/, '').trim();
  const { i18n } = useTranslation();
  const { pathPrefix, shopifyProxyMode } = useCachedAppProxy();

  const storeUrl = mainContext?.request?.orders?.store?.url ?? '';

  return {
    generatePlatformEfaUrl: ({
      productUrl,
      continuePath,
      redirectUrl,
    }: GeneratePlatformEfaUrlParams) => {
      const searchParams = new URLSearchParams();
      const currency = orders?.order_total_set.presentment_money.currency;
      currency && searchParams.set('currency', currency);
      const appKey = orders?.app.key ?? '';
      const orgId = shops?.organization?.id ?? '';
      const shopnowParams = new URLSearchParams();

      const currentUrl = `${location.origin}${location.pathname}`;
      const shopperHomeUrl = `${location.origin}${pathPrefix}`;
      const shopperContinueUrl = `${location.origin}${pathPrefix}${continuePath}`;

      // 在非 app proxy 的情况下，为 URL 添加 hostname 参数
      const shouldIncludeHostname = !shopifyProxyMode;

      const urlParams = [
        { key: 'appKey', value: appKey },
        { key: 'orgId', value: orgId },
        { key: 'intensionId', value: intentionId },
        { key: 'sessionToken', value: sessionToken },
        { key: 'language', value: i18n.language },

        {
          key: 'shopperHomeUrl',
          value: attachPersistQueryParamsToUrl(shopperHomeUrl, {
            includeHostname: shouldIncludeHostname,
            hostname: shopHostName,
          }),
        },
        {
          key: 'shopperContinueUrl',
          value: attachPersistQueryParamsToUrl(shopperContinueUrl, {
            includeHostname: shouldIncludeHostname,
            hostname: shopHostName,
          }),
        },
        {
          key: 'goBackUrl',
          value: attachPersistQueryParamsToUrl(currentUrl, {
            includeHostname: shouldIncludeHostname,
            hostname: shopHostName,
          }),
        },

        // @deprecated
        {
          key: 'referer',
          value: attachPersistQueryParamsToUrl(currentUrl),
        },
        {
          key: 'redirectUrl',
          value: attachPersistQueryParamsToUrl(redirectUrl || currentUrl),
        },
        {
          key: 'pathPrefix',
          value: pathPrefix,
        },
      ];

      urlParams.forEach(({ key, value }) => {
        shopnowParams.set(key, value);
      });

      const encodeParams = btoa(shopnowParams.toString());

      searchParams.set(RC_SHOP_NOW, encodeParams);
      const url = productUrl ?? storeUrl;
      return `${url.includes('http') ? url : `//${url}`}?${searchParams.toString()}${
        shopHostName ? `&hostname=${shopHostName}` : ''
      }`;
    },
  };
};
