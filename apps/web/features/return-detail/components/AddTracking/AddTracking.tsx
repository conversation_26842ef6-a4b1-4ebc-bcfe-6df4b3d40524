import { useCallback, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Select, Stack, TextField, useToast } from '@aftership/astra';
import { Courier, ErrorResponse, isJWTError } from '@aftership/returns-logics-core';
import { useFlow } from 'returns-logics/react';

import { Modal } from '@/components/Modal';

import { ErrorFieldEnum, useCheckField } from './hooks';
import { detectCouriers } from './services';

import { TrackModalMode, useTrackDetailContext } from '../../context/TrackDetailContext';
import { useReturnDetailFlow } from '../../hooks/useReturnDetailFlow';

const AddTracking = ({ couriers, isLoading }: { couriers?: Courier[]; isLoading: boolean }) => {
  const { returnDetail, returnDetailFlowContext, mainDispatch } = useReturnDetailFlow();
  const { t } = useTranslation();
  const { children } = useFlow();

  const dispatch = children?.returnDetailSubFlow?.dispatch;
  const { showToast } = useToast();
  const { value, updateContextValue } = useTrackDetailContext();
  const { trackModalMode } = value;
  const [trackingNumber, setTrackingNumber] = useState(value.trackingNumber);
  const [courierSlug, setCourierSlug] = useState<string>(value.courierSlug);
  const [errors, setErrors] = useState<Partial<Record<ErrorFieldEnum, string>>>({});

  const [isDetectLoading, setIsDetectLoading] = useState(false);

  const { handleCheckField } = useCheckField({
    errors,
    setErrors,
  });

  const defaultCourierSlug = value?.courierSlug;

  useEffect(() => {
    if (value?.courierSlug) {
      setCourierSlug(value.courierSlug);
    }
    if (value?.trackingNumber) {
      setTrackingNumber(value.trackingNumber);
    }
  }, [value]);

  const options = useMemo(
    () =>
      couriers?.map((item) => ({
        label: item.name,
        value: item.slug,
      })) ?? [],
    [couriers],
  );
  const searchFieldItems = useMemo(
    () =>
      !options?.find((i) => i.value === value.courierSlug)
        ? [
            ...options,
            {
              value: value.courierSlug,
              label: value.courierSlug,
            },
          ]
        : options,
    [options, value.courierSlug],
  );
  const modeTitle =
    trackModalMode === TrackModalMode.ADD
      ? t('page.action.addTrackingDetail')
      : t('page.action.editTrackingDetail');

  const trackModeText =
    trackModalMode === TrackModalMode.ADD ? t('page.action.add') : t('page.action.save');
  const isButtonDisabled = Boolean(
    errors?.[ErrorFieldEnum.TrackingNumber] || errors?.[ErrorFieldEnum.CourierSlug],
  );

  const closeModal = useCallback(() => {
    updateContextValue({
      isTrackModalOpen: false,
    });
  }, [updateContextValue]);

  const handleDetectCouriers = useCallback(
    async (trackingNumber: string) => {
      if (returnDetail?.id && trackingNumber && !courierSlug) {
        try {
          setIsDetectLoading(true);
          const data = await detectCouriers(
            { trackingNumber, returnId: returnDetail.id },
            returnDetailFlowContext?.token!,
          );

          const firstCourierSlug = data?.couriers?.[0]?.slug;
          const courierSlugValid = searchFieldItems.find((i) => i.value === firstCourierSlug);
          if (firstCourierSlug && courierSlugValid) {
            setCourierSlug(firstCourierSlug);
            handleCheckField(ErrorFieldEnum.CourierSlug, firstCourierSlug);
          }
          setIsDetectLoading(false);
        } catch (error) {
          setIsDetectLoading(false);
          const { code, message = 'invalid error' } = (error as ErrorResponse) ?? {};
          if (isJWTError(code)) {
            mainDispatch({
              type: 'HANDLE_JWT_ERROR',
            });
          } else {
            showToast(message, {
              type: 'error',
            });
          }
        }
      }
    },
    [
      courierSlug,
      mainDispatch,
      returnDetail?.id,
      returnDetailFlowContext?.token,
      showToast,
      handleCheckField,
      searchFieldItems,
    ],
  );

  useEffect(() => {
    if (courierSlug) {
      handleCheckField(ErrorFieldEnum.CourierSlug, courierSlug);
    }
  }, [courierSlug, handleCheckField]);

  const onSubmit = useCallback(() => {
    dispatch?.({
      type: 'POST_SHIPMENTS',
      data: {
        trackingNumber,
        courierSlug,
      },
    });
    updateContextValue({
      courierSlug,
      trackingNumber,
    });
    closeModal();
  }, [closeModal, courierSlug, dispatch, trackingNumber, updateContextValue]);

  // todo: 原来在 footer 处做了移动端键盘遮挡问题，不太清楚原因，现在没办法自定义 footer 了，先记录
  return (
    <Modal
      isOpen={value.isTrackModalOpen}
      title={modeTitle}
      onClose={closeModal}
      primaryAction={{
        content: trackModeText,
        isDisabled: isButtonDisabled,
        isLoading: isLoading,
        onAction: async () => {
          const isTrackingNumberInvalid = await handleCheckField(
            ErrorFieldEnum.TrackingNumber,
            trackingNumber,
          );
          const isCourierSlugInvalid = await handleCheckField(
            ErrorFieldEnum.CourierSlug,
            courierSlug,
          );
          if (isTrackingNumberInvalid && isCourierSlugInvalid) onSubmit();
        },
      }}
      secondaryAction={{
        content: t('page.request.cancel'),
        onAction: () => {
          closeModal();
        },
      }}
    >
      <Stack direction='column' gap='xl'>
        <TextField
          name='trackingNumber'
          placeholder={t('page.action.trackingNumber')}
          isFullWidth
          value={trackingNumber}
          onChange={(value) => {
            setTrackingNumber(value);
            handleCheckField(ErrorFieldEnum.TrackingNumber, value);
          }}
          onBlur={async (e: any) => {
            const value = e.target.value as string;
            const isTrackingNumberInvalid = await handleCheckField(
              ErrorFieldEnum.TrackingNumber,
              value,
            );
            setTrackingNumber(value);
            if (isTrackingNumberInvalid) handleDetectCouriers(value);
          }}
          validationError={errors?.[ErrorFieldEnum.TrackingNumber]}
        />
        <Select
          name='courierSlug'
          placeholder={t('popup.request.search')}
          isDisabled={isDetectLoading}
          options={searchFieldItems}
          value={courierSlug ? [courierSlug] : []}
          defaultSelectedValues={defaultCourierSlug ? [defaultCourierSlug] : []}
          onChange={(value) => {
            if (typeof value[0] === 'string') {
              setCourierSlug(value[0]);
            }
          }}
          validationError={errors?.[ErrorFieldEnum.CourierSlug]}
          notFoundContent={t('return_detail.courier_slug.not_found')}
          enableVirtualList
          isSearchable
        />
      </Stack>
    </Modal>
  );
};

export default AddTracking;
