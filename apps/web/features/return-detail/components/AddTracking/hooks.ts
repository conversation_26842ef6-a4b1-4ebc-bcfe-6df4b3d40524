import { Dispatch, SetStateAction, useCallback } from 'react';
import { useTranslation } from 'react-i18next';

export enum ErrorFieldEnum {
  TrackingNumber = 'trackingNumber',
  CourierSlug = 'courierSlug',
}

export const TrackingNumberMinLength = 5;
export const CourierSlugMaxLength = 60;

export const useCheckField = ({
  errors,
  setErrors,
}: {
  errors: Partial<Record<ErrorFieldEnum, string>>;
  setErrors: Dispatch<SetStateAction<Partial<Record<ErrorFieldEnum, string>>>>;
}) => {
  const { t } = useTranslation();
  const handleCheckField = useCallback(
    async (name: ErrorFieldEnum, value: string) => {
      const isNormalVaild = typeof value === 'string' && value.trim() !== '';
      const isCurrentFieldValid =
        name === ErrorFieldEnum.TrackingNumber
          ? value.length >= TrackingNumberMinLength
          : value.length <= CourierSlugMaxLength;
      if (isNormalVaild && isCurrentFieldValid) {
        // 如果之前有错误，清除错误
        if (errors?.[name]) {
          const cloneErrors = { ...errors };
          delete cloneErrors[name];
          setErrors(cloneErrors);
        }
        return true;
      } else {
        if (!isNormalVaild) {
          setErrors((prev) => {
            return {
              ...(prev ?? {}),
              [name]:
                name === ErrorFieldEnum.TrackingNumber
                  ? t('v2.validation.tracking.number.enter')
                  : t('v2.validation.courier.enter'),
            };
          });
        } else if (name === ErrorFieldEnum.TrackingNumber) {
          setErrors((prev) => {
            return {
              ...(prev ?? {}),
              [name]: t('v2.page.description.lengthAtLeastCharacters', {
                length: TrackingNumberMinLength,
              }),
            };
          });
        } else if (name === ErrorFieldEnum.CourierSlug) {
          setErrors((prev) => {
            return {
              ...(prev ?? {}),
              [name]: t('page.description.lengthAtMostCharacters', {
                length: CourierSlugMaxLength,
              }),
            };
          });
        }
        return false;
      }
    },
    [errors, setErrors, t],
  );

  const clearErrors = useCallback(
    (field?: ErrorFieldEnum) => {
      if (field) {
        setErrors((prev) => {
          const cloneErrors = { ...prev };
          delete cloneErrors[field];
          return cloneErrors;
        });
      } else {
        setErrors({});
      }
    },
    [setErrors],
  );

  return { handleCheckField, clearErrors };
};
