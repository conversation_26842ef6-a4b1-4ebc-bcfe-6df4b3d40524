import {
  IDetectCarrierResponse,
  ResponseBody,
  request,
  v4hostName,
} from '@aftership/returns-logics-core';

// 目前只在纯 UI 层使用
export const detectCouriers = async (
  payload: { trackingNumber: string; returnId: string },
  token: string,
) => {
  const uri = `${v4hostName()}/detect-couriers`;
  return request<ResponseBody<IDetectCarrierResponse>>(uri, {
    method: 'POST',
    payload: {
      tracking_number: payload.trackingNumber,
      return_id: payload.returnId,
    },
    headers: { 'returns-authorization': token },
  }).then((response) => response.data || Promise.reject(response));
};
