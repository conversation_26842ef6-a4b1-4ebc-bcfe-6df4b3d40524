import { Skeleton, Stack } from '@aftership/astra';
import { tokenVars } from '@aftership/astra-tokens/Contract.css';

import { Divider } from '@/components/Divider';

import useCartStyle from '../../hooks/useCartStyle';

const { Space } = tokenVars.Semantic;

export const ReturnItemCardLoading = () => {
  return (
    <>
      <Stack direction='row' gap='m'>
        <Skeleton variant='rounded' width={80} height={80} />
        <Stack direction='column' gap='2xs'>
          <Skeleton variant='rounded' height={24} width={234} />
          <Skeleton variant='rounded' height={24} width={137} />
        </Stack>
      </Stack>
      <Divider spacing={Space.L} />
      <Stack direction='column' gap='2xs'>
        <Skeleton variant='rounded' height={24} width={137} />
        <Skeleton variant='rounded' height={24} width={326} />
      </Stack>
    </>
  );
};

export const ReturnStatusCardLoading = () => {
  const { isMobile } = useCartStyle();
  return (
    <Stack direction='column' gap='m'>
      <Skeleton variant='rounded' height={24} style={{ width: isMobile ? '70%' : 350 }} />
      <Skeleton variant='rounded' height={24} width={isMobile ? '100%' : 512} />
    </Stack>
  );
};

export default ReturnItemCardLoading;
