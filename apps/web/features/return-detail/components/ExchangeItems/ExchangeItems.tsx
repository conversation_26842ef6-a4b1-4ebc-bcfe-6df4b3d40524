import { useTranslation } from 'react-i18next';

import { Box, Link, Stack, Typography } from '@aftership/astra';
import { tokenVars } from '@aftership/astra-tokens/Contract.css';
import { ExchangeItemDetail } from '@aftership/returns-logics-core';

import { Card, CardHeader, CardSection } from '@/components/Card';

import ItemCard from '../../../../components/ItemCard';
import useCartStyle from '../../hooks/useCartStyle';
import useHideSummaryWidth from '../../hooks/useHideSummaryWidth';
import { productTitleContainerStyles } from '../../styles/product.css';

const { Space } = tokenVars.Semantic;

export interface ExchangeItemsProps {
  exchangeItems: ExchangeItemDetail[];
  exchangeOrder: {
    orderNumber?: string;
    checkoutUrl?: string;
    exchangeExternalId?: string;
  };
}

const ExchangeItems = ({ exchangeItems, exchangeOrder }: ExchangeItemsProps) => {
  const { t } = useTranslation();
  const { orderNumber, checkoutUrl, exchangeExternalId } = exchangeOrder ?? {};
  const { normalStyle, mobileNoOutlineClassname, isMobile } = useCartStyle();
  const hideSummaryWidth = useHideSummaryWidth();

  return (
    <Card
      {...normalStyle}
      className={mobileNoOutlineClassname}
      style={{ width: hideSummaryWidth || normalStyle.width }}
    >
      <CardHeader>
        <Typography variant='heading2Xs'>{t('dynamic.requestReview.exchangeItems')}</Typography>
      </CardHeader>

      <CardSection>
        <Stack direction='column'>
          {orderNumber && exchangeExternalId && (
            <Stack align='center' gap='2xs' style={{ marginBlockEnd: Space.M }}>
              <Typography variant='bodyMdSemibold'>
                {t('page.title.exchangeOrder')} {':'}
              </Typography>
              <Box>
                <Stack align='center' gap='2xs'>
                  {checkoutUrl ? (
                    <Link
                      onPress={() => {
                        if (checkoutUrl) window.open(checkoutUrl, '_blank');
                      }}
                    >
                      {orderNumber}
                    </Link>
                  ) : (
                    <Typography variant='bodyMdSemibold'>{orderNumber}</Typography>
                  )}
                </Stack>
              </Box>
            </Stack>
          )}

          <Stack direction='column' gap={isMobile ? '2xl' : 'none'}>
            {exchangeItems?.map((item) => (
              <ItemCard
                key={item.external_product_id + item.external_variant_id}
                hideDivider={isMobile}
                style={{ borderWidth: 0 }}
                productInfo={{
                  productTitle: item?.product_title || item?.title || '',
                  price: item.price_set?.presentment_money,
                  variantTitle: item?.variant_title ?? '',
                  productCoverUrl: item.image_url,
                  quantity: isMobile ? item.quantity : void 0,
                  productTags: item?.tags ?? [],
                }}
                rightContent={
                  !isMobile && <Typography variant='bodyMd'>x {item.quantity}</Typography>
                }
                productTitleContainerStyles={isMobile ? productTitleContainerStyles : undefined}
                exchangeNotes={item.exchange_request}
              />
            ))}
          </Stack>
        </Stack>
      </CardSection>
    </Card>
  );
};

export default ExchangeItems;
