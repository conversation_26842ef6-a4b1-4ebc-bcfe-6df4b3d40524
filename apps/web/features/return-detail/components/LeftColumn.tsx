import { Box, Stack } from '@aftership/astra';
import { tokenVars } from '@aftership/astra-tokens/Contract.css';
import {
  Courier,
  ReturnItemType,
  ReturnMethod,
  ShipmentsInfo,
} from '@aftership/returns-logics-core';

import useDevice from '@/hooks/useDevice';

import { AddTracking } from './AddTracking';
import { ExchangeItems } from './ExchangeItems';
import { ReturnItems } from './ReturnItems';
import { ReturnItem } from './ReturnItems/ReturnItems';
import { ShippingAddress } from './ShippingAddress';

import { useTrackDetailContext } from '../context/TrackDetailContext';
import { useReturnDetailFlow } from '../hooks/useReturnDetailFlow';

const { Color } = tokenVars.Semantic;

const LeftColumn = ({
  returnDetail,
  returnMethod,
  couriers,
}: {
  returnDetail?: ReturnItemType;
  returnMethod?: ReturnMethod;
  shipments?: ShipmentsInfo[];
  couriers?: Courier[];
}) => {
  const isMobile = useDevice().mobile;
  const { returnDetailFlowMatches, shippingAddress, contactRecipient, isFetchingDetail } =
    useReturnDetailFlow();
  const { value } = useTrackDetailContext();
  const orderNumber = returnDetail?.exchange_order?.number;
  const checkoutUrl = returnDetail?.exchange_order?.checkout_url;

  const returnItems = returnDetail?.items?.map((item) => ({
    itemId: item.item_id,
    productTitle: item?.product_title ?? '',
    price: item.discounted_base_price_set?.presentment_money,
    originPrice: item.base_price_set?.presentment_money,
    variantTitle: item?.variant_title ?? '',
    productCoverUrl: item.image?.src,
    returnReason: item.reason,
    returnSubReason: item.subreason,
    quantity: item.return_quantity,
    returnImages: item.return_images ?? [],
    notesToMerchant: item.notes_to_merchant,
    productTags: item.tags ?? [],
    // return item 存在 replacement 时，不要展示 exchange notes，因为这时候会在 exchange item 里展示
    exchangeNotes: item.replacement ? undefined : item.exchange_request,
  })) as ReturnItem[];

  const isAddTrackingLoading = !!returnDetailFlowMatches?.({
    postShipmentsById: 'loading',
  });

  return (
    <Box backgroundColor={Color.Bg.Body} width='100%'>
      <Stack gap={isMobile ? 'none' : 'xl'} direction='column'>
        {!!returnDetail?.exchange_items?.length && (
          <ExchangeItems
            exchangeItems={returnDetail.exchange_items}
            exchangeOrder={{
              orderNumber,
              checkoutUrl,
              exchangeExternalId: returnDetail.exchange_order?.external_id,
            }}
          />
        )}
        <ReturnItems
          isLoading={isFetchingDetail}
          exchangeItemsExist={!!returnDetail?.exchange_items?.length}
          returnItems={returnItems}
          returnMethod={{
            name: returnMethod?.name,
            id: returnMethod?.id,
            description: returnMethod?.description,
          }}
          costOfReturnOption={returnDetail?.return_cost_option}
          costOfReturAmountPrice={
            returnDetail?.is_return_care ? null : returnDetail?.cost_of_return
          }
          resolution={returnDetail?.resolution}
        />
        {shippingAddress && contactRecipient && (
          <ShippingAddress contact={contactRecipient} address={shippingAddress} />
        )}
        {value.isTrackModalOpen && (
          <AddTracking couriers={couriers} isLoading={isAddTrackingLoading} />
        )}
      </Stack>
    </Box>
  );
};

export default LeftColumn;
