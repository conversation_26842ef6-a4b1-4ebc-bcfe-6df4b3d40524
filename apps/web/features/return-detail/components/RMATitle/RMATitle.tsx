import dayjs from 'dayjs';

import { Box, Icon, Link, Stack, Typography, useBreakpointValue } from '@aftership/astra';
import { CheckCircleOutlined } from '@aftership/astra-icons';
import { tokenVars } from '@aftership/astra-tokens/Contract.css';

import useWarrantyTranslation from '@/hooks/useTranslation.ts';

const { Space } = tokenVars.Semantic;

export interface RMATitleProps {
  isFromReview?: boolean;
  rmaId?: string;
  rmaCreateAt?: string;
  isMobile?: boolean;
  requestAnotherReturn: VoidFunction;
}

export const formatDate = (date?: string, format?: string) => {
  return dayjs(date).format(format || 'MMM D, YYYY [at] h:mm a');
};

// 从 review 进入的 RMA 详情页面展示的组件
export const RMATitleFromReview = ({
  rmaTitle,
  requestAnotherReturn,
}: {
  rmaTitle: string;
  requestAnotherReturn: VoidFunction;
}) => {
  const isMobile = useBreakpointValue({ base: true, m: false });
  const { t } = useWarrantyTranslation();

  if (isMobile) {
    return (
      <Stack direction='column' align='center' gap='s' style={{ paddingBlockStart: Space.M }}>
        <Icon size='80px' source={CheckCircleOutlined} color='success' />
        <Stack direction='column' gap='2xs' align='center'>
          <Typography variant='bodySm' color='secondary'>
            {rmaTitle}
          </Typography>
          <Typography variant='headingXs'>{t('v2.rma.detail.title.has.submitted')}</Typography>

          <Link onPress={requestAnotherReturn} style={{ paddingBlockStart: Space['2Xs'] }}>
            <Typography variant='bodyLgSemibold'>{t('v2.request.another.return.text')}</Typography>
          </Link>
        </Stack>
      </Stack>
    );
  }
  return (
    <Stack direction='column' gap='s' align='center'>
      <Icon size='80px' source={CheckCircleOutlined} color='success' />
      <Stack direction='column' gap='2xs' align='center'>
        <Typography variant='bodySm' color='secondary'>
          {rmaTitle}
        </Typography>
        <Typography variant='headingXs'>{`${t('v2.rma.detail.title.has.submitted')}`}</Typography>
        <Link onPress={requestAnotherReturn}>
          <Typography variant='bodyLgSemibold'>{t('v2.request.another.return.text')}</Typography>
        </Link>
      </Stack>
    </Stack>
  );
};

// 从 RMA 列表进入的 RMA 详情页面展示的组件
export const RMATitleFromList = ({
  rmaTitle,
  rmaCreateAt,
}: {
  rmaTitle: string;
  rmaCreateAt?: string;
}) => {
  return (
    <Stack direction='column' align='center'>
      <Typography variant='heading2Xs'>{rmaTitle}</Typography>
      {rmaCreateAt && (
        <Typography variant='bodySm' color={'secondary'}>
          {formatDate(rmaCreateAt)}
        </Typography>
      )}
    </Stack>
  );
};

export const RMATitle = ({
  rmaId,
  isFromReview = false,
  rmaCreateAt,
  requestAnotherReturn,
}: RMATitleProps) => {
  const rmaTitle = rmaId ? `RMA #${rmaId}` : '';
  return (
    <Box>
      {isFromReview ? (
        <RMATitleFromReview rmaTitle={rmaTitle} requestAnotherReturn={requestAnotherReturn} />
      ) : (
        <RMATitleFromList rmaTitle={rmaTitle} rmaCreateAt={rmaCreateAt} />
      )}
    </Box>
  );
};

export default RMATitle;
