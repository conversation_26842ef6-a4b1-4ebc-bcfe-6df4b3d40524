import Image from 'next/image';
import { useTranslation } from 'react-i18next';

import { Box, Stack, Typography } from '@aftership/astra';
import { tokenVars } from '@aftership/astra-tokens/Contract.css';

import { Modal } from '@/components/Modal';
import useDevice from '@/hooks/useDevice';

import { wordBreakMetaStyle } from '../../styles/text.css';
import type { ReturnItem } from '../ReturnItems/ReturnItems';

const { Radius } = tokenVars.Semantic;

interface ReasonDetailModalProps {
  returnItemId: string;
  returnItems: ReturnItem[];
  isOpen: boolean;
  onClose: () => void;
}

const ReasonDetailModal = (props: ReasonDetailModalProps) => {
  const { t } = useTranslation();
  const { isOpen, onClose, returnItemId, returnItems } = props;
  const isMobile = useDevice().mobile;

  const {
    returnImages,
    notesToMerchant: comments,
    returnReason: reason,
    returnSubReason: subReason,
  } = returnItems.find((item) => item.itemId === returnItemId) ?? {};

  const Content = () => {
    return (
      <Stack direction='column' gap='xl'>
        {reason && (
          <Box>
            <Stack direction='column'>
              <Typography variant='bodyMdSemibold' color='secondary'>
                {t('v2.return.reason.detail.title')}
              </Typography>
              <Typography variant='bodyLg'>{`${reason} ${subReason ? `/ ${subReason}` : ''}`}</Typography>
            </Stack>
          </Box>
        )}

        {comments && (
          <Box>
            <Stack direction='column'>
              <Typography variant='bodyMdSemibold' color='secondary'>
                {t('page.description.comment')}
              </Typography>
              <Typography variant='bodyLg' className={wordBreakMetaStyle}>
                {comments}
              </Typography>
            </Stack>
          </Box>
        )}

        {!!returnImages?.length && (
          <Box>
            <Stack direction='column'>
              <Typography variant='bodyMdSemibold' color='secondary'>
                {t('page.description.images')}
              </Typography>
              <Box>
                <Stack wrap gap={isMobile ? 's' : 'xs'} style={{ gridColumn: 4, gridArea: '' }}>
                  {returnImages.map((image) => (
                    <Image
                      key={image.id}
                      width={100}
                      height={100}
                      src={image.src}
                      style={{ borderRadius: Radius.Xs }}
                      alt=''
                    />
                  ))}
                </Stack>
              </Box>
            </Stack>
          </Box>
        )}
      </Stack>
    );
  };
  return (
    <Modal isOpen={isOpen} title={t('v2.return.reason.detail.header')} onClose={onClose}>
      <Content />
    </Modal>
  );
};

export default ReasonDetailModal;
