import { useTranslation } from 'react-i18next';

import { Stack, Typography } from '@aftership/astra';

import { Button } from '@/components/Button';
import { Divider } from '@/components/Divider';

const Refundid = () => {
  const { t } = useTranslation();
  return (
    <>
      <Divider />
      <Stack direction='column' gap='xs'>
        <Typography variant='bodyMd'>{t('page.description.claimRefundDesc')}</Typography>
        <Button>{t('page.request.claimRefundNow')}</Button>
      </Stack>
    </>
  );
};

export default Refundid;
