import { useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Box, Stack, Typography } from '@aftership/astra';
import {
  AmountPrice,
  PresentmentMoney,
  Resolution,
  ReturnCostOption,
  ReturnImage,
} from '@aftership/returns-logics-core';

import { Card, CardHeader, CardSection } from '@/components/Card';
import { Divider } from '@/components/Divider';
import {
  ReturnItemsSectionTitleText,
  ReturnMethodSectionTitleText,
} from '@/features/preview/components/WithPreviewSection';
import { formatCostOfReturn } from '@/features/return-method/utils/costOfReturn';
import { ReturnMethodSuffix, genReturnRoutingRuleCode } from '@/i18n/dynamic';

import ItemCard from '../../../../components/ItemCard';
import useCartStyle from '../../hooks/useCartStyle';
import useHideSummaryWidth from '../../hooks/useHideSummaryWidth';
import { productTitleContainerStyles } from '../../styles/product.css';
import { ReturnItemCardLoading } from '../CardLoading';
import { ReasonDetailModal } from '../ReasonDetailModal';

export interface ReturnItem {
  itemId: string;
  productTitle: string;
  price?: PresentmentMoney;
  originPrice: PresentmentMoney;
  variantTitle: string;
  productCoverUrl: string;
  returnReason: string;
  returnSubReason?: string;
  quantity: number;
  returnImages: ReturnImage[];
  notesToMerchant: string;
  productTags: string[];
  exchangeNotes?: string;
}

export interface ReturnItemsComponentReturnMethod {
  id?: string;
  name?: string;
  description?: string;
}

export interface ReturnItemsComponentProps {
  resolution?: Resolution;
  returnItems?: ReturnItem[];
  costOfReturnOption?: ReturnCostOption;
  costOfReturAmountPrice?: AmountPrice | null;
  returnMethod?: ReturnItemsComponentReturnMethod;
  exchangeItemsExist?: boolean;
  isLoading?: boolean;
}

const ReturnItems = ({
  returnItems = [],
  costOfReturnOption,
  costOfReturAmountPrice,
  returnMethod,
  exchangeItemsExist,
  isLoading = false,
}: ReturnItemsComponentProps) => {
  const { t } = useTranslation();
  const hideSummaryWidth = useHideSummaryWidth();
  const { normalStyle, mobileNoOutlineClassname, isMobile, cardHeaderBottomStyle } = useCartStyle();
  const [returnDetailVisiable, setReturnDetailVisiable] = useState(false);
  const [returnItemId, setReturnItemId] = useState<string>();
  const appendCost = formatCostOfReturn(costOfReturAmountPrice, costOfReturnOption);
  const showReturnMethod = !!returnMethod?.id;
  const returnI18nMethodName = t(
    genReturnRoutingRuleCode({
      methodId: returnMethod?.id || '',
      suffix: ReturnMethodSuffix.Name,
    }),
    {
      rawValue: returnMethod?.name,
      defaultValue: returnMethod?.name,
    },
  );

  const returnMethodI18Description = t(
    genReturnRoutingRuleCode({
      methodId: returnMethod?.id || '',
      suffix: ReturnMethodSuffix.Description,
    }),
    { rawValue: returnMethod?.description, defaultValue: returnMethod?.description },
  );

  return (
    <>
      {isMobile && exchangeItemsExist && <Divider spacing='0' />}
      <Card
        {...normalStyle}
        className={mobileNoOutlineClassname}
        style={{ width: hideSummaryWidth || normalStyle.width }}
      >
        <CardHeader className={cardHeaderBottomStyle}>
          <ReturnItemsSectionTitleText variant='heading2Xs'>
            {t('dynamic.requestReview.returnItems')}
          </ReturnItemsSectionTitleText>
        </CardHeader>
        {isLoading ? (
          <ReturnItemCardLoading />
        ) : (
          <>
            <CardSection>
              <Stack direction='column' gap={isMobile ? '2xl' : 'none'}>
                {returnItems?.map((item) => (
                  <ItemCard
                    hideDivider={isMobile}
                    key={item.itemId}
                    style={{ borderWidth: 0 }}
                    productInfo={{
                      productTitle: item?.productTitle,
                      price: item.price,
                      originPrice: item.originPrice,
                      variantTitle: item?.variantTitle,
                      productCoverUrl: item.productCoverUrl,
                      quantity: isMobile ? item.quantity : void 0,
                      productTags: item.productTags,
                    }}
                    showMoreReason={!!item?.returnImages?.length || !!item?.notesToMerchant}
                    showMoreReasonClick={() => {
                      setReturnDetailVisiable(true);
                      setReturnItemId(item.itemId);
                    }}
                    returnReason={item.returnReason}
                    returnSubReason={item?.returnSubReason}
                    rightContent={
                      !isMobile && (
                        <Typography variant='bodyMd' style={{ lineHeight: '24px' }} as='p'>
                          x {item.quantity}
                        </Typography>
                      )
                    }
                    productTitleContainerStyles={isMobile ? productTitleContainerStyles : undefined}
                    exchangeNotes={item.exchangeNotes}
                  />
                ))}
              </Stack>
            </CardSection>

            {showReturnMethod && (
              <CardSection>
                <Stack gap='xs' direction='column'>
                  <ReturnMethodSectionTitleText variant='bodyMdSemibold'>
                    {t('dynamic.requestReview.shipping')}
                  </ReturnMethodSectionTitleText>
                  <Stack gap='2xs' direction='column'>
                    {returnI18nMethodName && (
                      <Box>
                        <Typography variant='bodyMd'>{returnI18nMethodName}</Typography>
                        <Typography variant='bodyMdSemibold'>{` ${appendCost ?? ''}`}</Typography>
                      </Box>
                    )}

                    {!!returnMethod?.description && (
                      <Typography variant='bodyMd' color='secondary'>
                        {returnMethodI18Description}
                      </Typography>
                    )}
                  </Stack>
                </Stack>
              </CardSection>
            )}

            {returnItemId && (
              <ReasonDetailModal
                returnItemId={returnItemId}
                returnItems={returnItems}
                isOpen={returnDetailVisiable}
                onClose={() => {
                  setReturnDetailVisiable(false);
                }}
              />
            )}
          </>
        )}
      </Card>
    </>
  );
};

export default ReturnItems;
