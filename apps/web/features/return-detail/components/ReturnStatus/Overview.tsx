import { ProgressBar, Stack } from '@aftership/astra';

import { ReturnDetailStatusTitleText } from '@/features/preview/components/WithPreviewSection';
import { useReturnTitle } from '@/features/return-list/hooks/useReturnTitle';
import { getCurrentProgress, getTotalProgress } from '@/features/return-list/utils/getProgress';

import { useReturnStatusContext } from './ReturnStatusProvider';
import ReturnBottomCard from './components/ReturnBottomCard';

const Overview = () => {
  const { returnDetail } = useReturnStatusContext();
  const returnCurrentStatusTitle = useReturnTitle({
    returnStatus: returnDetail?.status,
    shippingStatus: returnDetail?.shipping_status || undefined,
    returnMethodSlug: returnDetail?.return_method_slug,
    dropoffStatus: returnDetail?.dropoff_status,
  });
  const totalProgress = getTotalProgress(returnDetail?.return_method_slug);
  const currentProgress = getCurrentProgress({
    returnStatus: returnDetail?.status,
    shippingStatus: returnDetail?.shipping_status || undefined,
    returnMethodSlug: returnDetail?.return_method_slug,
    dropoffStatus: returnDetail?.dropoff_status,
  });

  return (
    <Stack direction='column' gap='m'>
      <Stack direction='column' gap='xs'>
        <ReturnDetailStatusTitleText variant='bodyLgSemibold'>
          {returnCurrentStatusTitle}
        </ReturnDetailStatusTitleText>
        {!!totalProgress && (
          <ProgressBar
            showPoint
            showLabel={false}
            value={currentProgress}
            maxValue={totalProgress}
            style={{ width: '100%' }}
          />
        )}
      </Stack>

      {returnDetail && <ReturnBottomCard returnDetail={returnDetail} />}
    </Stack>
  );
};

export default Overview;
