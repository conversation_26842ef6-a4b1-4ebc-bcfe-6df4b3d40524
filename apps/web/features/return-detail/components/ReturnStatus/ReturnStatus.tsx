import { useTranslation } from 'react-i18next';

import { Typography } from '@aftership/astra';
import {
  DisplayQrCodeSlug,
  ReturnItemType,
  ReturnMethod,
  ReturnStatus as ReturnStatusEnum,
  ShipmentsInfo,
  ShippingStatus,
} from '@aftership/returns-logics-core';

import { Card, CardHeader, CardSection } from '@/components/Card';
import { ReturnMethodSuffix, genReturnRoutingRuleCode } from '@/i18n/dynamic';

import Overview from './Overview';
import { ReturnStatusProvider } from './ReturnStatusProvider';
import { Instructions } from './components/Instructions';

import useCartStyle from '../../hooks/useCartStyle';
import useHideSummaryWidth from '../../hooks/useHideSummaryWidth';
import { ReturnStatusCardLoading } from '../CardLoading';

export interface IReturnStatusProps {
  returnDetail?: ReturnItemType;
  returnMethod?: ReturnMethod;
  shipments?: ShipmentsInfo[];
  isMobile?: boolean;
  isLoading?: boolean;
  displayQrCodeSlug?: DisplayQrCodeSlug;
}

const ReturnStatus = ({
  returnDetail,
  returnMethod,
  shipments,
  displayQrCodeSlug,
  isMobile = false,
  isLoading = false,
}: IReturnStatusProps) => {
  const { t } = useTranslation();
  const { normalStyle } = useCartStyle();

  const hideSummaryWidth = useHideSummaryWidth();

  // const isShowRefundid =
  //   returnDetail?.status &&
  //   ![ReturnStatusEnum.SUBMITTED, ReturnStatusEnum.REJECTED].includes(returnDetail.status) &&
  //   !returnDetail.refunded &&
  //   returnDetail.resolution === Resolution.Refundid;

  const showInstructions = Boolean(
    returnDetail?.status === ReturnStatusEnum.APPROVED &&
      returnDetail?.shipping_status !== ShippingStatus.Received &&
      returnMethod?.shipping_instructions,
  );

  return (
    <Card
      style={{ width: hideSummaryWidth || normalStyle.width }}
      paddingX={isMobile ? '16px' : 'auto'}
    >
      <CardHeader>
        <Typography variant='heading2Xs'>{t('page.general.returnStatus')}</Typography>
      </CardHeader>
      {isLoading ? (
        <ReturnStatusCardLoading />
      ) : (
        <CardSection>
          <ReturnStatusProvider
            returnDetail={returnDetail}
            returnMethod={returnMethod}
            shipments={shipments}
            displayQrCodeSlug={displayQrCodeSlug}
          >
            <Overview />
            {/* 新版先不考虑 refundid */}
            {/* {isShowRefundid && <Refundid />} */}
            {showInstructions && (
              <Instructions
                instructions={t(
                  genReturnRoutingRuleCode({
                    methodId: returnMethod?.id || '',
                    suffix: ReturnMethodSuffix.Instructions,
                  }),
                  {
                    rawValue: returnMethod?.shipping_instructions,
                    defaultValue: returnMethod?.shipping_instructions,
                  },
                )}
              />
            )}
          </ReturnStatusProvider>
        </CardSection>
      )}
    </Card>
  );
};

export default ReturnStatus;
