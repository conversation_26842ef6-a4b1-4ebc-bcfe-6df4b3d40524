import React from 'react';

import {
  DisplayQrCodeSlug,
  ReturnItemType,
  ReturnMethod,
  ShipmentsInfo,
} from '@aftership/returns-logics-core';

import useDevice from '@/hooks/useDevice';

export interface ReturnStatusContextProps {
  returnDetail?: ReturnItemType;
  returnMethod?: ReturnMethod;
  shipments?: ShipmentsInfo[];
  isMobile?: boolean;
  displayQrCodeSlug?: DisplayQrCodeSlug;
}

interface ReturnStatusProviderProps extends ReturnStatusContextProps {
  children: React.ReactNode;
}
export const ReturnStatusContext = React.createContext<ReturnStatusContextProps>({});

export const ReturnStatusProvider = (props: ReturnStatusProviderProps) => {
  const { children, ...rest } = props;
  const isMobile = useDevice().mobile;
  return (
    <ReturnStatusContext.Provider
      value={{
        ...rest,
        isMobile,
      }}
    >
      {children}
    </ReturnStatusContext.Provider>
  );
};

export const useReturnStatusContext = () => {
  return React.useContext(ReturnStatusContext);
};
