import { useTranslation } from 'react-i18next';

import { Stack, Typography } from '@aftership/astra';
import { tokenVars } from '@aftership/astra-tokens/Contract.css';

import Divider from '@/components/Divider/Divider.tsx';
import { RegexpRichText } from '@/components/RegexpRichText';

import { InstructionsClassName } from './Instructions.css.ts';

const { Space } = tokenVars.Semantic;

const Instructions = ({ instructions }: { instructions: string }) => {
  const { t } = useTranslation();
  return (
    <Stack direction='column'>
      <Divider spacing={Space.M} />
      <Stack direction='column' gap='xs'>
        <Typography variant='bodyMdSemibold' color='primary'>
          {t('page.description.instructions')}
        </Typography>
        <RegexpRichText variant='bodyMd' className={InstructionsClassName} color='secondary'>
          {instructions}
        </RegexpRichText>
      </Stack>
    </Stack>
  );
};

export default Instructions;
