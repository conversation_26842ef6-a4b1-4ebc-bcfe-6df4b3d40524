import { useTranslation } from 'react-i18next';

import { Button } from '@/components/Button';
import { EllipsisText } from '@/components/EllipsisText';
import {
  elipsisTextStyles,
  returnStatusActionButtonStyles,
} from '@/features/return-detail/styles/returnStatus.css';

const InvoiceButton = ({
  url,
  className = returnStatusActionButtonStyles,
}: {
  url: string;
  className?: string;
}) => {
  const { t } = useTranslation();
  return (
    url && (
      <Button
        variant='basic'
        size={'small'}
        className={className}
        onPress={() => {
          if (url) {
            window.open(url, '_blank');
          }
        }}
      >
        <EllipsisText text={t('page.action.viewInvoice')} className={elipsisTextStyles} />
      </Button>
    )
  );
};

export default InvoiceButton;
