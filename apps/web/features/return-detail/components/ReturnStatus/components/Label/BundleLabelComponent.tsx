import dayjs from 'dayjs';
import { useState } from 'react';

import { Stack } from '@aftership/astra';
import { DisplayQrCodeSlug, ShipmentsInfo } from '@aftership/returns-logics-core';

import { isQRCodeEnable } from '@/features/return-detail/utils/shipment';

import GeneralMoreLabels from './GeneralMoreLabels';
import WithQrCodeItem from './WithQrCodeItem';
import WithoutQrCodeItem from './WithoutQrCodeItem';

import { useReturnStatusContext } from '../../ReturnStatusProvider';

export interface BundleLabelProps {
  returnId: string;
  shipments: ShipmentsInfo[];
  additionalLabels?: ShipmentsInfo[];
  isAllAdditionalLabelPayloadAvailable?: boolean;
  displayQrCodeSlug?: DisplayQrCodeSlug;
}

const SHOW_ALL_BUTTON_MAX_COUNT = 3;

const BundleLabelComponent = ({
  returnId,
  additionalLabels,
  isAllAdditionalLabelPayloadAvailable,
  shipments,
  displayQrCodeSlug,
}: BundleLabelProps) => {
  const { returnDetail } = useReturnStatusContext();
  const {
    shipping_documents: returnDetaillShippingDocuments,
    shipping_documents_url: returnDetailshippingDocumentsUrl,
  } = returnDetail ?? {};
  const [isExpand, setIsExpand] = useState(false);
  const isShowAllButton = (shipments?.length ?? 0) > SHOW_ALL_BUTTON_MAX_COUNT;

  const displayShipments = isShowAllButton
    ? isExpand
      ? shipments
      : shipments?.slice(0, SHOW_ALL_BUTTON_MAX_COUNT)
    : shipments;

  return (
    <Stack direction='column' gap='xs'>
      {displayShipments?.map((shipment, index) => {
        const {
          tracking_slug: trackingSlug,
          label_qr_code: labelQrCode,
          retailer_label_url: retailerLabelUrl,
          packing_slip_url: packingSlipUrl,
          retailer_invoice_url: retailerInvoiceUrl,
          tracking_number: trackingNumber,
          created_at: createdAt,
          shipping_documents: shippingDocuments,
          shipping_documents_url: shippingDocumentsUrl,
        } = shipment;
        const qrCodeEnable = isQRCodeEnable(trackingSlug, labelQrCode);
        const isNotLastIndex = displayShipments.length - 1 !== index;
        const createdDate = createdAt ? dayjs(createdAt).format('MMM D, YYYY') : undefined;

        const bundleItemLabelShippingDocuments = shippingDocuments?.length
          ? shippingDocuments
          : returnDetaillShippingDocuments;
        const bundleItemLabelShippingDocumentsUrl =
          shippingDocumentsUrl || returnDetailshippingDocumentsUrl;
        return (
          <Stack direction='column' gap='xs' key={shipment.postmen_label_id}>
            {qrCodeEnable ? (
              <WithQrCodeItem
                items={shipment.items}
                slug={trackingSlug!}
                createdDate={createdDate}
                retailerLabelUrl={retailerLabelUrl}
                packingSlipUrl={packingSlipUrl}
                retailerInvoiceUrl={retailerInvoiceUrl}
                trackingNumber={trackingNumber}
                qrCodeUrl={labelQrCode!}
                shippingDocuments={bundleItemLabelShippingDocuments}
                shippingDocumentsUrl={bundleItemLabelShippingDocumentsUrl}
                needShowLocation={!isNotLastIndex}
                needShowAllButton={isShowAllButton && !isNotLastIndex}
                isExpandShipments={isExpand}
                onExpandShipments={() => setIsExpand(!isExpand)}
                displayQrCodeSlug={displayQrCodeSlug}
              />
            ) : (
              <WithoutQrCodeItem
                retailerLabelUrl={retailerLabelUrl}
                packingSlipUrl={packingSlipUrl}
                retailerInvoiceUrl={retailerInvoiceUrl}
                slug={trackingSlug}
                createdDate={createdDate}
                trackingNumber={trackingNumber}
                items={shipment.items}
                needShowAllButton={isShowAllButton && !isNotLastIndex}
                isExpandShipments={isExpand}
                onExpandShipments={() => setIsExpand(!isExpand)}
                shippingDocuments={bundleItemLabelShippingDocuments}
                shippingDocumentsUrl={bundleItemLabelShippingDocumentsUrl}
              />
            )}
          </Stack>
        );
      })}

      <GeneralMoreLabels
        returnId={returnId}
        additionalLabels={additionalLabels}
        isAllAdditionalLabelPayloadAvailable={isAllAdditionalLabelPayloadAvailable}
      />
    </Stack>
  );
};

export default BundleLabelComponent;
