import { t } from 'i18next';
import Image from 'next/image';
import React from 'react';

import { Stack, Typography } from '@aftership/astra';

import MobilePopup from '@/components/MobilePopup';
import { Modal } from '@/components/Modal';
import useDevice from '@/hooks/useDevice';

interface IProps {
  isOpen: boolean;
  onClose: VoidFunction;
}

const GeneralLabeFailedModal = ({ isOpen, onClose }: IProps) => {
  const isMobile = useDevice().mobile;
  const title = t('popup.description.failedToGenerateLabel');
  const description = t('popup.description.contactSupport', {
    phoneNumber: '08003131318',
  });

  const warningImage = require('@/assets/warning_outlined.svg').default;

  if (isMobile) {
    return (
      <MobilePopup isOpen={isOpen} title={title} onClose={onClose}>
        <Stack direction='column' align='center' gap='m'>
          <Image src={warningImage} width={96} alt='xx' />
          <Typography variant='bodyLg' textAlign='center' color='primary'>
            {description}
          </Typography>
        </Stack>
      </MobilePopup>
    );
  }

  return (
    <Modal isOpen={isOpen} title={title} onClose={onClose}>
      <Stack direction='column' align='center' gap='m'>
        <Image src={warningImage} width={96} alt='xx' />
        <Typography variant='bodyLg' textAlign='center' color='primary'>
          {description}
        </Typography>
      </Stack>
    </Modal>
  );
};

export default GeneralLabeFailedModal;
