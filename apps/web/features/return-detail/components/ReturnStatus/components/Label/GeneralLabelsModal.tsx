import { useTranslation } from 'react-i18next';

import { Box, NumberField, Stack, Typography } from '@aftership/astra';

import { Button } from '@/components/Button';
import MobilePopup from '@/components/MobilePopup';
import { Modal } from '@/components/Modal';
import useDevice from '@/hooks/useDevice';

interface IProps {
  isOpen: boolean;
  quantity: number;
  maxQuantity: number;
  isSubmitting?: boolean;
  setQuantity: (value: number) => void;
  onConfirm: VoidFunction;
  onClose: VoidFunction;
}

const GeneralLabelsModal = ({
  isOpen,
  quantity,
  maxQuantity,
  isSubmitting,
  setQuantity,
  onConfirm,
  onClose,
}: IProps) => {
  const isMobile = useDevice().mobile;
  const { t } = useTranslation();

  const modeTitle = t('popup.description.getMoreReturnLabels');
  const description = t('popup.description.labelsWantToGenerate');
  const actionTitle = t('popup.request.generateLabels');

  if (isMobile) {
    return (
      <MobilePopup
        isOpen={isOpen}
        title={modeTitle}
        onClose={onClose}
        footer={
          <Stack direction='column' gap='m' align='center'>
            <Button onPress={onConfirm} isLoading={isSubmitting} size='large'>
              {actionTitle}
            </Button>
          </Stack>
        }
      >
        <Stack direction='column' gap='m' align='center'>
          <Typography variant='bodyLg' textAlign='center'>
            {description}
          </Typography>
          <NumberField
            defaultValue={1}
            step={1}
            isWheelDisabled={true}
            value={quantity}
            minValue={1}
            maxValue={maxQuantity}
            variant='noOutline'
            onChange={setQuantity}
            style={{
              width: '188px',
            }}
          />
        </Stack>
      </MobilePopup>
    );
  }
  return (
    <Modal isOpen={isOpen} title={modeTitle} onClose={onClose}>
      <Stack direction='column' gap='xl' align='center'>
        <Typography variant='bodyLg' textAlign='center'>
          {description}
        </Typography>
        <NumberField
          defaultValue={1}
          step={1}
          isWheelDisabled={true}
          value={quantity}
          minValue={1}
          maxValue={maxQuantity}
          variant='noOutline'
          onChange={setQuantity}
          style={{
            width: '240px',
          }}
        />

        <Box style={{ width: '100%', paddingTop: '24px', maxWidth: '292px' }}>
          <Button
            onPress={onConfirm}
            isLoading={isSubmitting}
            size='large'
            style={{
              width: '100%',
            }}
          >
            {actionTitle}
          </Button>
        </Box>
      </Stack>
    </Modal>
  );
};

export default GeneralLabelsModal;
