import { useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Link, Typography, useToast } from '@aftership/astra';
import { GrayFeatureKey, ShipmentsInfo } from '@aftership/returns-logics-core';

import useGenerateAdditionalLabel from '@/features/return-detail/hooks/useGenerateAdditionalLabel';
import { useGetAutoGenerateLabelsStatus } from '@/features/return-detail/hooks/useGetAutoGenerateLabelsStatus';
import useGetGrayFeatureEnabled from '@/hooks/useGetGrayFeatureEnabled';

import GeneralLabeFailedModal from './GeneralLabeFailedModal';
import GeneralLabelsModal from './GeneralLabelsModal';
import GeneratingLabelModal from './GeneratingLabelModal';

interface GeneralMoreLabelsProps {
  returnId: string;
  additionalLabels?: ShipmentsInfo[];
  isAllAdditionalLabelPayloadAvailable?: boolean;
}

const GeneralMoreLabels = ({
  returnId,
  additionalLabels = [],
  isAllAdditionalLabelPayloadAvailable,
}: GeneralMoreLabelsProps) => {
  const { t } = useTranslation();

  const [generalLabelsCount, setGeneralLabelsCount] = useState(1);

  // General Labels Modal 打开关闭状态
  const [isOpenGeneralLabelsModal, setIsOpenGeneralLabelsModal] = useState(false);

  // GeneralLabeFailedModal 打开关闭状态
  const [isOpenGeneralLabeFailedModal, setIsOpenGeneralLabeFailedModal] = useState(false);

  // Generating Label Modal 打开关闭状态
  const [isOpenGeneratingLabelModal, setIsOpenGeneratingLabelModal] = useState(false);

  // const { mutate: generateLabelsMutate, isLoading } = useMutationReturnAdditionalLabels();

  const { autoCreateLabelsQuota } = useGetAutoGenerateLabelsStatus(additionalLabels);

  const { showToast } = useToast();

  const {
    handleUpdateLabelQuantity,
    goToAddMoreLabel,
    isSubmitQuantityLoading,
    additionalLabels: labels,
  } = useGenerateAdditionalLabel({
    onUpdateQuantityFailed: (error) => {
      if (error?.message) {
        showToast(error.message, { type: 'error' });
      }
    },

    onPollingShipments: () => {
      setIsOpenGeneralLabelsModal(false);
      setIsOpenGeneratingLabelModal(true);
    },
  });

  // TODO 灰度开关
  const isEnabledGetAdditionalLabel = useGetGrayFeatureEnabled(
    GrayFeatureKey.EnabledGetAdditionalLabel,
  );

  const handleGenerateLabels = () => {
    handleUpdateLabelQuantity(returnId, generalLabelsCount);
  };

  if (
    !isEnabledGetAdditionalLabel ||
    !autoCreateLabelsQuota ||
    !isAllAdditionalLabelPayloadAvailable
  ) {
    return null;
  }
  return (
    <>
      <Link
        onPress={() => {
          // TODO: 优化一下
          setIsOpenGeneralLabelsModal(true);
          goToAddMoreLabel();
        }}
      >
        <Typography variant='bodyMd'>{t('page.action.needMoreLabels')}</Typography>
      </Link>
      <GeneralLabelsModal
        isOpen={isOpenGeneralLabelsModal}
        quantity={generalLabelsCount}
        maxQuantity={autoCreateLabelsQuota}
        isSubmitting={isSubmitQuantityLoading}
        setQuantity={setGeneralLabelsCount}
        onConfirm={handleGenerateLabels}
        onClose={() => setIsOpenGeneralLabelsModal(false)}
      />

      <GeneratingLabelModal
        isOpen={isOpenGeneratingLabelModal}
        labels={labels}
        onError={() => setIsOpenGeneralLabeFailedModal(true)}
        onClose={() => setIsOpenGeneratingLabelModal(false)}
      />
      <GeneralLabeFailedModal
        isOpen={isOpenGeneralLabeFailedModal}
        onClose={() => setIsOpenGeneralLabeFailedModal(false)}
      />
    </>
  );
};

export default GeneralMoreLabels;
