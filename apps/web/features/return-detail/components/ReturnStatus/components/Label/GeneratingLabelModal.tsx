import { t } from 'i18next';
import React, { useEffect } from 'react';

import { <PERSON><PERSON>, Stack, Typography } from '@aftership/astra';
import { tokenVars } from '@aftership/astra-tokens/Contract.css';
import { ShipmentsInfo } from '@aftership/returns-logics-core';

import MobilePopup from '@/components/MobilePopup';
import { Modal } from '@/components/Modal';
import { useGetAutoGenerateLabelsStatus } from '@/features/return-detail/hooks/useGetAutoGenerateLabelsStatus';
import useDevice from '@/hooks/useDevice';

const { Space: PrimitiveSpace } = tokenVars.Primitive;

// 轮询时间间隔

interface IProps {
  isOpen: boolean;
  labels: ShipmentsInfo[];
  onSucess?: VoidFunction;
  onError?: VoidFunction;
  onClose: VoidFunction;
}

const GeneratingLabelModal = ({ isOpen, labels, onSucess, onError, onClose }: IProps) => {
  const isMobile = useDevice().mobile;
  // 轮询获取最新的 additional labels 数据
  const { isLabelsCreateFailed, isLabelsAllCreated } = useGetAutoGenerateLabelsStatus(labels);

  const title = t('popup.description.generatingLabel');
  const description = t('popup.request.generatingLabelLoading');

  useEffect(() => {
    if (isLabelsAllCreated) {
      onClose();
      onSucess?.();
    }
    if (isLabelsCreateFailed) {
      onClose();
      onError?.();
    }
  }, [isLabelsCreateFailed, isLabelsAllCreated, onClose, onSucess, onError]);

  if (isMobile) {
    return (
      <MobilePopup isOpen={isOpen} title={title} onClose={onClose}>
        <Stack
          gap='xl'
          align='center'
          direction='column'
          style={{ paddingBlock: PrimitiveSpace[1200] }}
        >
          <Spinner style={{ width: '22px' }} />
          <Typography variant='bodyLg' textAlign='center' color='primary'>
            {description}
          </Typography>
        </Stack>
      </MobilePopup>
    );
  }

  return (
    <Modal isOpen={isOpen} title={title} onClose={onClose}>
      <Stack
        direction='column'
        align='center'
        gap='xl'
        style={{ paddingBlock: PrimitiveSpace[1200] }}
      >
        <Spinner style={{ width: '22px' }} />
        <Typography variant='bodyLg' textAlign='center' color='primary'>
          {description}
        </Typography>
      </Stack>
    </Modal>
  );
};

export default GeneratingLabelModal;
