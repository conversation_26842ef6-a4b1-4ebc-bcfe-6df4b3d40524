import Image from 'next/image';
import { useTranslation } from 'react-i18next';

import { Box, Stack, Typography } from '@aftership/astra';
import { tokenVars } from '@aftership/astra-tokens/Contract.css';
import {
  DropoffItemDetail,
  DropoffLocation,
  DropoffLocations as DropoffLocationsType,
} from '@aftership/returns-logics-core';

import happyReturnLogo from '@/assets/happyreturn-log-vertical.png';
import { Button } from '@/components/Button';
import { Card } from '@/components/Card';
import useCartStyle from '@/features/return-detail/hooks/useCartStyle';
import { DropoffLocations } from '@/features/return-method/components/DropoffLocations';
import { ViewHappyReturnLocations } from '@/features/return-method/components/ViewHappyReturnLocations';
import useDevice from '@/hooks/useDevice';

import { RedirectButton } from './RedirectButton';

const { Radius } = tokenVars.Semantic;

interface HappyReturnLabelComponentProps {
  dropOffItemDetail?: DropoffItemDetail;
  dropoffLocations?: DropoffLocationsType;
  nearbyLocations?: DropoffLocation;
  onNearByLocations?: (latitude: number, longitude: number) => void;
}

const HappyReturnLabelComponent = (props: HappyReturnLabelComponentProps) => {
  const isMobile = useDevice().mobile;
  const { labelContainerStyle } = useCartStyle();
  const { dropOffItemDetail, dropoffLocations, nearbyLocations, onNearByLocations } = props;
  const dropOffLocation = dropoffLocations?.happy_returns?.locations?.[0];

  const { t } = useTranslation();

  return (
    <Box {...labelContainerStyle}>
      <Stack direction={isMobile ? 'column' : 'row'} gap='xl'>
        <Stack direction='row' justify='center'>
          <Card padding={0} style={{ borderRadius: Radius.Xs, width: 160, height: 210 }}>
            <Stack
              gap='xs'
              align='center'
              justify='center'
              direction='column'
              style={{ width: '100%', height: '100%' }}
            >
              <Image
                width={120}
                height={20}
                src={happyReturnLogo.src}
                style={{ marginTop: 2, marginBottom: 2 }}
                alt='Happy Return'
              />
              {dropOffItemDetail?.label_qr_code && (
                <Image
                  width={120}
                  height={120}
                  alt='happy return qrcode'
                  src={dropOffItemDetail.label_qr_code}
                />
              )}
              {dropOffItemDetail?.dropoff_number && (
                <Typography>{dropOffItemDetail?.dropoff_number}</Typography>
              )}
            </Stack>
          </Card>
        </Stack>
        <DropoffLocations
          title={t('page.happyReturn.recommends.title')}
          locations={dropoffLocations?.happy_returns?.locations}
          count={1}
          showMore={false}
        >
          <Stack direction='column' gap='xs' align='center'>
            <RedirectButton
              latitude={dropOffLocation?.address?.latitude}
              longitude={dropOffLocation?.address?.longitude}
              text={t('page.happyReturn.action.getDirections')}
            />
            <ViewHappyReturnLocations
              locations={nearbyLocations?.locations ?? []}
              allLocationsLink={nearbyLocations?.link}
              onNearByLocations={onNearByLocations}
              nearByLayout={isMobile ? 'center' : 'start'}
            >
              <Button
                size='small'
                variant='basic'
                style={{ backgroundColor: 'transparent', width: 248, height: 36, maxWidth: '100%' }}
              >
                {t('view.nearby.locations')}
              </Button>
            </ViewHappyReturnLocations>
          </Stack>
        </DropoffLocations>
      </Stack>
    </Box>
  );
};

export default HappyReturnLabelComponent;
