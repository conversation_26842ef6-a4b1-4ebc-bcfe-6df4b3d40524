import Image from 'next/image';
import { useTranslation } from 'react-i18next';

import { Box, Stack, Typography } from '@aftership/astra';
import { tokenVars } from '@aftership/astra-tokens/Contract.css';
import { DisplayQrCodeSlug, IShippingDocument } from '@aftership/returns-logics-core';

import { Button } from '@/components/Button';
import { Card } from '@/components/Card';
import useCartStyle from '@/features/return-detail/hooks/useCartStyle';
import {
  labelButtonStyles,
  returnStatusButtonFullWidthStyle,
} from '@/features/return-detail/styles/returnStatus.css';
import useDevice from '@/hooks/useDevice';

import GeneralMoreLabels from './GeneralMoreLabels';

import { InvoiceButton } from '../InvoiceButton';
import ApprovalShipment from '../ReturnCards/components/ApprovalShipment';

const { Space, Radius } = tokenVars.Semantic;

export enum QrCodeSize {
  normal = '160px',
  small = '120px',
}

export interface QrCodeComponentProps {
  returnId: string;
  slug: string;
  qrCodeUrl: string;
  retailerLabelUrl?: string;
  retailerInvoiceUrl?: string;
  packingSlipUrl?: string;
  isAllAdditionalLabelPayloadAvailable?: boolean;
  shippingDocumentsUrl?: string;
  shippingDocuments?: IShippingDocument[];
  displayQrCodeSlug?: DisplayQrCodeSlug;
}

const QrCodeComponent = ({
  returnId,
  qrCodeUrl,
  slug,
  retailerLabelUrl,
  retailerInvoiceUrl,
  packingSlipUrl,
  isAllAdditionalLabelPayloadAvailable,
  shippingDocumentsUrl,
  shippingDocuments,
  displayQrCodeSlug,
}: QrCodeComponentProps) => {
  const isMobile = useDevice().mobile;
  const carrierName = displayQrCodeSlug?.[slug]?.name ?? '';
  const locationUrl = displayQrCodeSlug?.[slug]?.dropOffUrl ?? '';

  const { t } = useTranslation();

  const { labelContainerStyle, isSpecialDesktopForReturnDetail } = useCartStyle();

  const approvalShipmentClassName = isSpecialDesktopForReturnDetail
    ? returnStatusButtonFullWidthStyle
    : labelButtonStyles;

  const viewLocationsClassName = isSpecialDesktopForReturnDetail
    ? returnStatusButtonFullWidthStyle
    : labelButtonStyles;

  const invoiceButtonClassName = isSpecialDesktopForReturnDetail
    ? returnStatusButtonFullWidthStyle
    : labelButtonStyles;

  return (
    <Stack direction='column' gap='m'>
      <Box {...labelContainerStyle}>
        <Stack direction={isMobile ? 'column' : 'row'} gap='xl'>
          <Stack direction='row' justify='center'>
            <Card padding={19} style={{ borderRadius: Radius.S, width: 160, height: 160 }}>
              <Box
                position='relative'
                width='100%'
                height='100%'
                style={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}
              >
                <Image
                  fill
                  alt='qrcode'
                  src={qrCodeUrl}
                  style={{
                    objectFit: 'contain',
                    objectPosition: 'center',
                  }}
                />
              </Box>
            </Card>
          </Stack>
          <Box>
            <Stack direction='column' gap='xs'>
              <Typography variant='bodyMdSemibold'> {t('page.description.qrcodeTitle')}</Typography>
              <Typography variant='bodyMd' color={'secondary'}>
                {t('page.description.qrcodeDesc', { carrierName: carrierName })}
              </Typography>
            </Stack>
            <Box paddingTop={Space.M}>
              <Stack gap='xs' direction={'column'} align={isMobile ? 'center' : 'start'}>
                {locationUrl && (
                  <Button
                    variant='basic'
                    className={viewLocationsClassName}
                    size={'small'}
                    onPress={() => {
                      window.open(locationUrl, '_blank');
                    }}
                  >
                    <Typography variant='bodyMd'>{t('page.request.viewLocations')}</Typography>
                  </Button>
                )}
                <ApprovalShipment
                  retailerLabelUrl={retailerLabelUrl}
                  packingSlipUrl={packingSlipUrl}
                  shippingDocuments={shippingDocuments}
                  shippingDocumentsUrl={shippingDocumentsUrl}
                  className={approvalShipmentClassName}
                  layout={isMobile ? 'center' : 'start'}
                />

                {retailerInvoiceUrl && (
                  <InvoiceButton url={retailerInvoiceUrl} className={invoiceButtonClassName} />
                )}
              </Stack>
            </Box>
          </Box>
        </Stack>
      </Box>

      <Box>
        <GeneralMoreLabels
          returnId={returnId}
          isAllAdditionalLabelPayloadAvailable={isAllAdditionalLabelPayloadAvailable}
        />
      </Box>
    </Stack>
  );
};

export default QrCodeComponent;
