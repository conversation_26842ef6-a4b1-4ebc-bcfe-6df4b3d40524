import { Button } from '@/components/Button';

interface IRedirectButtonProps {
  latitude?: string | number;
  longitude?: string | number;
  text: string;
}

export const RedirectButton = ({ latitude, longitude, text }: IRedirectButtonProps) => {
  if (!latitude || !longitude) {
    return null;
  }

  return (
    <Button
      size='small'
      variant='basic'
      style={{
        backgroundColor: 'transparent',
        width: 248,
        height: 36,
        maxWidth: '100%',
      }}
      onPress={() => {
        window.open(`https://maps.google.com/maps?q=${latitude},${longitude}`, '_blank');
      }}
    >
      {text}
    </Button>
  );
};
