import Image from 'next/image';
import { useTranslation } from 'react-i18next';

import { Box, Stack } from '@aftership/astra';
import { tokenVars } from '@aftership/astra-tokens/Contract.css';
import {
  DropoffItemDetail,
  DropoffLocations as DropoffLocationsType,
} from '@aftership/returns-logics-core';

import { Button } from '@/components/Button';
import { Card } from '@/components/Card';
import useCartStyle from '@/features/return-detail/hooks/useCartStyle';
import { DropoffLocations } from '@/features/return-method/components/DropoffLocations';
import { ViewRetailReworkLocations } from '@/features/return-method/components/ViewRetailReworkLocations';
import useDevice from '@/hooks/useDevice';

import { RedirectButton } from './RedirectButton';

const { Radius } = tokenVars.Semantic;

interface RetailReworkLabelComponentProps {
  dropOffItemDetail?: DropoffItemDetail;
  dropoffLocations?: DropoffLocationsType;
}

const RetailReworkLabelComponent = ({
  dropOffItemDetail,
  dropoffLocations,
}: RetailReworkLabelComponentProps) => {
  const { t } = useTranslation();
  const isMobile = useDevice().mobile;
  const { labelContainerStyle } = useCartStyle();
  const dropOffLocation = dropoffLocations?.retail_reworks?.locations?.[0];

  return (
    <Box {...labelContainerStyle}>
      <Stack direction={isMobile ? 'column' : 'row'} gap='xl'>
        <Stack direction='row' justify='center'>
          <Card padding={20} style={{ borderRadius: Radius.S, width: 160 }}>
            {dropOffItemDetail?.label_qr_code && (
              <Image
                width={120}
                height={120}
                alt='retail rework qrcode'
                src={dropOffItemDetail.label_qr_code}
              />
            )}
          </Card>
        </Stack>

        <DropoffLocations
          title={t('recommendation.locations')}
          locations={dropoffLocations?.retail_reworks?.locations}
          count={1}
          showMore={false}
        >
          <Stack direction='column' gap='xs' align='center'>
            <RedirectButton
              latitude={dropOffLocation?.address?.latitude}
              longitude={dropOffLocation?.address?.longitude}
              text={t('page.retailRework.action.getDirections')}
            />
            <ViewRetailReworkLocations
              locations={dropoffLocations?.retail_reworks?.locations ?? []}
            >
              <Button
                size='small'
                variant='basic'
                style={{ backgroundColor: 'transparent', width: 248, height: 36, maxWidth: '100%' }}
              >
                {t('view.more.locations')}
              </Button>
            </ViewRetailReworkLocations>
          </Stack>
        </DropoffLocations>
      </Stack>
    </Box>
  );
};

export default RetailReworkLabelComponent;
