import { Stack } from '@aftership/astra';
import { DisplayQrCodeSlug, IShippingDocument } from '@aftership/returns-logics-core';

import { isQRCodeEnable } from '@/features/return-detail/utils/shipment';

import GeneralMoreLabels from './GeneralMoreLabels';
import QrCodeComponent from './QrCodeComponent';

import { InvoiceButton } from '../InvoiceButton';
import ApprovalShipment from '../ReturnCards/components/ApprovalShipment';

export interface SingleLabelProps {
  returnId?: string;
  trackingSlug?: string;
  labelQrCode?: null | string;
  retailerLabelUrl?: string;
  packingSlipUrl?: string;
  retailerInvoiceUrl?: string;
  shippingDocuments?: IShippingDocument[];
  shippingDocumentsUrl?: string;
  isAllAdditionalLabelPayloadAvailable?: boolean;
  refetchAdditionalLabels?: () => void;
  postmenQRCodeEnabledByAdmin?: boolean | null;
  displayQrCodeSlug?: DisplayQrCodeSlug;
}

const SingleLabelComponent = (props: SingleLabelProps) => {
  const {
    trackingSlug,
    labelQrCode,
    returnId,
    retailerInvoiceUrl,
    retailerLabelUrl,
    packingSlipUrl,
    isAllAdditionalLabelPayloadAvailable,
    shippingDocuments,
    shippingDocumentsUrl,
    displayQrCodeSlug,
  } = props;
  if (!returnId) return null;
  const qrCodeEnable = isQRCodeEnable(trackingSlug, labelQrCode);

  return qrCodeEnable ? (
    <QrCodeComponent
      returnId={returnId}
      slug={trackingSlug!}
      qrCodeUrl={labelQrCode!}
      retailerInvoiceUrl={retailerInvoiceUrl}
      retailerLabelUrl={retailerLabelUrl}
      packingSlipUrl={packingSlipUrl}
      isAllAdditionalLabelPayloadAvailable={isAllAdditionalLabelPayloadAvailable}
      shippingDocuments={shippingDocuments}
      shippingDocumentsUrl={shippingDocumentsUrl}
      displayQrCodeSlug={displayQrCodeSlug}
    />
  ) : (
    <Stack direction='row' gap='xs' style={{ flexBasis: 0 }} wrap={true}>
      <ApprovalShipment
        retailerLabelUrl={retailerLabelUrl}
        packingSlipUrl={packingSlipUrl}
        shippingDocuments={shippingDocuments}
        shippingDocumentsUrl={shippingDocumentsUrl}
        layout={'start'}
        direction={'row'}
      />
      {retailerInvoiceUrl && <InvoiceButton url={retailerInvoiceUrl} />}
      <GeneralMoreLabels
        returnId={returnId}
        isAllAdditionalLabelPayloadAvailable={isAllAdditionalLabelPayloadAvailable}
      />
    </Stack>
  );
};

export default SingleLabelComponent;
