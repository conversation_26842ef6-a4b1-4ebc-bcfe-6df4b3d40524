import Image from 'next/image';
import { useTranslation } from 'react-i18next';

import { Box, Link, Stack, Typography } from '@aftership/astra';
import { tokenVars } from '@aftership/astra-tokens/Contract.css';
import {
  DisplayQrCodeSlug,
  IShippingDocument,
  ShipmentItemInfo,
} from '@aftership/returns-logics-core';

import { Card } from '@/components/Card';
import useCartStyle from '@/features/return-detail/hooks/useCartStyle';
import {
  labelButtonStyles,
  returnStatusButtonFullWidthStyle,
} from '@/features/return-detail/styles/returnStatus.css';
import useDevice from '@/hooks/useDevice';

import ShowAllShipmentsButton from './ShowAllShipmentsButton';

import { InvoiceButton } from '../InvoiceButton';
import ApprovalShipment from '../ReturnCards/components/ApprovalShipment';

const { Space } = tokenVars.Semantic;

export interface WithQrCodeItemProps {
  items: ShipmentItemInfo[] | null;
  qrCodeUrl: string;
  slug: string;
  createdDate?: string;
  retailerLabelUrl?: string | null;
  packingSlipUrl?: string;
  retailerInvoiceUrl?: string;
  trackingNumber?: string;
  needShowLocation?: boolean;
  needShowAllButton?: boolean;
  isExpandShipments?: boolean;
  onExpandShipments?: VoidFunction;
  shippingDocuments?: IShippingDocument[];
  shippingDocumentsUrl?: string;
  displayQrCodeSlug?: DisplayQrCodeSlug;
}

const generateTitle = (title: string, quantity: number, variantTitle?: string) => {
  if (!title.length && !variantTitle?.length) {
    return '';
  }
  return `${title}${variantTitle ? ` - ${variantTitle}` : ''} × ${quantity}`;
};

const WithQrCodeItem = ({
  items,
  slug,
  createdDate,
  qrCodeUrl,
  retailerLabelUrl,
  packingSlipUrl,
  retailerInvoiceUrl,
  trackingNumber,
  needShowLocation = true,
  needShowAllButton,
  isExpandShipments,
  shippingDocuments,
  shippingDocumentsUrl,
  displayQrCodeSlug,
  onExpandShipments,
}: WithQrCodeItemProps) => {
  const isMobile = useDevice().mobile;
  const { labelContainerStyle, isSpecialDesktopForReturnDetail } = useCartStyle();
  const { t } = useTranslation();

  const carrierName = displayQrCodeSlug?.[slug]?.name ?? '';
  const locationUrl = displayQrCodeSlug?.[slug]?.dropOffUrl ?? '';

  const approvalShipmentClassName = isSpecialDesktopForReturnDetail
    ? returnStatusButtonFullWidthStyle
    : labelButtonStyles;

  const invoiceButtonClassName = isSpecialDesktopForReturnDetail
    ? returnStatusButtonFullWidthStyle
    : labelButtonStyles;

  return (
    <Stack direction='column' gap='m'>
      <Box {...labelContainerStyle}>
        <Stack direction={isMobile ? 'column' : 'row'} gap='xl'>
          <Stack direction='row' justify='center'>
            <Card radiusSize='small' padding={19} style={{ height: 160, width: 160 }}>
              <Box
                position='relative'
                width='100%'
                height='100%'
                style={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}
              >
                <Image
                  fill
                  alt='qrcode'
                  src={qrCodeUrl}
                  style={{
                    objectFit: 'contain',
                    objectPosition: 'center',
                  }}
                />
              </Box>
            </Card>
          </Stack>

          <Box flex={1} overflow='auto'>
            <Stack direction='column' gap='xs'>
              <Box>
                <Stack direction='column'>
                  {trackingNumber && (
                    <Typography variant='bodyMdSemibold'>
                      {carrierName} {trackingNumber}
                    </Typography>
                  )}
                  {createdDate && (
                    <Typography variant='bodySm' color='secondary'>
                      {t('v2.date_formate.create_at', {
                        date: createdDate,
                      })}
                    </Typography>
                  )}
                </Stack>
              </Box>

              <Box>
                <Stack direction='column'>
                  <Typography variant='bodyMdSemibold'>{t('v2.label.title.items')}</Typography>
                  <Stack direction='column' gap='2xs'>
                    {items?.map((item) => {
                      return (
                        <Typography key={item.item_id} variant='bodyMd' color='secondary'>
                          {generateTitle(item.product_title, item.quantity, item.variant_title)}
                        </Typography>
                      );
                    })}
                  </Stack>
                </Stack>
              </Box>
            </Stack>
            <Box marginTop={Space.M}>
              <Stack direction='column' gap='m' align={isMobile ? 'center' : void 0}>
                <ApprovalShipment
                  retailerLabelUrl={retailerLabelUrl}
                  packingSlipUrl={packingSlipUrl}
                  shippingDocuments={shippingDocuments}
                  shippingDocumentsUrl={shippingDocumentsUrl}
                  className={approvalShipmentClassName}
                  layout={isMobile ? 'center' : 'start'}
                />
                {retailerInvoiceUrl && (
                  <InvoiceButton url={retailerInvoiceUrl} className={invoiceButtonClassName} />
                )}
              </Stack>
            </Box>
          </Box>
        </Stack>
      </Box>

      {needShowAllButton && (
        <Box>
          <ShowAllShipmentsButton isExpand={isExpandShipments} onPress={onExpandShipments} />
        </Box>
      )}
      {needShowLocation && (
        <Box>
          <Typography variant='bodyMd'>
            {t('page.description.qrcodeDesc', { carrierName: carrierName })}{' '}
            <Link target='_blank' href={locationUrl}>
              <Typography variant='bodyMd'>{t('page.request.viewLocations')}</Typography>
            </Link>
          </Typography>
        </Box>
      )}
    </Stack>
  );
};

export default WithQrCodeItem;
