import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';

import { Box, Stack, Typography } from '@aftership/astra';
import { IShippingDocument, ShipmentItemInfo } from '@aftership/returns-logics-core';

import useCartStyle from '@/features/return-detail/hooks/useCartStyle';
import { useReturnDetailFlow } from '@/features/return-detail/hooks/useReturnDetailFlow';
import {
  labelButtonStyles,
  returnStatusButtonFullWidthStyle,
} from '@/features/return-detail/styles/returnStatus.css';

import ShowAllShipmentsButton from './ShowAllShipmentsButton';

import { InvoiceButton } from '../InvoiceButton';
import ApprovalShipment from '../ReturnCards/components/ApprovalShipment';

export interface WithoutQrCodeItemProps {
  items: ShipmentItemInfo[] | null;
  retailerLabelUrl?: string | null;
  packingSlipUrl?: string;
  retailerInvoiceUrl?: string;
  slug?: string;
  createdDate?: string;
  trackingNumber?: string;
  needShowAllButton?: boolean;
  isExpandShipments?: boolean;
  onExpandShipments?: VoidFunction;
  shippingDocuments?: IShippingDocument[];
  shippingDocumentsUrl?: string;
}

const generateTitle = (title: string, quantity: number, variantTitle?: string) => {
  if (!title.length && !variantTitle?.length) {
    return '';
  }
  return `${title}${variantTitle ? ` - ${variantTitle}` : ''} × ${quantity}`;
};

const WithoutQrCodeItem = ({
  items,
  retailerInvoiceUrl,
  retailerLabelUrl,
  packingSlipUrl,
  slug,
  createdDate,
  trackingNumber,
  needShowAllButton,
  isExpandShipments,
  onExpandShipments,
  shippingDocuments,
  shippingDocumentsUrl,
}: WithoutQrCodeItemProps) => {
  const { t } = useTranslation();
  const { couriers } = useReturnDetailFlow();
  const { labelContainerStyle, isMobile, isSpecialDesktopForReturnDetail } = useCartStyle();

  const courierName = useMemo(() => {
    if (couriers && slug) {
      const courier = couriers?.find((item) => item.slug === slug);
      return courier?.name;
    }
    return '';
  }, [couriers, slug]);

  const approvalShipmentClassName = isSpecialDesktopForReturnDetail
    ? returnStatusButtonFullWidthStyle
    : isMobile
      ? labelButtonStyles
      : void 0;

  const invoiceButtonClassName = isSpecialDesktopForReturnDetail
    ? returnStatusButtonFullWidthStyle
    : isMobile
      ? labelButtonStyles
      : void 0;
  return (
    <Stack direction='column' gap='m'>
      <Box {...labelContainerStyle}>
        <Stack direction='column' gap='m'>
          <Box>
            <Stack direction='column'>
              {trackingNumber && (
                <Typography variant='bodyMdSemibold'>
                  {courierName} {trackingNumber}
                </Typography>
              )}
              {createdDate && (
                <Typography variant='bodySm'>
                  {t('v2.date_formate.create_at', {
                    date: createdDate,
                  })}
                </Typography>
              )}
            </Stack>
          </Box>

          <Box>
            <Stack direction='column'>
              <Typography variant='bodyMdSemibold'>{t('v2.label.title.items')}</Typography>
              {items?.map((item) => {
                return (
                  <Typography key={item.item_id} variant='bodyMd'>
                    {generateTitle(item.product_title, item.quantity, item.variant_title)}
                  </Typography>
                );
              })}
            </Stack>
          </Box>

          <Box>
            <Stack direction='column' gap='m' align={isMobile ? 'center' : void 0}>
              <ApprovalShipment
                retailerLabelUrl={retailerLabelUrl}
                packingSlipUrl={packingSlipUrl}
                shippingDocuments={shippingDocuments}
                shippingDocumentsUrl={shippingDocumentsUrl}
                className={approvalShipmentClassName}
                layout={isMobile ? 'center' : 'start'}
                direction={isMobile ? 'column' : 'row'}
              />
              {retailerInvoiceUrl && (
                <InvoiceButton url={retailerInvoiceUrl} className={invoiceButtonClassName} />
              )}
            </Stack>
          </Box>
        </Stack>
      </Box>

      {needShowAllButton && (
        <ShowAllShipmentsButton isExpand={isExpandShipments} onPress={onExpandShipments} />
      )}
    </Stack>
  );
};

export default WithoutQrCodeItem;
