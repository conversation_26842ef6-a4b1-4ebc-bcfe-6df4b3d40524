import { t } from 'i18next';

import {
  ReturnItemType,
  ReturnMethodSlug,
  ReturnStatus,
  ShippingStatus,
} from '@aftership/returns-logics-core';

import { ReturnDetailStatusTitleDescription } from '@/features/preview/components/WithPreviewSection';
import useQueryLabel from '@/features/return-detail/hooks/useQueryLabel';

import GeneratingLabelBox from './GeneratingLabelBox';
import ReturnApprovedCard from './ReturnCards/ReturnApprovedCard';
import ReturnNoLabelCard from './ReturnCards/ReturnNoLabelCard';
import ReturnPendingCard from './ReturnCards/ReturnPendingCard';
import ReturnReceivedCard from './ReturnCards/ReturnReceivedCard';
import ReturnRejectCard from './ReturnCards/ReturnRejectCard';
import ReturnShippedCard from './ReturnCards/ReturnShippedCard';
import ReturnShippingCard from './ReturnCards/ReturnShippingCard';

interface ReturnBottomCardProps {
  returnDetail: ReturnItemType;
}

const ReturnBottomCard = (props: ReturnBottomCardProps) => {
  const { returnDetail } = props;
  const { isLoading } = useQueryLabel();

  // 处理加载状态
  if (isLoading) return <GeneratingLabelBox />;

  const { return_method_slug, shipping_status, status } = returnDetail;

  // 1. 首先判断特殊条件
  if (
    return_method_slug === ReturnMethodSlug.RetailerLabel &&
    (shipping_status === ShippingStatus.InTransit || shipping_status === ShippingStatus.Delivered)
  ) {
    return <ReturnShippingCard />;
  }

  // 2. 然后基于状态进行判断
  switch (status) {
    case ReturnStatus.SUBMITTED:
      return <ReturnPendingCard returnMethodSlug={return_method_slug} />;

    case ReturnStatus.APPROVED:
      // 2.1 特殊情况：零售商标签 + 已收到状态
      if (
        return_method_slug === ReturnMethodSlug.RetailerLabel &&
        shipping_status === ShippingStatus.Received
      ) {
        return <ReturnReceivedCard />;
      }

      // 2.2 没有预付标签的情况
      if (shipping_status === ShippingStatus.NoPrepaidLabel) {
        if (return_method_slug === ReturnMethodSlug.CarrierPickup) {
          return (
            <ReturnDetailStatusTitleDescription variant='bodyMd'>
              {t('page.description.approvedStatusForCarrierPickup')}
            </ReturnDetailStatusTitleDescription>
          );
        }
        return <ReturnNoLabelCard />;
      }

      // 2.3 默认已批准情况
      return <ReturnApprovedCard />;

    case ReturnStatus.REJECTED:
      return <ReturnRejectCard />;

    case ReturnStatus.SHIPPED:
      return <ReturnShippedCard />;

    case ReturnStatus.RECEIVED:
      return <ReturnReceivedCard />;

    case ReturnStatus.DONE:
    default:
      return null;
  }
};

export default ReturnBottomCard;
