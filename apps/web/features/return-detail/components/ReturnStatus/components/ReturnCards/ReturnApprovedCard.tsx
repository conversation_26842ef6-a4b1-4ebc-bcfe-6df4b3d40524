import { useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';

import { Stack } from '@aftership/astra';
import { DropoffStatus, ReturnMethodSlug, ShippingStatus } from '@aftership/returns-logics-core';

import { ReturnDetailStatusTitleDescription } from '@/features/preview/components/WithPreviewSection';
import {
  TrackModalMode,
  useTrackDetailContext,
} from '@/features/return-detail/context/TrackDetailContext';
import { useReturnDetailFlow } from '@/features/return-detail/hooks/useReturnDetailFlow';

import ApprovalAction from './components/ApprovalAction';

import { useReturnStatusContext } from '../../ReturnStatusProvider';

const ReturnApprovedCard = () => {
  const { t } = useTranslation();
  const { returnDetail } = useReturnStatusContext();
  const { dropoffs } = useReturnDetailFlow();
  const { updateContextValue } = useTrackDetailContext();
  const returnMethod = returnDetail?.return_method_slug;
  const dropOffItemDetail = dropoffs?.[0];

  const dropOffStatus = dropOffItemDetail?.status;
  const shippingStatus = returnDetail?.shipping_status;

  const titleComponentFunc = useMemo(() => {
    if (returnMethod === ReturnMethodSlug.CustomerCourier) {
      return (
        <ReturnDetailStatusTitleDescription variant='bodyMd'>
          {t('page.description.approvedStatusForCarrier')}
        </ReturnDetailStatusTitleDescription>
      );
    }
    if (returnMethod === ReturnMethodSlug.GreenReturn) {
      return (
        <ReturnDetailStatusTitleDescription variant='bodyMd'>
          {t('page.description.approvedStatusForGreen')}
        </ReturnDetailStatusTitleDescription>
      );
    }

    if (returnMethod === ReturnMethodSlug.HappyReturns) {
      let title: string;
      if (
        !dropOffStatus ||
        [DropoffStatus.Creating, DropoffStatus.Failed].includes(dropOffStatus)
      ) {
        title = t('page.happyReturn.status.approvalPending');
      } else {
        title = t('page.happyReturn.status.approvalSucc');
      }

      return (
        <ReturnDetailStatusTitleDescription variant='bodyMd'>
          {title}
        </ReturnDetailStatusTitleDescription>
      );
    }

    if (returnMethod === ReturnMethodSlug.RetailRework) {
      let title: string;
      if (
        !dropOffStatus ||
        [DropoffStatus.Creating, DropoffStatus.Failed].includes(dropOffStatus)
      ) {
        title = t('page.retailRework.status.approvalPending');
      } else {
        title = t('page.retailRework.status.approvalSucc');
      }

      return (
        <ReturnDetailStatusTitleDescription variant='bodyMd'>
          {title}
        </ReturnDetailStatusTitleDescription>
      );
    }
    if (
      returnMethod === ReturnMethodSlug.RetailerLabel &&
      shippingStatus === ShippingStatus.NoLabel
    ) {
      return (
        <ReturnDetailStatusTitleDescription variant='bodyMd'>
          {t('page.description.readyStatus')}
        </ReturnDetailStatusTitleDescription>
      );
    }
    return null;
  }, [dropOffStatus, returnMethod, shippingStatus, t]);

  const handleAddTrackingDetail = useCallback(() => {
    updateContextValue({
      isTrackModalOpen: true,
      trackModalMode: TrackModalMode.ADD,
    });
  }, [updateContextValue]);

  return (
    <Stack direction='column' gap='m'>
      {titleComponentFunc}
      <ApprovalAction
        dropOffItemDetail={dropOffItemDetail}
        handleAddTrackingDetail={handleAddTrackingDetail}
      />
    </Stack>
  );
};

export default ReturnApprovedCard;
