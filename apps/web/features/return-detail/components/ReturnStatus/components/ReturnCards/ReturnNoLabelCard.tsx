import { useTranslation } from 'react-i18next';

import { ReturnDetailStatusTitleDescription } from '@/features/preview/components/WithPreviewSection';

const ReturnNoLabelCard = () => {
  const { t } = useTranslation();
  return (
    <ReturnDetailStatusTitleDescription variant='bodyMd'>
      {t('page.description.noLabelStatus')}
    </ReturnDetailStatusTitleDescription>
  );
};

export default ReturnNoLabelCard;
