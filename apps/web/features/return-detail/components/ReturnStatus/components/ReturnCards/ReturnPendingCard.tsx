import { useTranslation } from 'react-i18next';

import { ReturnMethodSlug } from '@aftership/returns-logics-core';

import { ReturnDetailStatusTitleDescription } from '@/features/preview/components/WithPreviewSection';

export interface Props {
  returnMethodSlug: ReturnMethodSlug;
}

const ReturnPendingCard = ({ returnMethodSlug }: Props) => {
  const { t } = useTranslation();
  return (
    <ReturnDetailStatusTitleDescription variant='bodyMd'>
      {returnMethodSlug === ReturnMethodSlug.HappyReturns
        ? t('page.happyReturn.description.pendingStatus')
        : t('page.description.pendingStatus')}
    </ReturnDetailStatusTitleDescription>
  );
};

export default ReturnPendingCard;
