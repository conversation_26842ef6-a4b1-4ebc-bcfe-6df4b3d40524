import { useTranslation } from 'react-i18next';

import { ReturnDetailStatusTitleDescription } from '@/features/preview/components/WithPreviewSection';

const ReturnReceivedCard = () => {
  const { t } = useTranslation();
  return (
    <ReturnDetailStatusTitleDescription variant='bodyMd'>
      {t('page.description.doneStatus')}
    </ReturnDetailStatusTitleDescription>
  );
};

export default ReturnReceivedCard;
