import React from 'react';
import { useTranslation } from 'react-i18next';

import { ReturnDetailStatusTitleDescription } from '@/features/preview/components/WithPreviewSection';

import { useReturnStatusContext } from '../../ReturnStatusProvider';

function ReturnRejectCard() {
  const { t } = useTranslation();
  const { returnDetail } = useReturnStatusContext();
  const { reject_reason: reason, exchange_for_anything_checkout_mismatch } = returnDetail ?? {};
  // 如果是 EFA 修改地址拒绝后,文案用本地的
  const finalReason = exchange_for_anything_checkout_mismatch?.shipping_address_mismatch
    ? t('status.description.rejectedEFAStatus')
    : reason;

  return (
    <ReturnDetailStatusTitleDescription variant='bodyMd'>{`${t('page.details.rejectReason')}: ${finalReason ?? ''}`}</ReturnDetailStatusTitleDescription>
  );
}

export default ReturnRejectCard;
