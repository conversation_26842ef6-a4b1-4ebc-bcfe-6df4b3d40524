import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';

import { Link, Skeleton, Stack, Typography } from '@aftership/astra';
import { ShipmentsInfo } from '@aftership/returns-logics-core';
import { useFlow } from 'returns-logics/react';

import {
  TrackModalMode,
  useTrackDetailContext,
} from '@/features/return-detail/context/TrackDetailContext';
import { useReturnDetailFlow } from '@/features/return-detail/hooks/useReturnDetailFlow';
import { wordBreakMetaStyle } from '@/features/return-detail/styles/text.css';

import { useReturnStatusContext } from '../../ReturnStatusProvider';
import { TrackingButton } from '../TrackingButton';

const ReturnShippedCard = () => {
  const { returnDetail, shipments } = useReturnStatusContext();
  const { returnDetailFlowMatches } = useReturnDetailFlow();
  const { updateContextValue } = useTrackDetailContext();
  const { children } = useFlow();
  const { t } = useTranslation();
  const couriers = children?.returnDetailSubFlow?.context?.couriers;
  const courierSlug = shipments?.[0]?.courier_slug;
  const trackingNumber = shipments?.[0]?.tracking_number;

  const courierDisplay = useMemo(() => {
    if (couriers?.length) {
      const existCourier = couriers?.find((courier) => {
        return courier.slug === courierSlug;
      });

      if (existCourier) {
        return existCourier.name;
      }
    }
    return courierSlug;
  }, [couriers, courierSlug]);
  const handleEditTrackModal = () => {
    updateContextValue({
      isTrackModalOpen: true,
      trackModalMode: TrackModalMode.EDIT,
      returnId: returnDetail?.id,
      trackingNumber,
      courierSlug,
    });
  };

  const isAddTrackingLoading = !!returnDetailFlowMatches?.({
    postShipmentsById: 'loading',
  });

  return (
    <Stack direction='column' gap='m'>
      <Stack direction='row' align='center' gap='2xs'>
        {isAddTrackingLoading ? (
          <Skeleton variant='rounded' height={24} width={248} />
        ) : (
          courierDisplay &&
          trackingNumber && (
            <Stack direction='row' gap='xs'>
              <Typography variant='bodyMd' className={wordBreakMetaStyle} color='secondary'>
                {`${courierDisplay}: ${trackingNumber}`}
              </Typography>
              <Link
                onPress={() => {
                  handleEditTrackModal();
                }}
              >
                {t('page.action.edit')}
              </Link>
            </Stack>
          )
        )}
      </Stack>
      <TrackingButton shipment={shipments?.[0] ?? ({} as ShipmentsInfo)} />
    </Stack>
  );
};

export default ReturnShippedCard;
