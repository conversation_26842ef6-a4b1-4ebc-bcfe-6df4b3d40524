import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';

import { Box, Stack, Typography } from '@aftership/astra';
import { ShipmentsInfo } from '@aftership/returns-logics-core';
import { useFlow } from 'returns-logics/react';

import useCartStyle from '@/features/return-detail/hooks/useCartStyle';

import { useReturnStatusContext } from '../../ReturnStatusProvider';
import { TrackingButton } from '../TrackingButton';

const ReturnShippingCard = () => {
  const { children } = useFlow();
  const returnDetailFlowContext = children?.returnDetailSubFlow?.context;
  const couriers = useMemo(() => {
    return returnDetailFlowContext?.couriers ?? [];
  }, [returnDetailFlowContext]);
  const { labelContainerStyle } = useCartStyle();
  const { shipments } = useReturnStatusContext();
  const { t } = useTranslation();
  // 直接遍历 shipments，不需要过滤
  const validShipments = useMemo(() => {
    // 扁平化处理所有 shipments（包括 additional_labels）目的是处理 Flow 触发的分包成多个additional_labels的情况
    const flattenShipments = (shipments: ShipmentsInfo[]): ShipmentsInfo[] => {
      return shipments.reduce((acc, shipment) => {
        return [...acc, shipment, ...flattenShipments(shipment.additional_labels || [])];
      }, [] as ShipmentsInfo[]);
    };

    if (!shipments?.length) return [];
    return flattenShipments(shipments);
  }, [shipments]);

  // 获取快递公司显示名称
  const getCourierDisplay = (courierSlug?: string) => {
    if (couriers.length && courierSlug) {
      const existCourier = couriers.find((courier) => courier.slug === courierSlug);
      if (existCourier) {
        return existCourier.name;
      }
    }
    return courierSlug;
  };

  // 生成商品标题
  const generateTitle = (title: string, quantity: number, variantTitle?: string) => {
    if (!title.length && !variantTitle?.length) {
      return '';
    }
    return `${title}${variantTitle ? ` - ${variantTitle}` : ''} × ${quantity}`;
  };

  // 根据 shipment 数量决定展示方式
  if (validShipments.length === 0) {
    return null;
  }

  if (validShipments.length === 1) {
    // 单个 shipment - 保留现状展示
    const { tracking_number: trackingNumber, courier_slug: courierSlug } = validShipments[0];
    const courierDisplay = getCourierDisplay(courierSlug);

    return (
      <Stack direction='column' gap='m'>
        <Typography variant='bodyMd' color='secondary'>
          {`${courierDisplay}: ${trackingNumber}`}
        </Typography>
        <TrackingButton shipment={validShipments[0]} />
      </Stack>
    );
  } else {
    // 多个 shipment - 新的展示方式
    return (
      <Stack direction='column' gap='m'>
        {validShipments.map((shipment, index) => (
          <Box {...labelContainerStyle} key={shipment.return_shipment_id || index}>
            <Stack wrap direction='row' gap='s' justify='space-between' align='center'>
              <Stack direction='column' gap='s'>
                {/* 追踪号信息 */}
                <Typography variant='bodyMd' color='secondary'>
                  {getCourierDisplay(shipment.courier_slug)}: {shipment.tracking_number}
                </Typography>
                {/* 商品信息 */}
                {shipment.items && shipment.items.length > 0 && (
                  <Stack direction='column' gap='2xs'>
                    <Typography variant='bodyMd' color='secondary'>
                      {t('shipment.card.items.label', 'Items')}
                    </Typography>
                    {shipment.items.map((item) => (
                      <Typography key={item.item_id} variant='bodyMd' color='secondary'>
                        {generateTitle(item.product_title, item.quantity, item.variant_title)}
                      </Typography>
                    ))}
                  </Stack>
                )}
              </Stack>
              {/* 独立的追踪按钮 */}
              <TrackingButton shipment={shipment} />
            </Stack>
          </Box>
        ))}
      </Stack>
    );
  }
};

export default ReturnShippingCard;
