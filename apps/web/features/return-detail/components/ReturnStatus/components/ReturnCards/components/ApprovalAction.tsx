import { useCallback } from 'react';
import { useTranslation } from 'react-i18next';

import { Box, Typography } from '@aftership/astra';
import {
  DropoffItemDetail,
  DropoffStatus,
  GenerateLabelStatus,
  ReturnMethodSlug,
} from '@aftership/returns-logics-core';

import { Button } from '@/components/Button';
import { Divider } from '@/components/Divider';
import useCartStyle from '@/features/return-detail/hooks/useCartStyle';
import { useReturnDetailFlow } from '@/features/return-detail/hooks/useReturnDetailFlow';
import { returnStatusActionButtonStyles } from '@/features/return-detail/styles/returnStatus.css';
import { isQRCodeEnable } from '@/features/return-detail/utils/shipment';

import ApprovalShipment from './ApprovalShipment';
import InStoreApprovalAction from './InStoreApprovalAction';
import ShipmentLabelContainer from './ShipmentLabelContainer';

import { useReturnStatusContext } from '../../../ReturnStatusProvider';
import BundleLabelComponent from '../../Label/BundleLabelComponent';
import HappyReturnLabelComponent from '../../Label/HappyReturnLabelComponent';
import RetailReworkLabelComponent from '../../Label/RetailReworkLabelComponent';
import SingleLabelComponent from '../../Label/SingleLabelComponent';

interface Props {
  dropOffItemDetail?: DropoffItemDetail;
  handleAddTrackingDetail: VoidFunction;
}

const ApprovalAction = ({ handleAddTrackingDetail, dropOffItemDetail }: Props) => {
  const { t } = useTranslation();
  const { isMobile } = useCartStyle();
  const {
    returnMethod,
    dropoffLocations,
    nearbyLocations,
    returnDetailFlowDispatch,
    returnDetailFlowCurrentStep,
    displayQrCodeSlug,
    couriers,
  } = useReturnDetailFlow();
  const { returnDetail, shipments } = useReturnStatusContext();

  const {
    id: returnId,
    return_method_slug: returnMethodSlug,
    packing_slip_url: packingSlipUrl,
    shipping_documents: shippingDocuments,
    shipping_documents_url: shippingDocumentsUrl,
  } = returnDetail ?? {};

  const dropOffStatus = dropOffItemDetail?.status;

  const handleNearByLocations = useCallback(
    (latitude: number, longitude: number) => {
      if (dropoffLocations && returnDetailFlowCurrentStep === 'fetchDescisionDataDone')
        returnDetailFlowDispatch?.({
          type: 'LOAD_NEARBY_LOCATIONS',
          data: { latitude, longitude },
        });
    },
    [dropoffLocations, returnDetailFlowCurrentStep, returnDetailFlowDispatch],
  );

  const retailStoreLocationUrl = returnMethod?.retail_store_location_url;
  const showRmaQrCode = returnMethod?.show_rma_qr_code;

  if (returnMethodSlug === ReturnMethodSlug.CustomerCourier) {
    return (
      <ApprovalShipment
        slot={
          <Button
            isFullWidth={isMobile}
            onPress={handleAddTrackingDetail}
            size={'small'}
            className={returnStatusActionButtonStyles}
          >
            {t('page.action.enterShipmentDetails')}
          </Button>
        }
        direction={isMobile ? 'column' : 'row'}
        layout='start'
        packingSlipUrl={packingSlipUrl}
        shippingDocuments={shippingDocuments}
        shippingDocumentsUrl={shippingDocumentsUrl}
      />
    );
  }

  if (returnMethodSlug === ReturnMethodSlug.CarrierPickup) {
    if (shipments && shipments.length) {
      const [firstShipment] = shipments;
      const {
        courier_slug: courierSlug,
        retailer_label_url: retailerLabelUrl,
        tracking_number: trackingNumber,
        packing_slip_url: packingSlipUrl,
        shipping_documents: shippingDocuments,
        shipping_documents_url: shippingDocumentsUrl,
      } = firstShipment;

      const courierName = couriers?.find((item) => item.slug === courierSlug)?.name;

      return (
        <ApprovalShipment
          slot={
            <Box width={'100%'}>
              <Typography color='secondary'>
                {courierName ? `${courierName}:` : courierName} {trackingNumber}
              </Typography>
            </Box>
          }
          direction={isMobile ? 'column' : 'row'}
          layout='start'
          retailerLabelUrl={retailerLabelUrl}
          packingSlipUrl={packingSlipUrl}
          shippingDocuments={shippingDocuments}
          shippingDocumentsUrl={shippingDocumentsUrl}
        />
      );
    }
  }

  if (returnMethodSlug === ReturnMethodSlug.InStore) {
    return (
      <InStoreApprovalAction
        retailStoreLocationUrl={retailStoreLocationUrl}
        packingSlipUrl={packingSlipUrl}
        shippingDocuments={shippingDocuments}
        shippingDocumentsUrl={shippingDocumentsUrl}
        showRmaQrCode={showRmaQrCode}
        rmaId={returnDetail?.rma_id}
      />
    );
  }

  // 当 returnMethod 为 Happy_return 时
  if (
    returnMethodSlug === ReturnMethodSlug.HappyReturns &&
    dropOffStatus &&
    ![DropoffStatus.Creating, DropoffStatus.Failed].includes(dropOffStatus)
  ) {
    return (
      <ShipmentLabelContainer>
        <HappyReturnLabelComponent
          dropOffItemDetail={dropOffItemDetail}
          dropoffLocations={dropoffLocations}
          nearbyLocations={nearbyLocations}
          onNearByLocations={handleNearByLocations}
        />
      </ShipmentLabelContainer>
    );
  }

  if (
    returnMethodSlug === ReturnMethodSlug.RetailerLabel ||
    returnMethodSlug === ReturnMethodSlug.CarrierDropoff
  ) {
    // 理论上不存在 shipments 为空的情况,这里容错处理下
    if (shipments && shipments.length) {
      // 所有的 return_shipment 是否已保存了打单 payload 的快照
      const isAllAdditionalLabelPayloadAvailable = shipments.every(
        (s) => s.additional_label_payload_available,
      );
      const additionalLabels = shipments?.[0]?.additional_labels ?? [];
      // 过滤出已创建完成的 additional labels
      const createdLabels = additionalLabels.filter(
        (label) =>
          label.retailer_label_source === 'custom' ||
          (label.retailer_label_source === 'postmen' &&
            label.auto_label_generation_status === GenerateLabelStatus.Created),
      );
      // 合并 additional labels 与 shipments 为同一数据源
      const mergedShipmentOfAdditionalLabels = [...createdLabels, ...shipments];

      if (mergedShipmentOfAdditionalLabels.length === 1 && returnId) {
        const [firstShipment] = shipments;
        const {
          tracking_slug: trackingSlug,
          label_qr_code: labelQrCode,
          retailer_label_url: retailerLabelUrl,
          packing_slip_url: packingSlipUrl,
          retailer_invoice_url: retailerInvoiceUrl,
          postmen_label_qr_code_enabled: postmenQRCodeEnabledByAdmin,
          shipping_documents: shippingDocuments,
          shipping_documents_url: shippingDocumentsUrl,
        } = firstShipment;

        // TODO shippingDocuments 和 shippingDocumentsUrl 在 shipment 中不存在
        const qrCodeEnable = isQRCodeEnable(trackingSlug, labelQrCode);
        // 单 label 的情况
        return (
          <ShipmentLabelContainer hiddenDivder={!qrCodeEnable}>
            <SingleLabelComponent
              trackingSlug={trackingSlug}
              labelQrCode={labelQrCode}
              retailerLabelUrl={retailerLabelUrl}
              packingSlipUrl={packingSlipUrl}
              retailerInvoiceUrl={retailerInvoiceUrl}
              postmenQRCodeEnabledByAdmin={postmenQRCodeEnabledByAdmin}
              shippingDocuments={shippingDocuments}
              shippingDocumentsUrl={shippingDocumentsUrl}
              returnId={returnId}
              isAllAdditionalLabelPayloadAvailable={isAllAdditionalLabelPayloadAvailable}
              displayQrCodeSlug={displayQrCodeSlug}
            />
          </ShipmentLabelContainer>
        );
      }
      // 多 label 的情况
      return (
        returnId && (
          <Box>
            <Divider />
            <ShipmentLabelContainer>
              <BundleLabelComponent
                returnId={returnId}
                additionalLabels={additionalLabels}
                isAllAdditionalLabelPayloadAvailable={isAllAdditionalLabelPayloadAvailable}
                shipments={mergedShipmentOfAdditionalLabels}
                displayQrCodeSlug={displayQrCodeSlug}
              />
            </ShipmentLabelContainer>
          </Box>
        )
      );
    }
    return null;
  }

  // 当 returnMethod 为 retail_rework 时
  if (
    returnMethodSlug === ReturnMethodSlug.RetailRework &&
    dropOffStatus &&
    ![DropoffStatus.Creating, DropoffStatus.Failed].includes(dropOffStatus) &&
    dropOffItemDetail
  ) {
    return (
      <RetailReworkLabelComponent
        dropoffLocations={dropoffLocations}
        dropOffItemDetail={dropOffItemDetail}
      />
    );
  }

  return null;
};

export default ApprovalAction;
