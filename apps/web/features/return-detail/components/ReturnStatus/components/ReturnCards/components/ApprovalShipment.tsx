import { t } from 'i18next';

import { Stack } from '@aftership/astra';
import { IShippingDocument, IShippingDocumentType } from '@aftership/returns-logics-core';

import { Button } from '@/components/Button';
import { EllipsisText } from '@/components/EllipsisText';
import {
  elipsisTextStyles,
  returnStatusActionButtonStyles,
} from '@/features/return-detail/styles/returnStatus.css';
import useDevice from '@/hooks/useDevice';

interface IProps {
  packingSlipUrl?: string;
  retailerLabelUrl?: string | null;
  shippingDocuments?: IShippingDocument[];
  shippingDocumentsUrl?: string;
  className?: string;
  layout?: 'start' | 'center' | 'end';
  direction?: 'row' | 'column';
  slot?: React.ReactNode;
}

interface ILegacyRMALabelUrl {
  retailerLabelUrl?: string | null;
  packingSlipUrl?: string;
}
interface INewRMALabelUrl {
  shippingDocuments?: IShippingDocument[];
  shippingDocumentsUrl?: string;
}

const getShippingDocumentDisplayName = (shippingDocuments?: IShippingDocument[]): string => {
  if (shippingDocuments) {
    // 呈现的文案有优先级，label -> packing slip -> shipping documents
    for (const document of shippingDocuments) {
      if (document.type === IShippingDocumentType.Label) {
        return t('page.action.downloadReturnsLabel');
      }
      if (document.type === IShippingDocumentType.PackingSlip) {
        return t('page.action.downloadPackingSlip');
      }

      if (document.type === IShippingDocumentType.ConditionalShippingDocument) {
        return t('page.action.downloadShippingDocument');
      }
    }
  }
  return '';
};

const formatShippingDocumentUrl = (legacyData: ILegacyRMALabelUrl, newData: INewRMALabelUrl) => {
  // 如果有新数据，走新数据的逻辑，只会展示一个按钮
  if (newData.shippingDocumentsUrl) {
    const content = getShippingDocumentDisplayName(newData.shippingDocuments);
    return [
      {
        content,
        url: newData.shippingDocumentsUrl,
      },
    ];
  }

  // 如果是老数据，维持之前的逻辑，可能会存在多个按钮
  return [
    {
      content: t('page.action.downloadReturnsLabel'),
      url: legacyData.retailerLabelUrl,
    },
    {
      content: t('page.action.downloadPackingSlip'),
      url: legacyData.packingSlipUrl,
    },
  ].filter((item) => item.url);
};

const ApprovalShipment = ({
  shippingDocuments,
  shippingDocumentsUrl,
  packingSlipUrl,
  retailerLabelUrl,
  className = returnStatusActionButtonStyles,
  layout = 'start',
  direction = 'column',
  slot,
}: IProps) => {
  const documents = formatShippingDocumentUrl(
    { retailerLabelUrl, packingSlipUrl },
    { shippingDocuments, shippingDocumentsUrl },
  );
  const isMobile = useDevice().mobile;

  return (
    <Stack
      wrap
      gap='xs'
      direction={direction}
      align={layout}
      style={{
        width: isMobile ? '100%' : 'auto',
        flexBasis: isMobile ? 'auto' : 0,
        alignSelf: 'stretch',
      }}
    >
      {slot}
      {documents.map((document) => {
        return (
          <Button
            key={document.url}
            variant='basic'
            size={'small'}
            isFullWidth={isMobile}
            className={className}
            onPress={() => {
              if (document.url) window.open(document.url, '_blank');
            }}
          >
            <EllipsisText text={document.content} className={elipsisTextStyles} />
          </Button>
        );
      })}
    </Stack>
  );
};

export default ApprovalShipment;
