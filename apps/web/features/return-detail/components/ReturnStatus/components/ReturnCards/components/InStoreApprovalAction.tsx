import { QRCodeSVG } from 'qrcode.react';
import React from 'react';
import { useTranslation } from 'react-i18next';

import { Box, Card, Stack, Typography } from '@aftership/astra';
import { tokenVars } from '@aftership/astra-tokens/Contract.css';
import { IShippingDocument } from '@aftership/returns-logics-core';

import { Button } from '@/components/Button';
import { EllipsisText } from '@/components/EllipsisText';
import useCartStyle from '@/features/return-detail/hooks/useCartStyle';
import {
  elipsisTextStyles,
  returnStatusActionButtonStyles,
} from '@/features/return-detail/styles/returnStatus.css';
import {
  labelButtonStyles,
  returnStatusButtonFullWidthStyle,
} from '@/features/return-detail/styles/returnStatus.css';

import ApprovalShipment from './ApprovalShipment';
import ShipmentLabelContainer from './ShipmentLabelContainer';

export interface InStoreApprovalActionProps {
  packingSlipUrl?: string;
  shippingDocumentsUrl?: string;
  shippingDocuments?: IShippingDocument[];
  retailStoreLocationUrl?: string;
  rmaId?: string;
  showRmaQrCode?: boolean;
}

const { Space, Radius } = tokenVars.Semantic;

const InStoreApprovalAction = (props: InStoreApprovalActionProps) => {
  const {
    packingSlipUrl,
    shippingDocumentsUrl,
    shippingDocuments,
    retailStoreLocationUrl,
    showRmaQrCode,
    rmaId,
  } = props;
  const { t } = useTranslation();
  const { isMobile } = useCartStyle();
  const { labelContainerStyle, isSpecialDesktopForReturnDetail } = useCartStyle();

  const approvalShipmentClassName = isSpecialDesktopForReturnDetail
    ? returnStatusButtonFullWidthStyle
    : labelButtonStyles;

  const viewLocationsClassName = isSpecialDesktopForReturnDetail
    ? returnStatusButtonFullWidthStyle
    : labelButtonStyles;

  return showRmaQrCode ? (
    <ShipmentLabelContainer hiddenDivder={false}>
      <Box {...labelContainerStyle}>
        <Stack direction={isMobile ? 'column' : 'row'} gap='xl'>
          <Stack direction='row' justify='center'>
            <Card
              padding={19}
              style={{
                borderRadius: Radius.S,
                width: 160,
                height: 160,
                backgroundColor: 'white',
              }}
            >
              <QRCodeSVG value={rmaId ?? ''} width={120} height={120} />
            </Card>
          </Stack>
          <Box>
            <Stack direction='column' gap='xs'>
              <Typography variant='bodyMdSemibold'> {t('page.description.qrcodeTitle')}</Typography>
              <Typography variant='bodyMd' color={'secondary'}>
                {t('page.description.inStore.qrcodeDesc')}
              </Typography>
            </Stack>
            <Box paddingTop={Space.M}>
              <Stack gap='xs' direction={'column'} align={isMobile ? 'center' : 'start'}>
                {retailStoreLocationUrl && (
                  <Button
                    variant='basic'
                    className={viewLocationsClassName}
                    size={'small'}
                    onPress={() => {
                      window.open(retailStoreLocationUrl, '_blank');
                    }}
                  >
                    <Typography variant='bodyMd'>{t('page.request.viewLocations')}</Typography>
                  </Button>
                )}
                <ApprovalShipment
                  packingSlipUrl={packingSlipUrl}
                  shippingDocuments={shippingDocuments}
                  shippingDocumentsUrl={shippingDocumentsUrl}
                  className={approvalShipmentClassName}
                  layout={isMobile ? 'center' : 'start'}
                />
              </Stack>
            </Box>
          </Box>
        </Stack>
      </Box>
    </ShipmentLabelContainer>
  ) : (
    <Stack direction='row' gap='xs' align={isMobile ? 'center' : void 0} wrap={true}>
      <ApprovalShipment
        packingSlipUrl={packingSlipUrl}
        shippingDocuments={shippingDocuments}
        shippingDocumentsUrl={shippingDocumentsUrl}
      />
      {!!retailStoreLocationUrl && (
        <Button
          size={'small'}
          variant='basic'
          className={returnStatusActionButtonStyles}
          onPress={() => {
            window.open(retailStoreLocationUrl, '_blank');
          }}
        >
          <EllipsisText text={t('page.request.viewStoreLocations')} className={elipsisTextStyles} />
        </Button>
      )}
    </Stack>
  );
};

export default InStoreApprovalAction;
