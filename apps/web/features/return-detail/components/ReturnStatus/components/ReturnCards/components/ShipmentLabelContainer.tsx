import { ReactNode } from 'react';
import { useTranslation } from 'react-i18next';

import { Stack, Typography } from '@aftership/astra';

import { Divider } from '@/components/Divider';

export const ShipmentLabelContainer = ({
  hiddenDivder = false,
  children,
}: {
  hiddenDivder?: boolean;
  children: ReactNode | ReactNode[];
}) => {
  const { t } = useTranslation();
  return (
    // TODO: Divider 20px
    <>
      {!hiddenDivder && <Divider spacing='2xs' />}
      {hiddenDivder ? (
        children
      ) : (
        <Stack direction='column' gap='xs'>
          <Typography variant='bodyMdSemibold'>{t('v2.shipping.title')}</Typography>
          {children}
        </Stack>
      )}
    </>
  );
};
export default ShipmentLabelContainer;
