import { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Stack, Typography } from '@aftership/astra';
import { tokenVars } from '@aftership/astra-tokens/Contract.css';
import {
  ExchangeChargeMode,
  ExchangeMode,
  RefundDestination,
  Resolution,
  ReturnItemType,
  SummaryTotalType,
} from '@aftership/returns-logics-core';
import { useFlow } from 'returns-logics/react';

import useDevice from '@/hooks/useDevice';
import { useTranslateResolutionInfo } from '@/hooks/useResolutionInfo';

import { Summary } from './Summary';
import { SummaryProvider } from './Summary/SummaryProvider';

const { Space } = tokenVars.Semantic;

const RightColumn = ({
  returnDetail,
  financialOutcome,
}: {
  returnDetail?: ReturnItemType;
  financialOutcome?: SummaryTotalType;
}) => {
  const { t } = useTranslation();
  const flow = useFlow();
  const isMobile = useDevice().mobile;
  const [isFold, setIsFold] = useState(true);
  const translatedResolutionInfo = useTranslateResolutionInfo();
  const isReplaceWithSameItem = returnDetail?.resolution === Resolution.ReplaceTheSameItem;

  const isCostOfReturnIncluded =
    !!returnDetail?.return_preview_summary?.cost_of_return_included_in_calculation;
  const isChangeByOthers =
    flow.context?.storeConfig?.shopInfo?.exchange_mode === ExchangeMode.DifferentPrice ||
    flow.context?.storeConfig?.shopInfo?.exchange_rule_item_price_difference_settlement ===
      ExchangeChargeMode.ChargeByOthers;
  const isShowReplaceSameItemDesc =
    !isCostOfReturnIncluded &&
    isReplaceWithSameItem &&
    !!Number(returnDetail?.return_preview_summary?.cost_of_return_set?.presentment_money?.amount) &&
    isChangeByOthers;
  const haveExchangeItems = Boolean(returnDetail?.exchange_items?.length);

  const refundDestination = returnDetail?.refund_destination as RefundDestination;

  const isLegacyReplace =
    returnDetail?.exchange_rule_item_price_difference_settlement ===
      ExchangeChargeMode.ChargeByOthers &&
    returnDetail?.exchange_mode === ExchangeMode.DifferentPrice;

  const refundName = useMemo(() => {
    if (financialOutcome !== SummaryTotalType.Refund) return null;
    return translatedResolutionInfo?.[refundDestination]?.name ?? null;
  }, [financialOutcome, refundDestination, translatedResolutionInfo]);

  return (
    <Stack direction='column' gap='xl' style={{ paddingBottom: isMobile ? Space['2Xl'] : 0 }}>
      <SummaryProvider
        isFold={isFold}
        setIsFold={setIsFold}
        isLoading={false}
        isMobile={isMobile}
        previewSummary={returnDetail?.return_preview_summary}
        exchangeItemsLength={returnDetail?.exchange_items?.length}
        returnItemsLength={returnDetail?.items?.length}
        financialOutcome={financialOutcome}
        isReturnDetail={true}
        refundDestination={returnDetail?.refund_destination}
        refundName={refundName}
        haveExchangeItems={haveExchangeItems}
        isLegacyReplace={isLegacyReplace}
        isReturnCare={returnDetail?.is_return_care}
      >
        <Summary />
      </SummaryProvider>
      {isShowReplaceSameItemDesc && (
        <Typography
          variant='bodyMd'
          color='secondary'
        >{`*${t('page.description.replaceSameItemDesc')}`}</Typography>
      )}
    </Stack>
  );
};

export default RightColumn;
