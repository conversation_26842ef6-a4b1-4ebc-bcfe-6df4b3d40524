import { useTranslation } from 'react-i18next';

import { Box, Button, Icon, Stack, Typography } from '@aftership/astra';
import { AvatarOutlined, EmailOutlined, LocationOutlined } from '@aftership/astra-icons';
import { semanticTokenVar } from '@aftership/astra-tokens/semantic-token/Contract.css';
import { ContactRecipient, ReturnShippingAddress } from '@aftership/returns-logics-core';

import { Card, CardHeader, CardSection } from '@/components/Card';
import { Divider } from '@/components/Divider';
import { useShopInfo } from '@/features/returns/hooks/useShopInfo';

import useCartStyle from '../../hooks/useCartStyle';
import useHideSummaryWidth from '../../hooks/useHideSummaryWidth';
import { getFullShippingAddress } from '../../utils/shipment';

const ShippingAddress = ({
  address,
  contact,
  onEdit,
}: {
  address: ReturnShippingAddress;
  contact: ContactRecipient;
  onEdit?: () => void;
}) => {
  const { isMobile, normalStyle, mobileNoOutlineClassname } = useCartStyle();
  const hideSummaryWidth = useHideSummaryWidth();
  const { t } = useTranslation();
  const shopInfo = useShopInfo();

  return (
    <>
      {isMobile && <Divider spacing={semanticTokenVar.Space.Xs} />}
      <Card
        {...normalStyle}
        style={{ width: hideSummaryWidth || normalStyle.width }}
        className={mobileNoOutlineClassname}
      >
        <CardHeader>
          <Stack>
            <Box flex={1}>
              <Typography variant='heading2Xs'>
                {t('page.requestReview.contactDetails.title')}
              </Typography>
            </Box>
            {!!onEdit &&
              (shopInfo.allow_edit_contact_recipient ||
                shopInfo.allow_edit_return_shipping_address) && (
                <Button variant='plain' onPress={onEdit}>
                  {t('v2.review.contactDetails.edit')}
                </Button>
              )}
          </Stack>
        </CardHeader>

        <CardSection>
          <Stack direction='column' gap='xs'>
            {(contact?.firstName || contact?.lastName) && (
              <Stack align='center' gap='xs'>
                <Icon source={AvatarOutlined} size={20} />
                <Typography variant='bodyMd'>
                  {contact.firstName && `${contact.firstName},`}
                  {contact.lastName}
                </Typography>
              </Stack>
            )}
            <Stack align='center' gap='xs'>
              <Icon source={EmailOutlined} size={20} />
              <Typography variant='bodyMd'>{contact?.email}</Typography>
            </Stack>
            <Stack align='center' gap='xs'>
              <Icon source={LocationOutlined} size={20} />
              <Typography variant='bodyMd'>{getFullShippingAddress(address)}</Typography>
            </Stack>
          </Stack>
        </CardSection>
      </Card>
    </>
  );
};

export default ShippingAddress;
