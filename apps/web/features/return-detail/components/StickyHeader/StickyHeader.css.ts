import { style } from '@vanilla-extract/css';

import { tokenVars } from '@aftership/astra-tokens/Contract.css';

const { Space, Color } = tokenVars.Semantic;

export const stickyStyles = style({
  overflow: 'hidden',
  position: 'sticky',
  top: 0,
  width: '100%',
  transition: 'all 0.5s ease',
  zIndex: 100,
  backgroundColor: Color.Bg.Body,
  paddingTop: Space.Xl,
  paddingBottom: Space.M,
  paddingLeft: Space.L,
  paddingRight: Space.L,
});
