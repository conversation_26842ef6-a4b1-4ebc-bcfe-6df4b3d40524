import React, { CSSProperties, useRef } from 'react';

import { Box, IconButton, Stack, Typography } from '@aftership/astra';
import { ChevronLeftOutlined } from '@aftership/astra-icons';

import { stickyStyles } from './StickyHeader.css';

export interface HeaderProps {
  onBack?: VoidFunction;
  title?: React.ReactNode;
  leftSlot?: React.ReactNode;
  rightSlot?: React.ReactNode;
  height?: string | number;
  scrollContainerRef?: React.RefObject<HTMLDivElement>;
  style?: CSSProperties;
}

const Hidden = ({ children }: { children: React.ReactNode }) => {
  return (
    <Box height={0} overflow='hidden'>
      {children}
    </Box>
  );
};

const StickyHeader = ({ title = '', leftSlot, rightSlot, style, onBack }: HeaderProps) => {
  const headerRef = useRef<HTMLDivElement>(null);

  const EmptySlot = () => {
    return <Box />;
  };
  const LeftSlot = () => {
    return <IconButton onPress={onBack} icon={ChevronLeftOutlined} size='large' />;
  };

  return (
    <div ref={headerRef} className={`${stickyStyles}`} style={style}>
      <Stack gap='m' align='center' direction='row'>
        {leftSlot ? leftSlot : <LeftSlot />}
        <Hidden>{rightSlot ? rightSlot : <EmptySlot />}</Hidden>

        <Box flex={1}>
          {React.isValidElement(title) ? (
            title
          ) : (
            <Typography variant='headingXs' textAlign='center' color='primary' as='p'>
              {title}
            </Typography>
          )}
        </Box>

        <Hidden>{leftSlot ? leftSlot : <LeftSlot />}</Hidden>
        {rightSlot ? rightSlot : <EmptySlot />}
      </Stack>
    </div>
  );
};

export default StickyHeader;
