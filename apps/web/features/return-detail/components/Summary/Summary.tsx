import { cloneElement, useMemo } from 'react';
import { useTranslation } from 'react-i18next';

import { Stack, Typography } from '@aftership/astra';
import { tokenVars } from '@aftership/astra-tokens/Contract.css';

import { Card, CardHeader, CardSection } from '@/components/Card';
import { useSummaryHidden } from '@/features/return-detail/hooks/useSummaryHidden';
import { getCostOfReturnValue } from '@/features/return-method/utils/costOfReturn';
import { getStoreCreditIncentive } from '@/features/return-method/utils/storeCreditIncentive';

import { useSummaryContext } from './SummaryProvider';
import Accordion from './components/Accordion';
import CostOfReturnSection from './components/CostOfReturnSection';
import ExchangesSection from './components/ExchangesSection';
import ReturnCreditsSection from './components/ReturnCreditsSection';
import ShippingFeeSection from './components/ShippingFeeSection';
import ShippingTaxSection from './components/ShippingTaxSection';
import StoreCreditIncentive from './components/StoreCreditIncentive';
import TotalSection from './components/TotalSection';
import { useGetExchangeShippingValue } from './hooks';

import useCartStyle from '../../hooks/useCartStyle';

const { Space } = tokenVars.Semantic;

const Summary = () => {
  const { t } = useTranslation();
  const {
    isFold,
    setIsFold,
    isPreview,
    previewSummary,
    haveExchangeItems,
    isAllItemReplace,
    isReturnCare,
  } = useSummaryContext();
  const { summaryCartProps, isMobile } = useCartStyle();
  const hideSummary = useSummaryHidden(isAllItemReplace);

  const shippingFeeValue = useGetExchangeShippingValue({
    shippingFee: previewSummary?.exchange_shipping_fee_set?.presentment_money,
    shippingFeeOption: previewSummary?.exchange_shipping_fee_option,
  });

  const costOfReturnValue = getCostOfReturnValue(
    previewSummary?.cost_of_return_set?.presentment_money,
    previewSummary?.return_cost_option,
    false,
  );

  const shippingTax = previewSummary?.exchange_shipping_tax_set?.presentment_money;
  // 含税时不展示 shipping tax
  const showShippingTax =
    !!shippingTax && !!shippingTax.amount && !previewSummary.exchange_item_taxes_included;

  const storeCreditIncentiveValue = getStoreCreditIncentive(
    previewSummary?.store_credit_incentive_set?.presentment_money,
  );

  const isShowShippingFee = !!shippingFeeValue;
  const isShowCostOfReturn = !!costOfReturnValue;

  const sections = [
    haveExchangeItems ? <ExchangesSection isFold={isFold} key={'exchange'} /> : null,
    <ReturnCreditsSection isFold={isFold} key={'returnCredits'} />,
    isShowShippingFee && (
      <ShippingFeeSection key='shippingFee' shippingFeeValue={shippingFeeValue} />
    ),
    storeCreditIncentiveValue && (
      <StoreCreditIncentive
        storeCreditIncentiveValue={storeCreditIncentiveValue}
        // 桌面端下面出现 cost of return 或者 shipping tax 并且展开时需要有个间隔
        style={{
          paddingBlockEnd:
            !isFold && !isMobile && costOfReturnValue && (isShowCostOfReturn || showShippingTax)
              ? Space.S
              : 0,
        }}
      />
    ),
    isShowCostOfReturn && (
      <CostOfReturnSection
        key='costOfReturn'
        costOfReturnValue={costOfReturnValue}
        returnCostOption={previewSummary?.return_cost_option}
        includedInCalculation={previewSummary?.cost_of_return_included_in_calculation}
        isReturnCare={isReturnCare}
      />
    ),
    showShippingTax && <ShippingTaxSection key='shippingTax' price={shippingTax} />,
  ]?.filter(Boolean) as React.ReactElement[];

  const summaryLayoutGap = useMemo(() => {
    if (!isMobile) {
      return isFold ? 's' : 'none';
    } else {
      return isFold ? 's' : '2xs';
    }
  }, [isFold, isMobile]);

  return (
    !hideSummary && (
      <Stack direction='column' gap='3xl'>
        <Card {...summaryCartProps}>
          <CardHeader>
            <Stack direction='row' gap='3xl' justify='space-between'>
              <Typography variant='heading2Xs'>{t('dynamic.requestReview.summary')}</Typography>
              <Accordion isFold={isFold} setIsFold={setIsFold} isPreview={isPreview} />
            </Stack>
          </CardHeader>

          <CardSection>
            <Stack direction='column' gap={summaryLayoutGap}>
              {sections?.map((children, index) => {
                return cloneElement(children, {
                  ...children.props,
                  showDivider: index + 1 !== sections.length,
                });
              })}
            </Stack>
            <Stack direction='column'>
              <TotalSection />
            </Stack>
          </CardSection>
        </Card>
      </Stack>
    )
  );
};

export default Summary;
