import React from 'react';

import { RefundDestination, SummaryTotalType } from '@aftership/returns-logics-core';
import { ReturnPreviewSummary } from '@aftership/returns-logics-core';

export interface SummaryContextProps {
  isFold: boolean;
  setIsFold?: (isFold: boolean) => void;
  previewSummary?: ReturnPreviewSummary;
  isLoading?: boolean;
  exchangeItemsLength?: number;
  returnItemsLength?: number;
  financialOutcome?: SummaryTotalType;
  isMobile?: boolean;
  isPreview?: boolean;
  isReturnDetail?: boolean;
  refundDestination?: RefundDestination;
  refundName?: string | null;
  /**
   * @description 是否有换货商品，有的话需要展示 exchange item section
   */
  haveExchangeItems?: boolean;
  isAllItemReplace?: boolean;
  isLegacyReplace?: boolean;
  isReturnCare?: boolean;
}

interface SummaryProviderProps extends SummaryContextProps {
  children: React.ReactNode;
}
export const SummaryContext = React.createContext<SummaryContextProps>({
  isFold: true,
  isLegacyReplace: false,
});

export const SummaryProvider = (props: SummaryProviderProps) => {
  const { children, ...rest } = props;
  return <SummaryContext.Provider value={rest}>{children}</SummaryContext.Provider>;
};

export const useSummaryContext = () => {
  return React.useContext(SummaryContext);
};
