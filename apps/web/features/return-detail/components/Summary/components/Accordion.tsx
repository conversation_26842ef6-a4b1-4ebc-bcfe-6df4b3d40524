import { useTranslation } from 'react-i18next';

import { Icon, Pressable, Stack, Typography } from '@aftership/astra';
import { ChevronDownOutlined, ChevronUpOutlined } from '@aftership/astra-icons';

interface AccordionProps {
  isFold: boolean;
  setIsFold?: (isFold: boolean) => void;
  isPreview?: boolean;
}
const Accordion = ({ isPreview, isFold, setIsFold }: AccordionProps) => {
  const { t } = useTranslation();
  return (
    <Pressable
      onClick={() => {
        if (!isPreview) setIsFold?.(!isFold);
      }}
    >
      <Stack gap='2xs' direction='row' align='center'>
        <Typography>{t('v2.summary.fold.desc')}</Typography>
        <Icon source={isFold ? ChevronDownOutlined : ChevronUpOutlined} />
      </Stack>
    </Pressable>
  );
};

export default Accordion;
