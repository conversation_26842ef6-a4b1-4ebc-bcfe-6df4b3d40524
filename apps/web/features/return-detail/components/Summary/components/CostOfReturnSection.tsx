import { useTranslation } from 'react-i18next';

import { Stack, Tag, Typography } from '@aftership/astra';
import { ProtectionFilled } from '@aftership/astra-icons';
import { ReturnCostOption } from '@aftership/returns-logics-core';

import SectionLayout, { SummarySectionName } from './SectionLayout';

const CostOfReturnSection = ({
  costOfReturnValue,
  returnCostOption,
  includedInCalculation,
  isReturnCare,
}: {
  costOfReturnValue: string;
  returnCostOption?: ReturnCostOption;
  includedInCalculation?: boolean;
  isReturnCare?: boolean;
}) => {
  const { t } = useTranslation();
  const deleteReturnCost =
    [
      ReturnCostOption.CUSTOM,
      ReturnCostOption.EstimatedLabel,
      ReturnCostOption.PercentageValue,
    ].includes(returnCostOption as ReturnCostOption) && includedInCalculation === false;

  return (
    costOfReturnValue && (
      <Stack direction='column' gap='2xs'>
        <SectionLayout name={SummarySectionName.CostOfReturn} gap='2xs'>
          <Stack gap='2xs' align='center' wrap='wrap'>
            <Typography variant='bodyLg' style={{ whiteSpace: 'nowrap' }}>
              {t('page.description.costOfReturns')}
            </Typography>
            {isReturnCare === true && (
              <Tag variant='filled' color='green' icon={ProtectionFilled}>
                {t('returnCare.freeReturn')}
              </Tag>
            )}
          </Stack>
          <Stack gap='xs'>
            {deleteReturnCost && (
              <Typography
                variant='bodyLg'
                color='tertiary'
                style={{
                  textDecoration: 'line-through',
                }}
              >
                {costOfReturnValue}
              </Typography>
            )}
            <Typography variant='bodyLg'>
              {deleteReturnCost ? t('returnCare.freeReturn.free') : costOfReturnValue}
            </Typography>
          </Stack>
        </SectionLayout>
      </Stack>
    )
  );
};

export default CostOfReturnSection;
