import { useTranslation } from 'react-i18next';

import { Stack, Typography } from '@aftership/astra';
import { tokenVars } from '@aftership/astra-tokens/Contract.css';
import { AmountPrice } from '@aftership/returns-logics-core';

import { Divider } from '@/components/Divider';
import { ExtraCreditRow } from '@/features/return-detail/components/Summary/components/ExtraCreditRow.tsx';
import useDevice from '@/hooks/useDevice';
import { useExtraCredit } from '@/hooks/useExtraCredit.ts';
import { toCurrency } from '@/utils/price';

import SectionLayout, { SectionCommonProps, SummarySectionName } from './SectionLayout';

import { useSummaryContext } from '../SummaryProvider';

const { Space } = tokenVars.Semantic;

const getDisplayValue = (price?: Maybe<AmountPrice>) => {
  const amount = Number(price?.amount ?? 0);
  return amount ? toCurrency(amount, price?.currency) : '-';
};
const ExchangesSection = ({ isFold, showDivider }: { isFold: boolean } & SectionCommonProps) => {
  const { previewSummary: returnPreviewSummary, exchangeItemsLength } = useSummaryContext();
  const { t } = useTranslation();
  const { creditAmount, showInExchanges } = useExtraCredit(returnPreviewSummary);

  const isMobile = useDevice().mobile;

  const exchangeTaxIncluded = returnPreviewSummary?.exchange_item_taxes_included;
  const exchangeSubtotal =
    returnPreviewSummary?.exchange_item_base_price_total_set?.presentment_money;
  const exchangeTotal = returnPreviewSummary?.exchange_total_set?.presentment_money;
  const exchangeTaxTotal = returnPreviewSummary?.exchange_item_tax_total_set?.presentment_money;

  const showTaxLabel = !!exchangeTaxIncluded && !!exchangeTotal;
  const showExchangeTax = !exchangeTaxIncluded && !!exchangeTaxTotal;

  // section 栏的展示价格
  const sectionValue = getDisplayValue(exchangeTotal);

  // exchange 的展示价格
  const exchangesValue = getDisplayValue(exchangeSubtotal);
  // 展示 tax 价格
  const exchangesTaxValue = showExchangeTax && exchangeTaxTotal && toCurrency(exchangeTaxTotal);

  const getExchangeItemQty = () => {
    if (exchangeItemsLength) return exchangeItemsLength;
    return '';
  };

  const exchangeItemQty = getExchangeItemQty();

  const exchangeItemLabel = `${Number(exchangeItemQty) > 1 ? t('v2.page.general.exchangeItems') : t('v2.page.general.exchangeItem')}${
    showTaxLabel ? ` (${t('page.general.taxIncluded')})` : ''
  }`;

  return (
    <Stack direction='column' gap='2xs'>
      <SectionLayout name={SummarySectionName.Exchanges}>
        <Typography variant='bodyLg'>{t('page.general.exchanges')}</Typography>
        <Typography variant='bodyLg'>{sectionValue}</Typography>
      </SectionLayout>

      {!isFold && (
        <>
          <SectionLayout>
            <Typography
              variant='bodyMd'
              color='secondary'
            >{`${exchangeItemQty} ${exchangeItemLabel}`}</Typography>
            <Typography variant='bodyMd' color='secondary'>
              {exchangesValue}
            </Typography>
          </SectionLayout>
          {showExchangeTax && (
            <SectionLayout>
              <Typography variant='bodyMd' color='secondary'>
                {t('v2.page.general.tax')}
              </Typography>
              <Typography variant='bodyMd' color='secondary'>
                {exchangesTaxValue ?? '-'}
              </Typography>
            </SectionLayout>
          )}
          {showInExchanges && <ExtraCreditRow creditAmount={creditAmount} />}
          {!isMobile && showDivider && <Divider spacing={Space.S} />}
        </>
      )}
    </Stack>
  );
};

export default ExchangesSection;
