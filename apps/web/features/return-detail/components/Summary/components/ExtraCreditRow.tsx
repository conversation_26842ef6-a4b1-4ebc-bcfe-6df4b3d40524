import React from 'react';
import { useTranslation } from 'react-i18next';

import { Typography } from '@aftership/astra';

import SectionLayout from '@/features/return-detail/components/Summary/components/SectionLayout.tsx';
import { toCurrency } from '@/utils/price.ts';

interface ExtraCreditRowProps {
  creditAmount: {
    amount: number;
    currency?: string;
  };
}
export const ExtraCreditRow: React.FC<ExtraCreditRowProps> = ({ creditAmount }) => {
  const { t } = useTranslation();
  return (
    <SectionLayout>
      <Typography variant='bodyMd' color='secondary'>
        {t('page.description.creditForExchange')}
      </Typography>
      <Typography variant='bodyMd' color='secondary'>
        {`(${toCurrency(creditAmount.amount, creditAmount?.currency)})`}
      </Typography>
    </SectionLayout>
  );
};
