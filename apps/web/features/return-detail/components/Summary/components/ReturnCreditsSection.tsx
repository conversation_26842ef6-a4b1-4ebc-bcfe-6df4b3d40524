import { useTranslation } from 'react-i18next';

import { Stack, Typography } from '@aftership/astra';
import { tokenVars } from '@aftership/astra-tokens/Contract.css';

import { Divider } from '@/components/Divider';
import { ExtraCreditRow } from '@/features/return-detail/components/Summary/components/ExtraCreditRow.tsx';
import useDevice from '@/hooks/useDevice';
import { useExtraCredit } from '@/hooks/useExtraCredit.ts';
import { toCurrency } from '@/utils/price';

import SectionLayout, { SectionCommonProps, SummarySectionName } from './SectionLayout';

import { useSummaryContext } from '../SummaryProvider';

const { Space } = tokenVars.Semantic;

const ReturnCreditsSection = ({
  isFold,
  showDivider,
}: { isFold: boolean } & SectionCommonProps) => {
  const { previewSummary: returnPreviewSummary, returnItemsLength } = useSummaryContext();
  const isMobile = useDevice().mobile;

  const { t } = useTranslation();
  const { creditAmount, showInReturnCredits } = useExtraCredit(returnPreviewSummary);
  const itemsTaxTotal = returnPreviewSummary?.return_item_tax_total_set?.presentment_money;
  const itemsTotal = returnPreviewSummary?.return_item_subtotal_set?.presentment_money;
  const taxIncluded = returnPreviewSummary?.return_item_taxes_included;

  // 单 resolution 时, ExtraCredit 要展示在 ReturnCredit 中 ,只需要判断 creditUsedAmount 是否大于 0
  const showTaxLabel = !!itemsTaxTotal && !!taxIncluded;
  const showItemsTax = !!itemsTaxTotal && !taxIncluded;

  // returns item 的价格和 returns item tax 价格总和
  const totalAmount = Number(itemsTotal?.amount ?? 0) + Number(itemsTaxTotal?.amount ?? 0);
  // 0 元时展示 $0.00 而不是 -，并且后端保证百分百会有 return_item_subtotal_set 金额，所以这里不可能存在 - 情况
  const returnsValue = `(${toCurrency(Number(itemsTotal?.amount ?? 0), itemsTotal?.currency)})`;
  const returnsTaxValue = toCurrency(itemsTaxTotal?.amount ?? 0, itemsTaxTotal?.currency);

  // section 栏的展示价格 - 老版本公式需要包含 Credit for exchange 金额
  // Return credit 应该是 Return item + Tax + Credit for exchange 的总和
  // 基于业务逻辑判断：如果税费包含在商品价格中，使用 itemsTotal；否则使用 totalAmount
  const extraCreditValue = showInReturnCredits ? creditAmount.amount : 0;
  const baseAmount = taxIncluded ? Number(itemsTotal?.amount ?? 0) : totalAmount;
  const sectionAmount = baseAmount + extraCreditValue;
  const sectionValue = `(${toCurrency(sectionAmount, itemsTotal?.currency)})`;

  const returnItemQty = returnItemsLength ? `${returnItemsLength}` : '';
  const returnItemLabel = `${Number(returnItemQty) > 1 ? t('v2.page.general.returnItems') : t('v2.page.general.returnItem')}${
    showTaxLabel ? ` (${t('page.general.taxIncluded')})` : ''
  }`;

  return (
    <Stack direction='column' gap='2xs'>
      <SectionLayout name={SummarySectionName.ReturnCredit}>
        <Typography variant='bodyLg'>{t('page.general.returnCredits')}</Typography>
        <Typography variant='bodyLg'>{sectionValue}</Typography>
      </SectionLayout>
      {!isFold && (
        <>
          <SectionLayout>
            <Typography
              variant='bodyMd'
              color='secondary'
            >{`${returnItemQty} ${returnItemLabel}`}</Typography>
            <Typography variant='bodyMd' color='secondary'>
              {returnsValue}
            </Typography>
          </SectionLayout>

          {showItemsTax && (
            <SectionLayout>
              <Typography variant='bodyMd' color='secondary'>
                {t('v2.page.general.tax')}
              </Typography>
              <Typography variant='bodyMd' color='secondary'>
                {returnsTaxValue ? `(${returnsTaxValue})` : '-'}
              </Typography>
            </SectionLayout>
          )}

          {showInReturnCredits && <ExtraCreditRow creditAmount={creditAmount} />}
          {!isMobile && showDivider && <Divider spacing={Space.S} />}
        </>
      )}
    </Stack>
  );
};

export default ReturnCreditsSection;
