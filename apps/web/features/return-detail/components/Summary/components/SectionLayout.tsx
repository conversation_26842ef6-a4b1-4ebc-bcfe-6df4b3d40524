import { ReactNode, useMemo } from 'react';

import { Skeleton, Stack, StackProps } from '@aftership/astra';

import { useSummaryContext } from '../SummaryProvider';

export enum SummarySectionName {
  Total = 'total',
  CostOfReturn = 'costOfReturn',
  ShippingFee = 'shippingFee',
  ReturnCredit = 'returnCredit',
  Exchanges = 'exchanges',
  StoreCreditIncentive = 'storeCreditIncentive',
}

export interface SectionCommonProps {
  showDivider?: boolean;
}

const SectionLayout = ({
  children,
  isControlledLoading,
  name,
  gap,
}: {
  children: [ReactNode, ReactNode];
  isControlledLoading?: boolean;
  name?: SummarySectionName;
  gap?: StackProps['gap'];
}) => {
  const { isLoading } = useSummaryContext();
  const skeletonWidth = name === SummarySectionName.Total ? '141px' : '96px';

  const sectionLoading = useMemo(() => {
    // If isLoading is boolean, use it directly
    if (typeof isControlledLoading === 'boolean') {
      return isControlledLoading;
    }
    return isLoading;
  }, [isLoading, isControlledLoading]);

  return (
    <Stack direction='row' justify='space-between' align='center' gap={gap}>
      {children?.[0]}
      {sectionLoading ? (
        <Skeleton variant='rounded' width={skeletonWidth} height={24} />
      ) : (
        children?.[1]
      )}
    </Stack>
  );
};

export default SectionLayout;
