import { useTranslation } from 'react-i18next';

import { Stack, Typography } from '@aftership/astra';

import SectionLayout, { SummarySectionName } from './SectionLayout';

const ShippingFeeSection = ({ shippingFeeValue }: { shippingFeeValue: string }) => {
  const { t } = useTranslation();

  return (
    <Stack gap='2xs' direction='column'>
      <SectionLayout name={SummarySectionName.ShippingFee}>
        <Typography variant='bodyLg'>{t('page.description.exchangeShipping')}</Typography>
        <Typography variant='bodyLg'>{shippingFeeValue}</Typography>
      </SectionLayout>
    </Stack>
  );
};

export default ShippingFeeSection;
