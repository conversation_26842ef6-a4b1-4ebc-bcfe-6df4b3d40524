import { useTranslation } from 'react-i18next';

import { Stack, Typography } from '@aftership/astra';
import { AmountPrice } from '@aftership/returns-logics-core';

import { toCurrency } from '@/utils/price';

import SectionLayout, { SummarySectionName } from './SectionLayout';

const ShippingTaxSection = ({ price }: { price?: AmountPrice | null }) => {
  const { t } = useTranslation();

  return (
    <Stack direction='column' gap='2xs'>
      <SectionLayout name={SummarySectionName.CostOfReturn}>
        <Typography variant='bodyLg'>{t('page.description.shippingTax')}</Typography>
        <Typography variant='bodyLg'>{toCurrency(price)}</Typography>
      </SectionLayout>
    </Stack>
  );
};

export default ShippingTaxSection;
