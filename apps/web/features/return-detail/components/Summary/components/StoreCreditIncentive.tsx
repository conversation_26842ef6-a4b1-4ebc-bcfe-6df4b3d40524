import { useTranslation } from 'react-i18next';

import { Stack, Typography } from '@aftership/astra';

import SectionLayout, { SummarySectionName } from './SectionLayout';

const StoreCreditIncentive = ({
  storeCreditIncentiveValue,
  style,
}: {
  storeCreditIncentiveValue: string;
  style: React.CSSProperties;
}) => {
  const { t } = useTranslation();

  return (
    <Stack gap='2xs' direction='column' style={style}>
      <SectionLayout name={SummarySectionName.ShippingFee}>
        <Typography variant='bodyLg'>{t('store_credit.incentive')}</Typography>
        <Typography variant='bodyLg'>{storeCreditIncentiveValue}</Typography>
      </SectionLayout>
    </Stack>
  );
};

export default StoreCreditIncentive;
