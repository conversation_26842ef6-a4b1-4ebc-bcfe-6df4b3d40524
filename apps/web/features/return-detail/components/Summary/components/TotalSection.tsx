import { Stack, Typography } from '@aftership/astra';
import { tokenVars } from '@aftership/astra-tokens/Contract.css';
import { SummaryTotalType } from '@aftership/returns-logics-core';

import { Divider } from '@/components/Divider';
import { useHideTotalSection } from '@/features/return-detail/components/Summary/hooks.ts';
import { getSummaryLabel, getTotalValue } from '@/features/return-detail/utils/totalSummary';
import { useMainFlow } from '@/hooks/useMainFlow.ts';

import SectionLayout, { SummarySectionName } from './SectionLayout';

import { useSummaryContext } from '../SummaryProvider';

const { Space } = tokenVars.Semantic;

const TotalSection = () => {
  const {
    context: { isMultipleResolution },
  } = useMainFlow();
  const { previewSummary, refundName, isLegacyReplace } = useSummaryContext();
  const financialOutcome = previewSummary?.financial_outcome;
  const totalLabel = (financialOutcome && getSummaryLabel(financialOutcome)) ?? null;
  const isRefund = financialOutcome === SummaryTotalType.Refund;
  const totalValue = getTotalValue(previewSummary);
  const hideTotalSection = useHideTotalSection({
    isMultipleResolution,
    outcome: financialOutcome,
    isLegacyReplace: !!isLegacyReplace,
  });

  if (hideTotalSection) {
    return null;
  }

  return (
    <>
      <Divider spacing={Space.L} />
      <Stack direction='column' gap='2xs'>
        <SectionLayout name={SummarySectionName.Total}>
          <Typography variant='bodyLgSemibold'>{totalLabel}</Typography>
          <Typography variant='bodyLgSemibold' textAlign='right'>
            {totalValue}
          </Typography>
        </SectionLayout>
        {isRefund && refundName && (
          <Stack justify='end'>
            <Typography variant='bodyMd' textAlign='right' style={{ maxWidth: 264 }}>
              {refundName}
            </Typography>
          </Stack>
        )}
      </Stack>
    </>
  );
};

export default TotalSection;
