import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';

import {
  AmountPrice,
  GrayFeatureKey,
  ShippingFeeOption,
  SummaryTotalType,
} from '@aftership/returns-logics-core';

import useGetGrayFeatureEnabled from '@/hooks/useGetGrayFeatureEnabled.ts';
import { toCurrency } from '@/utils/price';

interface IExchangeShippingProps {
  shippingFee?: AmountPrice | null;
  shippingFeeOption?: ShippingFeeOption | null;
}

export const useGetExchangeShippingValue = ({
  shippingFee,
  shippingFeeOption,
}: IExchangeShippingProps) => {
  const { t } = useTranslation();

  const shippingFeeValue = useMemo<string | null>(() => {
    if (shippingFeeOption === ShippingFeeOption.CUSTOM) {
      return shippingFee ? toCurrency(shippingFee.amount, shippingFee.currency) : null;
    } else if (shippingFeeOption === ShippingFeeOption.FREE) {
      return t('page.description.free');
    }
    return null;
  }, [shippingFee, shippingFeeOption, t]);

  return shippingFeeValue;
};

interface IHideTotalSectionProps {
  isMultipleResolution: boolean;
  isLegacyReplace: boolean;
  outcome?: SummaryTotalType;
}

/**
 * + 多 resolution
 * + unchanged: 都显示, total refund 0
 * + refund: 根据灰度控制
 *
 * - 单 resolution
 * - unchanged: 除 legacy replace 外都显示
 * - refund: 根据灰度控制
 */
export const useHideTotalSection = ({
  isMultipleResolution,
  isLegacyReplace,
  outcome,
}: IHideTotalSectionProps) => {
  const hiddenSummaryRefundTotal = useGetGrayFeatureEnabled(GrayFeatureKey.HiddenSumaryRefundTotal);

  if (!outcome) {
    return false;
  }

  if (isMultipleResolution) {
    if (outcome === SummaryTotalType.Refund) {
      return hiddenSummaryRefundTotal;
    } else {
      return false;
    }
  } else {
    if (outcome === SummaryTotalType.Refund) {
      return hiddenSummaryRefundTotal;
    } else if (outcome === SummaryTotalType.Upsell) {
      return false;
    } else {
      return outcome === SummaryTotalType.Unchanged && isLegacyReplace;
    }
  }
};
