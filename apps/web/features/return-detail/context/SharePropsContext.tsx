import React, { useContext, useEffect, useState } from 'react';

import { useReviewFlow } from '@/hooks/useReviewFlow';

interface SharePropsContextProvider {
  children: React.ReactNode;
  hideSummary: boolean;
}

interface SharePropsContextType {
  hideSummary: boolean;
  disableSubmit: boolean;
  setDisableSubmit: (disableSubmit: boolean) => void;
}

export const SharePropsContext = React.createContext<SharePropsContextType>(
  {} as SharePropsContextType,
);

export const SharePropsProvider = ({ children, hideSummary }: SharePropsContextProvider) => {
  const [disableSubmit, setDisableSubmit] = useState(false);
  const { currentStep } = useReviewFlow();

  useEffect(() => {
    if (typeof currentStep?.name === 'object' && !!currentStep?.name?.postReturnPreviewActor) {
      setDisableSubmit(false);
    }
  }, [currentStep]);

  return (
    <SharePropsContext.Provider value={{ hideSummary, disableSubmit, setDisableSubmit }}>
      {children}
    </SharePropsContext.Provider>
  );
};

export const useSharePropsContext = () => {
  const context = useContext(SharePropsContext);
  return context;
};
