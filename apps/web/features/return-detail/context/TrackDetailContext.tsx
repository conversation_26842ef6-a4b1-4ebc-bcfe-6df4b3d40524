import React, { useContext, useState } from 'react';

export interface TrackDetailInfoProvider {
  children: React.ReactNode;
  refetchReturns?: () => void;
}

export enum TrackModalMode {
  ADD = 'add',
  EDIT = 'edit',
}

interface TrackDetailInfoType {
  returnId: string;
  trackingNumber: string;
  isTrackModalOpen: boolean;
  courierSlug: string;
  trackModalMode: TrackModalMode;
}

interface TrackDetailContextType {
  value: TrackDetailInfoType;
  refetchReturns?: () => void;
  updateContextValue: (value: Partial<TrackDetailInfoType>) => void;
}

export const TrackDetailContext = React.createContext<TrackDetailContextType>(
  {} as TrackDetailContextType,
);

export const TrackDetailProvider = ({ children, refetchReturns }: TrackDetailInfoProvider) => {
  const [value, setValue] = useState<TrackDetailInfoType>({
    returnId: '',
    trackingNumber: '',
    isTrackModalOpen: false,
    courierSlug: '',
    trackModalMode: TrackModalMode.ADD,
  });

  const updateContextValue = (newValue: Partial<TrackDetailInfoType>) => {
    setValue({
      ...value,
      ...newValue,
    });
  };

  return (
    <TrackDetailContext.Provider value={{ value, updateContextValue, refetchReturns }}>
      {children}
    </TrackDetailContext.Provider>
  );
};

export const useTrackDetailContext = () => {
  const context = useContext(TrackDetailContext);
  return context;
};
