import { useEffect } from 'react';

import { RequestErrorType, ShipmentsInfo } from '@aftership/returns-logics-core';
import { useFlow } from 'returns-logics/react';

interface IProps {
  onUpdateQuantitySuccess?: () => void;
  onUpdateQuantityFailed?: (error?: RequestErrorType) => void;
  onPollingShipments?: () => void;
}

// TODO: 感觉这个应该是 headless 层？
// TODO: 这儿有问题，回调不应该是这么做，应该是 provide actions
const useGenerateAdditionalLabel = ({
  onUpdateQuantitySuccess,
  onUpdateQuantityFailed,
  onPollingShipments,
}: IProps) => {
  const { children } = useFlow();

  const addMoreLabelSubFlow = children.returnDetailSubFlow?.children?.generateAdditionalLabel;

  const shipments = addMoreLabelSubFlow?.context?.shipments ?? [];

  const additionalLabels = shipments.reduce((acc, shipment) => {
    return [...acc, ...shipment.additional_labels];
  }, [] as ShipmentsInfo[]);

  const isUpdateQuantitySuccess = addMoreLabelSubFlow?.matches({ submitLabelQuantity: 'success' });

  const isUpdateQuantityFailed = addMoreLabelSubFlow?.matches({ submitLabelQuantity: 'error' });

  const isPollingShipmentStatus = addMoreLabelSubFlow?.matches('pollingShipmentsStatus');

  // FIXME: 这里要改一下，useEffect 去回调不太好
  useEffect(() => {
    if (isUpdateQuantitySuccess) {
      onUpdateQuantitySuccess?.();
    } else if (isUpdateQuantityFailed) {
      onUpdateQuantityFailed?.(addMoreLabelSubFlow?.context?.error);
    } else if (isPollingShipmentStatus) {
      onPollingShipments?.();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isUpdateQuantitySuccess, isPollingShipmentStatus, isUpdateQuantityFailed]);

  const handleUpdateLabelQuantity = (returnId: string, quantity: number) => {
    addMoreLabelSubFlow?.dispatch({
      type: 'updateAdditionalLabelQuantiy',
      data: { returnId, quantity },
    });
  };

  const goToAddMoreLabel = () => {
    children.returnDetailSubFlow?.dispatch({ type: 'ADD_MORE_LABEL' });
  };

  return {
    additionalLabels,
    addMoreLabelSubFlow,
    handleUpdateLabelQuantity,
    isSubmitQuantityLoading: addMoreLabelSubFlow?.matches({ submitLabelQuantity: 'loading' }),
    goToAddMoreLabel,
  };
};

export default useGenerateAdditionalLabel;
