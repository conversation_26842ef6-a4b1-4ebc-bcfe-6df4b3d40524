import { useMemo } from 'react';

import { GenerateLabelStatus, ShipmentsInfo } from '@aftership/returns-logics-core';

const MAXIMUM_CREATED_LABEL_COUNT = 10;

// 获取自动生成的 additional labels 的生成状态
export const useGetAutoGenerateLabelsStatus = (labels: ShipmentsInfo[]) => {
  const postmenLabels = useMemo(
    () => labels.filter((label) => label.retailer_label_source === 'postmen'),
    [labels],
  );

  const { isLabelsCreating, isLabelsCreateFailed, isLabelsAllCreated } = useMemo(() => {
    // 是否有 additional labels 正在生成中
    const isLabelsCreating = postmenLabels.some(
      (label) => label.auto_label_generation_status === GenerateLabelStatus.Creating,
    );
    // 是否有 additional labels 生成失败
    const isLabelsCreateFailed = postmenLabels.some(
      (label) => label.auto_label_generation_status === GenerateLabelStatus.Failed,
    );
    // 是否所有 additional labels 都已生成
    const isLabelsAllCreated =
      postmenLabels.length === 0
        ? false
        : postmenLabels.every(
            (label) => label.auto_label_generation_status === GenerateLabelStatus.Created,
          );

    return { isLabelsCreating, isLabelsCreateFailed, isLabelsAllCreated };
  }, [postmenLabels]);

  // 可以被自动创建的 additional labels 的数量
  const autoCreateLabelsQuota = useMemo(() => {
    // 过滤出自动创建的 labels
    const createdLabels = postmenLabels.filter((label) => {
      return (
        label.auto_label_generation_status === GenerateLabelStatus.Created ||
        label.auto_label_generation_status === GenerateLabelStatus.Creating
      );
    });

    if (createdLabels.length === 0) {
      return MAXIMUM_CREATED_LABEL_COUNT;
    }
    /**
     * 取任意 label，获得 shipment_id，计算同 shipment_id 的 label 数量
     * 后端在自动创建 N 个 additional labels 时，会在每个 shipment 下都创建 N 个 labels
     * 所以只要获取任意 label 的 shipment_id，就可以计算后端已经创建了多少个 labels
     */
    const { return_shipment_id } = createdLabels[0];
    const sameShipmentLabels = createdLabels.filter(
      (label) => label.return_shipment_id === return_shipment_id,
    );
    return MAXIMUM_CREATED_LABEL_COUNT - sameShipmentLabels.length;
  }, [postmenLabels]);

  return {
    isLabelsCreating,
    isLabelsCreateFailed,
    isLabelsAllCreated,
    autoCreateLabelsQuota,
  };
};
