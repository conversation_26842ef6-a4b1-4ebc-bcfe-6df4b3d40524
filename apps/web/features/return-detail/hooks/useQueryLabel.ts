import { useFlow } from 'returns-logics/react';

const useQueryLabel = () => {
  const { children } = useFlow();
  const isPollingShipments = children.returnDetailSubFlow?.matches({
    pollingLabelOrDropoff: {
      pollingShipments: 'polling',
    },
  });

  const isPollingDropoff = children.returnDetailSubFlow?.matches({
    pollingLabelOrDropoff: {
      pollingDropoff: 'polling',
    },
  });

  return {
    isLoading: isPollingShipments || isPollingDropoff,
  };
};

export default useQueryLabel;
