import { useMemo } from 'react';

import { Resolution } from '@aftership/returns-logics-core';
import { useFlow } from 'returns-logics/react';

export const useResolutionInfo = () => {
  const { context } = useFlow();
  const {
    exchange_for_anything_name = '',
    exchange_for_anything_description = '',
    exchange_name = '',
    exchange_description = '',
    refund_to_original_payment_name = '',
    refund_to_original_payment_description = '',
    refund_to_store_credit_name = '',
    refund_to_store_credit_description = '',
    instant_refund_with_refundid_name = '',
    instant_refund_with_refundid_description = '',
  } = context?.storeConfig?.shopInfo ?? {};

  const resolutionNameMap: Record<Resolution, { name: string; description: string }> =
    useMemo(() => {
      return {
        [Resolution.OriginalPayment]: {
          name: refund_to_original_payment_name,
          description: refund_to_original_payment_description,
        },
        [Resolution.StoreCredit]: {
          name: refund_to_store_credit_name,
          description: refund_to_store_credit_description,
        },
        [Resolution.ReplaceTheSameItem]: {
          name: exchange_name,
          description: exchange_description,
        },
        [Resolution.ExchangeForAnything]: {
          name: exchange_for_anything_name,
          description: exchange_for_anything_description,
        },
        [Resolution.Refundid]: {
          name: instant_refund_with_refundid_name,
          description: instant_refund_with_refundid_description,
        },
      };
    }, [
      exchange_for_anything_name,
      exchange_name,
      refund_to_original_payment_name,
      refund_to_store_credit_name,
      instant_refund_with_refundid_name,
      exchange_for_anything_description,
      exchange_description,
      refund_to_original_payment_description,
      refund_to_store_credit_description,
      instant_refund_with_refundid_description,
    ]);

  return resolutionNameMap;
};

export const useTranslateResolutionInfo = () => {
  const resolutionInfo = useResolutionInfo();

  const translatedResolutionInfo = useMemo(() => {
    return Object.entries(resolutionInfo).reduce((pre, [key, value]) => {
      return {
        ...pre,
        [key]: {
          name: value.name,
          description: value.description,
        },
      };
    }, {});
  }, [resolutionInfo]);

  return translatedResolutionInfo as Record<Resolution, { name: string; description: string }>;
};
