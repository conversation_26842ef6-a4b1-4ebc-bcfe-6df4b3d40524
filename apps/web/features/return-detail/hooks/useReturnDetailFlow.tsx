import { useFlow } from 'returns-logics/react';

import useDevice from '@/hooks/useDevice';

export const useReturnDetailFlow = () => {
  const { children, dispatch } = useFlow();
  const isMobile = useDevice().mobile;
  const returnDetailFlowContext = children?.returnDetailSubFlow?.context;
  const isLoading = children?.returnDetailSubFlow?.currentStep?.isLoading;
  const isFetchingDetail = children?.returnDetailSubFlow?.matches({
    getReturn: 'loading',
  });
  const returnDetail = returnDetailFlowContext?.returnDetail;
  const returnMethod = returnDetailFlowContext?.returnMethod;

  const shipments = returnDetailFlowContext?.shipments;

  const isAllShipmentGenerated =
    shipments?.length === returnDetailFlowContext?.shipmentAllocations?.length;

  const dropoffs = returnDetailFlowContext?.dropoffs;
  const couriers = returnDetailFlowContext?.couriers;
  const displayQrCodeSlug = returnDetailFlowContext?.displayQrCodeSlug;

  const returnDetailFlowCurrentStep = children?.returnDetailSubFlow?.currentStep?.name;

  const shippingAddress = returnDetailFlowContext?.returnShippingAddress;
  const contactRecipient = returnDetailFlowContext?.contactRecipient;

  const returnDetailFlowDispatch = children?.returnDetailSubFlow?.dispatch;
  const returnDetailFlowMatches = children?.returnDetailSubFlow?.matches;

  const dropoffLocations = returnDetailFlowContext?.dropoffLocations;
  const nearbyLocations = returnDetailFlowContext?.nearbyLocations;

  return {
    isFetchingDetail,
    isMobile,
    returnDetailFlowContext,
    shippingAddress,
    contactRecipient,
    isLoading,
    returnDetail: returnDetail
      ? {
          ...returnDetail,
          items: returnDetail?.items?.filter(({ return_quantity }) => return_quantity > 0) ?? [],
        }
      : undefined,
    returnMethod,
    shipments: isAllShipmentGenerated ? shipments : [],
    dropoffs,
    displayQrCodeSlug,
    dropoffLocations,
    couriers,
    nearbyLocations,
    returnDetailFlowCurrentStep,
    mainDispatch: dispatch,
    returnDetailFlowDispatch,
    returnDetailFlowMatches,
  };
};
