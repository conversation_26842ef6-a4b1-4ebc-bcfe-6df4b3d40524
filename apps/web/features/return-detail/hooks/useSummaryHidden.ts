import { ExchangeChargeMode, ExchangeMode, GrayFeatureKey } from '@aftership/returns-logics-core';

import useGetGrayFeatureEnabled from '@/hooks/useGetGrayFeatureEnabled';
import { useShopInfo } from '@/hooks/useShopInfo';

export const useSummaryHidden = (isReplace: boolean = false) => {
  const {
    multipleResolutionEnabled,
    hideReplaceSummaryAndPrice = false,
    exchangeMode,
    exchangeRuleItemPriceDifferenceSettlement,
  } = useShopInfo();
  const hidePriceAndSummaryAlways = useGetGrayFeatureEnabled(
    GrayFeatureKey.HiddenSummaryAndPriceAlways,
  );

  const hidePriceAndSummaryOnlyReplace =
    !multipleResolutionEnabled &&
    isReplace &&
    hideReplaceSummaryAndPrice &&
    exchangeMode === ExchangeMode.DifferentPrice &&
    exchangeRuleItemPriceDifferenceSettlement === ExchangeChargeMode.ChargeByOthers;

  // 如果是ReturnMethod 是 replace the same item, 并且属于灰度名单,需要隐藏价格还有 Summary
  return hidePriceAndSummaryAlways || hidePriceAndSummaryOnlyReplace;
};
