import { useCallback, useEffect, useMemo, useRef } from 'react';

import { Box, Stack } from '@aftership/astra';
import { tokenVars } from '@aftership/astra-tokens/Contract.css';
import { Resolution } from '@aftership/returns-logics-core';
import { useFlow } from 'returns-logics/react';

import { ScrollBox } from '@/components/ScrollBox';
import { StepCard } from '@/components/StepCard';
import { useSummaryHidden } from '@/features/return-detail/hooks/useSummaryHidden';
import { useStepCardMinHeight } from '@/hooks/useStepCardMinHeight';
import { PageId } from '@/utils/tracker/consts';
import { useReportPageViewEvent } from '@/utils/tracker/useReportPageViewEvent';

import LeftColumn from './components/LeftColumn';
import { RMATitle, RMATitleFromList, RMATitleFromReview } from './components/RMATitle';
import { ReturnStatus } from './components/ReturnStatus';
import RightColumn from './components/RightColumn';
import { StickyHeader } from './components/StickyHeader';
import { SharePropsProvider } from './context/SharePropsContext';
import { TrackDetailProvider } from './context/TrackDetailContext';
import useCartStyle from './hooks/useCartStyle';
import { useReturnDetailFlow } from './hooks/useReturnDetailFlow';

import { useUniversalRouting } from '../returns/hooks/useUniversalRouting';

const { Space } = tokenVars.Semantic;
const { Space: PrimitiveSpace, Color: PrimitiveColor } = tokenVars.Primitive;

const ReturnDetail = () => {
  const flow = useFlow();
  const { backgroundColor, isMobile, isSpecialDesktopForReturnDetail } = useCartStyle();
  const minHeight = useStepCardMinHeight();

  const { dispatch, currentStep } = flow;
  const containerRef = useRef<HTMLDivElement>(null);

  const {
    returnDetailFlowContext,
    returnDetailFlowDispatch,
    isFetchingDetail,
    returnDetail,
    returnMethod,
    shipments,
    couriers,
    displayQrCodeSlug,
  } = useReturnDetailFlow();
  const router = useUniversalRouting();
  const rmaId = router.getParam('rmaId');
  const isFormReview = router.getSearchParam('isFormReview') === 'true';
  const hideSummary = useSummaryHidden(returnDetail?.resolution === Resolution.ReplaceTheSameItem);

  const financialOutcome =
    returnDetailFlowContext?.returnDetail?.return_preview_summary?.financial_outcome;

  const refetchReturns = () => {
    returnDetailFlowDispatch?.({ type: 'REFETCH_RETURN_DETAIL' });
  };

  const goBackReturnList = useCallback(() => {
    dispatch({ type: 'GO_TO_RETURN_LIST' });
  }, [dispatch]);

  const requestAnotherReturn = () => {
    dispatch({ type: 'CREATE_RETURN' });
  };

  useEffect(() => {
    // 防止直接修改 URL 导致后不能正常工作
    if (rmaId && rmaId !== returnDetailFlowContext?.rmaId) {
      returnDetailFlowDispatch?.({ type: 'SET_RMA_ID', data: { rmaId } });
    }
  }, [rmaId, returnDetailFlowContext?.rmaId, returnDetailFlowDispatch]);

  const returnStatusMemoComp = useMemo(
    () => (
      <ReturnStatus
        returnDetail={returnDetail}
        returnMethod={returnMethod}
        shipments={shipments}
        isMobile={true}
        isLoading={isFetchingDetail}
        displayQrCodeSlug={displayQrCodeSlug}
      />
    ),
    [isFetchingDetail, returnDetail, returnMethod, shipments, displayQrCodeSlug],
  );

  useReportPageViewEvent(PageId.returnDetail);

  if (isMobile) {
    return (
      <Box backgroundColor={backgroundColor} minHeight={'100vh'} overflow='auto'>
        <StickyHeader
          style={{ backgroundColor: PrimitiveColor.Gray[100] }}
          scrollContainerRef={containerRef}
          onBack={goBackReturnList}
        />
        <RMATitle
          rmaId={returnDetail?.rma_id || rmaId}
          rmaCreateAt={returnDetail?.created_at}
          isFromReview={isFormReview}
          requestAnotherReturn={requestAnotherReturn}
          isMobile={true}
        />
        <SharePropsProvider hideSummary={hideSummary}>
          <TrackDetailProvider refetchReturns={refetchReturns}>
            <Stack direction='column' gap='2xl'>
              <Box paddingX={Space.M} marginTop={Space['2Xl']}>
                {returnStatusMemoComp}
              </Box>

              <LeftColumn
                returnDetail={returnDetail}
                returnMethod={returnMethod}
                shipments={shipments}
                couriers={couriers}
              />
              <RightColumn returnDetail={returnDetail} financialOutcome={financialOutcome} />
            </Stack>
          </TrackDetailProvider>
        </SharePropsProvider>
      </Box>
    );
  }
  const rmaTitle = returnDetail?.rma_id
    ? `RMA #${returnDetail.rma_id}`
    : rmaId
      ? `RMA #${rmaId}`
      : '';
  return (
    currentStep.name === 'returnDetail' && (
      <StepCard
        title={
          !isFormReview ? (
            <RMATitleFromList rmaTitle={rmaTitle} rmaCreateAt={returnDetail?.created_at} />
          ) : (
            <></>
          )
        }
        style={{
          minHeight: isSpecialDesktopForReturnDetail ? minHeight : void 0,
          borderRadius: isSpecialDesktopForReturnDetail ? '0px' : void 0,
        }}
        onBack={goBackReturnList}
        isFullWidth={isSpecialDesktopForReturnDetail || hideSummary}
      >
        {isFormReview && (
          <RMATitleFromReview rmaTitle={rmaTitle} requestAnotherReturn={requestAnotherReturn} />
        )}
        {isSpecialDesktopForReturnDetail ? (
          <ScrollBox>
            <Box
              paddingTop={Space['2Xl']}
              paddingX={0}
              paddingBottom={Space['3Xl']}
              overflow='auto'
            >
              <Stack
                gap='none'
                direction={hideSummary ? 'column' : 'row'}
                style={{ padding: Space['2Xl'] }}
              >
                <SharePropsProvider hideSummary={hideSummary}>
                  <TrackDetailProvider refetchReturns={refetchReturns}>
                    <Stack
                      gap='xl'
                      direction='column'
                      flex={!hideSummary ? '0 0 60%' : undefined}
                      style={{ paddingInlineEnd: Space.S }}
                      align={hideSummary ? 'center' : 'start'}
                    >
                      {returnStatusMemoComp}
                      <LeftColumn
                        returnDetail={returnDetail}
                        returnMethod={returnMethod}
                        shipments={shipments}
                        couriers={couriers}
                      />
                    </Stack>

                    <Box flex={!hideSummary && '0 0 40%'} paddingStart={Space.S}>
                      <RightColumn
                        returnDetail={returnDetail}
                        financialOutcome={financialOutcome}
                      />
                    </Box>
                  </TrackDetailProvider>
                </SharePropsProvider>
              </Stack>
            </Box>
          </ScrollBox>
        ) : (
          <Box
            paddingTop={Space['2Xl']}
            paddingX={hideSummary ? Space['3Xl'] : PrimitiveSpace[1200]}
            paddingBottom={Space['3Xl']}
          >
            <SharePropsProvider hideSummary={hideSummary}>
              <Stack direction={hideSummary ? 'column' : 'row'} gap='xl'>
                <TrackDetailProvider refetchReturns={refetchReturns}>
                  <Stack direction='column' gap='xl'>
                    {returnStatusMemoComp}
                    <LeftColumn
                      returnDetail={returnDetail}
                      returnMethod={returnMethod}
                      shipments={shipments}
                      couriers={couriers}
                    />
                  </Stack>
                </TrackDetailProvider>
                <RightColumn returnDetail={returnDetail} financialOutcome={financialOutcome} />
              </Stack>
            </SharePropsProvider>
          </Box>
        )}
      </StepCard>
    )
  );
};

export default ReturnDetail;
