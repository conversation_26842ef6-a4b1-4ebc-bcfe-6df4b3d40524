import { ReturnShippingAddress } from '@aftership/returns-logics-core';

export const getFullShippingAddress = (
  shippingAddress?: Omit<ReturnShippingAddress, 'firstName' | 'lastName'> | null,
) => {
  if (!shippingAddress) {
    return '';
  }

  const { addressLine1, addressLine2, city, state, postalCode, country } =
    shippingAddress || ({} as ReturnShippingAddress);

  const fullShippingAddress = [addressLine1, addressLine2, city, state, postalCode, country]
    .filter(Boolean)
    .join(', ');

  return fullShippingAddress;
};

/**
 *  判断是否需要显示 QRCode
 */
export function isQRCodeEnable(courierSlug?: string, labelQrCode?: null | string) {
  // 有 QrCode 信息
  return labelQrCode && courierSlug;
}
