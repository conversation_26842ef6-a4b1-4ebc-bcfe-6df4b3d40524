import { t } from 'i18next';

import { ReturnPreviewSummary, SummaryTotalType } from '@aftership/returns-logics-core';

import { toCurrency } from '@/utils/price';

export const getSummaryLabel = (type: SummaryTotalType) =>
  ({
    [SummaryTotalType.Refund]: t('page.description.totalRefund'),
    [SummaryTotalType.Upsell]: t('page.description.totalPay'),
    [SummaryTotalType.Unchanged]: t('resolution.description.subtotal'),
  })[type] || undefined;

export const getTotalValue = (summary?: ReturnPreviewSummary) => {
  const financialOutcome = summary?.financial_outcome;
  const checkoutTotal = summary?.checkout_total_set;
  const refundTotal = summary?.refund_total_set;

  if (financialOutcome === SummaryTotalType.Upsell) {
    return toCurrency(
      checkoutTotal?.presentment_money?.amount ?? '',
      checkoutTotal?.presentment_money?.currency,
      true,
    );
  } else if (financialOutcome === SummaryTotalType.Refund) {
    return toCurrency(
      refundTotal?.presentment_money?.amount ?? '',
      refundTotal?.presentment_money?.currency ?? '',
      true,
    );
  } else if (financialOutcome === SummaryTotalType.Unchanged) {
    // TODO 需要和 PO 确认是否展示 0
    return toCurrency('0', checkoutTotal?.presentment_money?.currency, true);
  }
  return null;
};
