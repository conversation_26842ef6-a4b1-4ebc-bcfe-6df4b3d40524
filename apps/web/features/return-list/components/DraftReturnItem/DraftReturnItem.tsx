import dayjs from 'dayjs';
import { useTranslation } from 'react-i18next';

import { Box, ProgressBar, Stack, Typography } from '@aftership/astra';
import { tokenVars } from '@aftership/astra-tokens/Contract.css';
import { IDraftReturn } from '@aftership/returns-logics-core';

import { Button } from '@/components/Button';
import ImageList from '@/components/ImageList';
import { PayNow } from '@/features/return-list/components/PayNow';
import useDevice from '@/hooks/useDevice';

import CountDown from '../../../../components/CountDown/CountDown.tsx';
import { useReturnListSubFlow } from '../../hooks/useReturnListSubFlow';

const { Space } = tokenVars.Semantic;

interface ReturnItemProps {
  item: IDraftReturn;
}

const DraftReturnItem = ({ item }: ReturnItemProps) => {
  const isMobile = useDevice().mobile;
  const { t } = useTranslation();
  const { dispatch, matches } = useReturnListSubFlow() || {};

  const isDeleteLoading = matches?.({ deleteDraftReturn: 'loading' });
  const isPullingLoading =
    matches?.('pollingGetDraftReturn') ||
    matches?.({ loadAllReturns: { getDraftReturns: 'loading' } });
  const covers = item.items.map((item) => item?.image?.src || '');

  const handleCancel = () => {
    dispatch?.({ type: 'DELETE_DRAFT_RETURN', data: { draftId: item.id } });
  };
  const handleExpiredConfirm = () => {
    dispatch?.({ type: 'RELOAD_RETURNS' });
  };
  const cancelDate = dayjs(item.created_at).add(10, 'minute').toDate();

  return (
    <Stack direction='column'>
      <Box paddingBottom={isMobile ? Space.L : Space.S}>
        <ImageList list={covers} />
      </Box>
      <Typography variant='bodySm' style={{ paddingBottom: Space['2Xs'] }} color='secondary'>
        {t('v2.date_formate.create_at', {
          date: dayjs(item.created_at).format('MMM DD, YYYY'),
        })}
      </Typography>
      <Stack direction='column' gap='2xs'>
        <CountDown targetDate={cancelDate} />
        <Typography variant={isMobile ? 'bodyLgSemibold' : 'heading2Xs'} color='primary'>
          {t('page.error.paymentInProgress')}
        </Typography>
        <Typography
          variant='bodyMd'
          color={isMobile ? 'secondary' : 'primary'}
          style={{ paddingBottom: Space.Xs }}
        >
          {t('page.description.creatingReturns')}
        </Typography>
      </Stack>
      <Box paddingBottom={isMobile ? Space.L : Space.S}>
        <ProgressBar showPoint showLabel={false} value={0} maxValue={1} />
      </Box>
      <Stack gap='m'>
        <PayNow
          draftInfo={item}
          isLoading={isPullingLoading}
          isDisabled={isDeleteLoading}
          onExpiredConfirmCallback={handleExpiredConfirm}
        />
        <Button
          variant='basic'
          color='error'
          onPress={handleCancel}
          isLoading={isDeleteLoading}
          isDisabled={isPullingLoading}
        >
          {t('v2.returns_list.draft_cancel')}
        </Button>
      </Stack>
    </Stack>
  );
};

export default DraftReturnItem;
