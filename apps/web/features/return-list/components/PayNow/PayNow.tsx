import dayjs from 'dayjs';
import { useCallback, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { IDraftReturn, StripePaymentStatus } from '@aftership/returns-logics-core';

import { Button } from '@/components/Button';
import ExpiredModal from '@/features/review/components/ExpiredModal';
import { StripeProvider } from '@/features/review/components/PaymentModal/components/StripeModal/StripeProvider';

import StripePayNowModal from './StripePayNowModal';

export interface PayNowProps {
  draftInfo: IDraftReturn;
  onExpiredConfirmCallback?: VoidFunction;
  isDisabled?: boolean;
  isLoading?: boolean;
}
const isExpired = (draftInfo: IDraftReturn) => {
  const cancelDate = dayjs(draftInfo.created_at).add(10, 'minute');
  return dayjs().isAfter(cancelDate);
};

const PayNow = ({ isDisabled, isLoading, draftInfo, onExpiredConfirmCallback }: PayNowProps) => {
  const { t } = useTranslation();

  const [payModalVisible, setPayModalVisible] = useState(false);
  const [expiredModalVisible, setExpiredModalVisible] = useState(false);
  const isStripePay = !draftInfo.checkout_url;
  const isShopifyPay = !!draftInfo.checkout_url;

  const handleClosePayModal = useCallback(() => {
    setPayModalVisible(false);
  }, []);

  if (!isShopifyPay && !isStripePay) {
    return null;
  }

  const handlePayNow = async () => {
    if (isExpired(draftInfo)) {
      setExpiredModalVisible(true);
    } else if (isShopifyPay) {
      window.open(draftInfo.checkout_url, '_blank');
    } else if (isStripePay) {
      setPayModalVisible(true);
    }
  };

  return (
    <>
      <ExpiredModal
        isOpen={expiredModalVisible}
        onClose={() => {
          setExpiredModalVisible(false);
          onExpiredConfirmCallback?.();
        }}
      />
      {isShopifyPay && (
        <Button onPress={handlePayNow} isDisabled={isDisabled} isLoading={isLoading}>
          {t('page.request.payNow')}
        </Button>
      )}

      {isStripePay && (
        <>
          <Button onPress={handlePayNow} isDisabled={isDisabled} isLoading={isLoading}>
            {draftInfo.payment_status === StripePaymentStatus.FAILED
              ? t('popup.request.tryAgain')
              : t('page.request.payNow')}
          </Button>
          <StripeProvider
            stripeAccount={draftInfo.payment_app_key}
            clientSecret={draftInfo.payment_client_token}
          >
            <StripePayNowModal
              isOpen={payModalVisible}
              onClose={handleClosePayModal}
              draftReturnItem={draftInfo}
            />
          </StripeProvider>
        </>
      )}
    </>
  );
};

export default PayNow;
