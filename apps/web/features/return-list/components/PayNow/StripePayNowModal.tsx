import clsx from 'clsx';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Box, Stack } from '@aftership/astra';
import { tokenVars } from '@aftership/astra-tokens/Contract.css';
import { IDraftReturn } from '@aftership/returns-logics-core';

import { Button } from '@/components/Button';
import { Modal } from '@/components/Modal';
import { SummaryProvider } from '@/features/return-detail/components/Summary/SummaryProvider';
import StripeErrorModal from '@/features/review/components/ErrorModals/StripeErrorModal';
import { StripeInProgressModal } from '@/features/review/components/InProgressModals';
import CheckoutForm from '@/features/review/components/PaymentModal/components/StripeModal/CheckoutForm';
import {
  stripeBodyStyles,
  stripeDesktopStyles,
} from '@/features/review/components/PaymentModal/components/StripeModal/StripeModal.css';
import { useStripeCheckout } from '@/features/review/components/PaymentModal/components/StripeModal/StripeProvider';
import { modalScrollStyles } from '@/features/review/components/PaymentModal/styles/modalStyle.css';
import useDevice from '@/hooks/useDevice';
import { useReturnListFlow } from '@/hooks/useReturnListFlow';

import PayNowSummary from './PayNowSummary';

import usePayNow from '../../hooks/usePayNow';

const { Space } = tokenVars.Semantic;

interface StripePayNowModalProps {
  isOpen: boolean;
  onClose: VoidFunction;
  draftReturnItem: IDraftReturn;
}

const StripePayNowModal = ({ isOpen, draftReturnItem, onClose }: StripePayNowModalProps) => {
  const isMobile = useDevice().mobile;
  const { t } = useTranslation();
  const { onPayContinue } = usePayNow({ draftId: draftReturnItem.id });
  const { isPollingDraftReturn } = useReturnListFlow();
  const { isLoading, isReady, submit } = useStripeCheckout();
  const [errorModalVisible, setErrorModalVisible] = useState(false);
  const [errorMsg, setErrorMsg] = useState('');

  const onPayNow = () => {
    submit?.({
      onSuccess: () => {
        onPayContinue();
        onClose();
      },
      onError: (error: string) => {
        if (error !== 'validation_error') {
          setErrorModalVisible(true);
          setErrorMsg(error);
        }
      },
    });
  };

  if (isPollingDraftReturn) {
    return (
      <StripeInProgressModal
        isOpen={isPollingDraftReturn}
        onCancel={onClose}
        title={t('page.error.paymentInProgress')}
        message={t('popup.description.processingStripePayment')}
      />
    );
  }

  if (errorModalVisible) {
    return (
      <StripeErrorModal
        isOpen={errorModalVisible}
        errorMsg={errorMsg}
        onClose={() => {
          onClose();
          setErrorModalVisible(false);
        }}
      />
    );
  }

  return (
    <Modal
      isOpen={isOpen}
      title={t('popup.description.payForReturnRequest')}
      className={modalScrollStyles}
      onClose={onClose}
    >
      <Stack direction='column' gap='2xs'>
        <SummaryProvider
          isFold={false}
          previewSummary={draftReturnItem?.return_preview_summary}
          haveExchangeItems={Boolean(
            draftReturnItem?.return_preview_summary?.exchange_items?.length,
          )}
        >
          <PayNowSummary />
        </SummaryProvider>

        <Box className={clsx([stripeBodyStyles, { [stripeDesktopStyles]: !isMobile }])}>
          <CheckoutForm />
        </Box>
      </Stack>

      <Stack direction='row' justify='end' style={{ marginTop: Space['2Xl'] }}>
        <Button
          onPress={onPayNow}
          isLoading={isLoading}
          isDisabled={!isReady}
          isFullWidth={isMobile}
        >
          {t('page.request.payNow')}
        </Button>
      </Stack>
    </Modal>
  );
};

export default StripePayNowModal;
