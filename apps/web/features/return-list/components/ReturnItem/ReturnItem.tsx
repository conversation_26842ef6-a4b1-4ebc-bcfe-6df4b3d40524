import { Box, Icon, Pressable, ProgressBar, Stack, Typography } from '@aftership/astra';
import { ChevronRightOutlined } from '@aftership/astra-icons';
import { tokenVars } from '@aftership/astra-tokens/Contract.css';
import { usePreviewContext } from '@aftership/preview-kit/client';
import { ReturnItemType } from '@aftership/returns-logics-core';
import { useMainFlowContext } from 'returns-logics/react';

import ImageList from '@/components/ImageList';
import { useRefundStatus } from '@/features/return-list/hooks/useRefundStatus';
import { useReturnTitle } from '@/features/return-list/hooks/useReturnTitle';
import { getCurrentProgress, getTotalProgress } from '@/features/return-list/utils/getProgress';
import useDevice from '@/hooks/useDevice';

const { Space, Radius, Color } = tokenVars.Semantic;

interface ReturnItemProps {
  item: ReturnItemType;
}

const ReturnItem = ({ item }: ReturnItemProps) => {
  const isMobile = useDevice().mobile;
  const mainFlowRef = useMainFlowContext();
  const covers = item.items.map((item) => item?.image?.src);

  const title = useReturnTitle({
    returnStatus: item.status,
    shippingStatus: item.shipping_status || undefined,
    returnMethodSlug: item.return_method_slug,
    dropoffStatus: item.dropoff_status,
  });
  const totalProgress = getTotalProgress(item.return_method_slug);
  const currentProgress = getCurrentProgress({
    returnStatus: item.status,
    shippingStatus: item.shipping_status || undefined,
    returnMethodSlug: item.return_method_slug,
    dropoffStatus: item.dropoff_status,
  });
  const { label: refundLabel, hidden: hiddenRefundLabel } = useRefundStatus({
    resolution: item.refunds?.[0]?.destination,
    amount: item.refunds?.[0]?.refunded_total?.amount,
    currency: item.refunds?.[0]?.refunded_total?.currency,
    isRefunded: item.refunded || false,
  });

  const handleDetail = () => {
    mainFlowRef.send({ type: 'GO_TO_RETURN_DETAIL', data: { rmaId: item.rma_id } });
  };

  const { isPreview } = usePreviewContext();

  return (
    <Pressable onPress={handleDetail} isDisabled={isPreview}>
      <Stack direction='column'>
        <Stack
          gap='xs'
          justify='space-between'
          align={isMobile ? 'center' : 'start'}
          style={{ paddingBottom: Space.S }}
        >
          <ImageList list={covers} />
          <Icon
            color='secondary'
            source={ChevronRightOutlined}
            size={isMobile ? Space.Xl : Space.M}
          />
        </Stack>
        <Typography variant='bodySm' color='secondary' style={{ paddingBottom: Space['2Xs'] }}>
          RMA #{item.rma_id}
        </Typography>
        {!hiddenRefundLabel && (
          <Box
            alignSelf='start'
            padding={isMobile ? `${Space['2Xs']} ${Space.S}` : `${Space['3Xs']} ${Space.M}`}
            marginBottom={Space.Xs}
            backgroundColor={Color.Bg.Body_Secondary}
            borderRadius={Radius.M}
          >
            <Typography variant='bodyMd' color='primary'>
              {refundLabel}
            </Typography>
          </Box>
        )}
        <Box marginBottom={Space.Xs}>
          <Typography variant={isMobile ? 'bodyLgSemibold' : 'heading2Xs'} color='primary'>
            {title}
          </Typography>
        </Box>
        <ProgressBar showPoint showLabel={false} value={currentProgress} maxValue={totalProgress} />
      </Stack>
    </Pressable>
  );
};

export default ReturnItem;
