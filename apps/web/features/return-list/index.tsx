import dayjs from 'dayjs';
import { useEffect, useMemo } from 'react';
import { useTranslation } from 'react-i18next';

import { Box, Spinner, Stack, Typography, useToast } from '@aftership/astra';
import { semanticTokenVar } from '@aftership/astra-tokens/semantic-token/Contract.css';
import { ErrorCode, GrayFeatureKey } from '@aftership/returns-logics-core';

import { Button } from '@/components/Button';
import { EllipsisText } from '@/components/EllipsisText';
import { ListBox, ListBoxItem } from '@/components/ListBox';
import { ScrollBox } from '@/components/ScrollBox';
import { StepCard } from '@/components/StepCard';
import { ReturnItem } from '@/features/return-list/components/ReturnItem';
import { isDraftReturnItem } from '@/features/return-list/utils/isDraftReturn';
import useDevice from '@/hooks/useDevice';
import useGetGrayFeatureEnabled from '@/hooks/useGetGrayFeatureEnabled';
import { useMainFlow } from '@/hooks/useMainFlow';
import { useStepCardMinHeight } from '@/hooks/useStepCardMinHeight';
import getTrackerInstance from '@/utils/tracker';
import { EventName, PageId } from '@/utils/tracker/consts';
import { useReportPageViewEvent } from '@/utils/tracker/useReportPageViewEvent';

import { DraftReturnItem } from './components/DraftReturnItem';
import { useReturnListSubFlow } from './hooks/useReturnListSubFlow';

import { ReturnListCreateReturnButton } from '../preview/components/WithPreviewSection';
import { useUniversalRouting } from '../returns/hooks/useUniversalRouting';

const { Color, Space } = semanticTokenVar;

export default function ReturnList() {
  const isMobile = useDevice().mobile;
  const router = useUniversalRouting();
  const toast = useToast();
  const mainFlow = useMainFlow();
  const returnListSubFlow = useReturnListSubFlow();
  const { t } = useTranslation();
  const { context, currentStep } = returnListSubFlow || {};
  const {
    returns = [],
    draftReturns = [],
    orderName,
    orderNumber,
    createTime,
    deleteDraftReturnError,
  } = context || {};

  const minHeight = useStepCardMinHeight();
  const showExternalOrder = useGetGrayFeatureEnabled(GrayFeatureKey.ShowExternalOrder);
  const displayOrderNumberOrName = useMemo(
    () => (showExternalOrder ? orderName : orderNumber),
    [showExternalOrder, orderName, orderNumber],
  );
  const returnList = useMemo(() => [...draftReturns, ...returns], [returns, draftReturns]);

  const handleCreate = () => {
    mainFlow.dispatch?.({ type: 'CREATE_RETURN' });
    getTrackerInstance().reportClickEvent({
      eventName: EventName.clickCreateReturnInReturnList,
    });
  };

  useReportPageViewEvent(PageId.returnList);

  useEffect(() => {
    router.beforePopState(() => {
      mainFlow.dispatch?.({ type: 'GO_TO_ORDER_LOOKUP' });
      return false;
    });
    return () => router.beforePopState(() => true);
  }, [mainFlow, router]);

  // 删除仅有一个点 draft return 时，需要跳转至 item selection
  // 这里 memo 了 returnList 的 flow 数据，所以这里需要拿 mainFlow 的 currentStep.name 判断
  useEffect(() => {
    if (
      mainFlow.currentStep.name === 'returnList' &&
      !currentStep?.isLoading &&
      returns.length <= 0 &&
      draftReturns.length <= 0
    ) {
      mainFlow.dispatch({ type: 'CREATE_RETURN' });
    }
  }, [currentStep, draftReturns.length, mainFlow, returns.length, router]);

  useEffect(() => {
    if (deleteDraftReturnError) {
      deleteDraftReturnError.code === ErrorCode.CancelConfirmedDraftReturn &&
        toast.error(t('v2.toast.cancel_confirmed_draft_return'));
      returnListSubFlow?.dispatch?.({
        type: 'RESET_ERROR',
        data: { type: 'deleteDraftReturnError' },
      });
    }
  }, [deleteDraftReturnError, returnListSubFlow, t, toast]);

  if (isMobile) {
    return (
      <Box flex={1} height={0} style={{ backgroundColor: Color.Bg.Body }}>
        <ScrollBox height='100%' paddingX={Space.M} paddingTop={Space['2Xl']}>
          <Stack style={{ minHeight: '100%' }} gap='2xl' direction='column'>
            <Stack gap='m' align='center' justify='space-between'>
              <Stack direction='column'>
                <EllipsisText
                  variant='heading2Xs'
                  color='primary'
                  style={{ whiteSpace: 'nowrap' }}
                  text={`${t('page.details.order')} ${displayOrderNumberOrName}`}
                />
                <Typography variant='bodySm' color='secondary' style={{ whiteSpace: 'nowrap' }}>
                  {dayjs(createTime).format(
                    t('v2.date_formate.at', { date: 'MMM DD, YYYY', time: 'hh:mm a' }),
                  )}
                </Typography>
              </Stack>
              <Button variant='basic' color='primary' onPress={handleCreate}>
                {t('page.request.create')}
              </Button>
            </Stack>
            {currentStep?.isLoading && draftReturns.length <= 0 && returns.length <= 0 ? (
              <Stack flex={1} align='center' justify='center'>
                <Spinner />
              </Stack>
            ) : (
              <Box style={{ paddingBottom: Space['2Xl'] }}>
                <ListBox rowGap={Space.L} items={returnList}>
                  {(item) => {
                    return (
                      <ListBoxItem style={{ padding: Space.M }}>
                        {() => {
                          if (isDraftReturnItem(item)) {
                            return <DraftReturnItem item={item} />;
                          }
                          return <ReturnItem item={item} />;
                        }}
                      </ListBoxItem>
                    );
                  }}
                </ListBox>
              </Box>
            )}
          </Stack>
        </ScrollBox>
      </Box>
    );
  }

  return (
    <StepCard
      width={800}
      style={{ width: '800px', minHeight }}
      headerContainerAlignItems='start'
      title={
        <Stack direction='column' align='center'>
          <Typography variant='heading2Xs' color='primary' style={{ whiteSpace: 'nowrap' }}>
            {t('page.details.order')} {displayOrderNumberOrName}
          </Typography>
          <Typography variant='bodySm' color='secondary' style={{ whiteSpace: 'nowrap' }}>
            {dayjs(createTime).format(
              t('v2.date_formate.at', { date: 'MMM DD, YYYY', time: 'hh:mm a' }),
            )}
          </Typography>
        </Stack>
      }
      rightSlot={
        <ReturnListCreateReturnButton onPress={handleCreate} style={{ textAlign: 'right' }}>
          <Typography variant='heading2Xs' style={{ position: 'relative' }}>
            {t('page.request.create')}
          </Typography>
        </ReturnListCreateReturnButton>
      }
      onBack={() => mainFlow.dispatch?.({ type: 'GO_TO_ORDER_LOOKUP' })}
    >
      {currentStep?.isLoading && draftReturns.length <= 0 && returns.length <= 0 ? (
        <Stack flex={1} align='center' justify='center'>
          <Spinner />
        </Stack>
      ) : (
        <Box
          style={{ marginTop: Space.L, paddingInline: Space['3Xl'], paddingBottom: Space['3Xl'] }}
        >
          <ListBox rowGap={Space.L} items={returnList}>
            {(item) => {
              return (
                <ListBoxItem style={{ padding: Space.M }}>
                  {() => {
                    if (isDraftReturnItem(item)) {
                      return <DraftReturnItem item={item} />;
                    }
                    return <ReturnItem item={item} />;
                  }}
                </ListBoxItem>
              );
            }}
          </ListBox>
        </Box>
      )}
    </StepCard>
  );
}
