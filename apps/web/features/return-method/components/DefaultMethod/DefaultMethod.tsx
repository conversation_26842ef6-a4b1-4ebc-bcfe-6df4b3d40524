import Image from 'next/image';

import { Icon, Stack, Typography } from '@aftership/astra';
import { InfoFilled } from '@aftership/astra-icons';
import { primitiveTokenVar } from '@aftership/astra-tokens/primitive-token/Contract.css';
import { semanticTokenVar } from '@aftership/astra-tokens/semantic-token/Contract.css';
import { ReturnMethodSlug } from '@aftership/returns-logics-core';

import { Divider } from '@/components/Divider';
import useDevice from '@/hooks/useDevice';

import { wrapperDisabledClassName } from './DefaultMethod.css';

interface DefaultMethodProps {
  slug: ReturnMethodSlug;
  title?: React.ReactNode;
  description?: React.ReactNode;
  disabled?: boolean;
  disableText?: React.ReactNode;
  children?: React.ReactNode;
}

const iconMap: Record<ReturnMethodSlug, string> = {
  [ReturnMethodSlug.CustomerCourier]: require('@/assets/customer-courier.png').default.src,
  [ReturnMethodSlug.InStore]: require('@/assets/in-store.png').default.src,
  [ReturnMethodSlug.RetailerLabel]: require('@/assets/retailer-label.png').default.src,
  [ReturnMethodSlug.GreenReturn]: require('@/assets/green-return.png').default.src,
  [ReturnMethodSlug.HappyReturns]: require('@/assets/happy-return.png').default.src,
  [ReturnMethodSlug.RetailRework]: require('@/assets/retail-rework.png').default.src,
  [ReturnMethodSlug.CarrierPickup]: require('@/assets/carrier-pickup.png').default.src,
  [ReturnMethodSlug.CarrierDropoff]: require('@/assets/carrier-dropoff.png').default.src,
};

const { Space: PrimitiveSpace } = primitiveTokenVar;
const { Space } = semanticTokenVar;

const DefaultMethod = ({
  slug,
  title = '',
  description = '',
  disabled = false,
  disableText = '',
  children,
}: DefaultMethodProps) => {
  const isMobile = useDevice().mobile;

  return (
    <Stack direction='column'>
      <Stack gap={isMobile ? 'xs' : 'm'} className={disabled ? wrapperDisabledClassName : ''}>
        {!isMobile && (
          <Image width={52} height={52} src={iconMap[slug]} alt={slug} style={{ height: '52px' }} />
        )}
        <Stack flex={1} direction='column' gap='m'>
          <Stack direction='column' gap='2xs'>
            <Stack gap='xs'>
              {isMobile && (
                <Image
                  width={32}
                  height={32}
                  src={iconMap[slug]}
                  style={{ alignSelf: 'center', height: '32px' }}
                  alt={slug}
                />
              )}
              {typeof title === 'string' ? (
                <Typography
                  style={{ flex: 1, alignSelf: 'center', paddingInlineEnd: PrimitiveSpace[1200] }}
                  variant='bodyLgSemibold'
                  color='primary'
                >
                  {title}
                </Typography>
              ) : (
                title
              )}
            </Stack>

            {typeof description === 'string' ? (
              <Typography variant='bodyMd' color='secondary'>
                {description}
              </Typography>
            ) : (
              description
            )}
            {children}
          </Stack>
        </Stack>
      </Stack>
      {disabled && disableText && (
        <>
          <Divider spacing={Space.S} />
          <Stack gap='xs'>
            <Stack style={{ height: Space.L }} align='center'>
              <Icon source={InfoFilled} size={Space.M} />
            </Stack>
            {typeof disableText === 'string' ? (
              <Typography variant='bodyMd' color='secondary'>
                {disableText}
              </Typography>
            ) : (
              disableText
            )}
          </Stack>
        </>
      )}
    </Stack>
  );
};

export default DefaultMethod;
