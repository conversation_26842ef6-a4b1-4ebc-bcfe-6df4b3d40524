import { useTranslation } from 'react-i18next';

import { Icon, Stack, Typography } from '@aftership/astra';
import { LocationOutlined } from '@aftership/astra-icons';
import { semanticTokenVar } from '@aftership/astra-tokens/semantic-token/Contract.css';
import { DistanceUnit, DropoffLocationItem } from '@aftership/returns-logics-core';

import { LocationItem } from '@/features/return-method/components/LocationItem';

const { Space } = semanticTokenVar;

interface DropoffLocationsProps {
  title?: React.ReactNode;
  locations?: DropoffLocationItem[];
  children?: React.ReactNode;
  count?: number;
  showMore?: boolean;
}

const DropoffLocations = ({
  title,
  locations = [],
  children,
  count = 3,
  showMore = true,
}: DropoffLocationsProps) => {
  const { t } = useTranslation();

  return (
    <Stack direction='column' gap='s'>
      {locations.length > 0 && (
        <Stack direction='column' gap='2xs'>
          {typeof title === 'string' ? (
            <Typography variant='bodyMdSemibold' color='primary'>
              {title}
            </Typography>
          ) : (
            title
          )}
          <Stack align='center' gap='2xs'>
            <Icon source={LocationOutlined} size={Space.M} color='secondary' />
            <Typography variant='bodySm' color='secondary'>
              {t('page.happyReturn.baseShippingAddress')}
            </Typography>
          </Stack>
        </Stack>
      )}
      {locations
        ?.slice(0, count)
        ?.map((location) => (
          <LocationItem
            key={location.name}
            location={location}
            distanceUnit={DistanceUnit.MI}
            showMore={showMore}
          />
        )) ?? []}
      {children}
    </Stack>
  );
};

export default DropoffLocations;
