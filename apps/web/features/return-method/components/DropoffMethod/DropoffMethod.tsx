import { TFunction } from 'i18next';
import { Trans } from 'react-i18next';
import { useTranslation } from 'react-i18next';

import { Link, Stack, Typography } from '@aftership/astra';
import { semanticTokenVar } from '@aftership/astra-tokens/semantic-token/Contract.css';
import { DropoffLocation, LngLat, ReturnMethodSlug } from '@aftership/returns-logics-core';

import { Divider } from '@/components/Divider';
import { DefaultMethod } from '@/features/return-method/components/DefaultMethod';
import { DropoffLocations } from '@/features/return-method/components/DropoffLocations';
import { ViewHappyReturnLocations } from '@/features/return-method/components/ViewHappyReturnLocations';
import { ViewRetailReworkLocations } from '@/features/return-method/components/ViewRetailReworkLocations';
import { HAPPY_RETURN_PRIVACY_POLICY_LINK } from '@/features/return-method/utils/constants';

const { Space } = semanticTokenVar;

const disableTextMapping = (t: TFunction) => ({
  [ReturnMethodSlug.HappyReturns]: t('v2.return_method.happy_return.unavailable'),
  [ReturnMethodSlug.RetailRework]: t('v2.return_method.retail_rework.unavailable'),
});
const dropoffRecommendsTitleMapping = (t: TFunction) => ({
  [ReturnMethodSlug.HappyReturns]: t('page.happyReturn.recommends.title'),
  [ReturnMethodSlug.RetailRework]: t('recommendation.locations'),
});

interface DropoffMethodProps {
  isSelected?: boolean;
  slug: ReturnMethodSlug.HappyReturns | ReturnMethodSlug.RetailRework;
  title?: string;
  description?: string;
  disabled?: boolean;
  location?: DropoffLocation;
  nearbyLocations?: DropoffLocation;
  isNearbyLoading?: boolean;
  onNearByLocations?: (latitude: LngLat['latitude'], longitude: LngLat['longitude']) => void;
}

const DropoffMethod = ({
  isSelected,
  slug,
  title = '',
  description,
  disabled = false,
  location = { link: '', locations: [] },
  nearbyLocations,
  isNearbyLoading = false,
  onNearByLocations,
}: DropoffMethodProps) => {
  const { t } = useTranslation();

  return (
    <DefaultMethod
      slug={slug}
      title={title}
      disabled={disabled}
      disableText={disableTextMapping(t)[slug]}
      description={
        <Stack direction='column' gap='2xs' style={{ paddingBlockStart: Space['2Xs'] }}>
          <Typography variant='bodySm' color='secondary'>
            {description}
          </Typography>
          {slug === ReturnMethodSlug.HappyReturns && isSelected && (
            <Trans i18nKey='v2.return_methods.happy_return.policy'>
              <Typography variant='bodySm' color='secondary'>
                {`By selecting, you agree to Happy Returns’ `}
                <Link target='_blank' href={HAPPY_RETURN_PRIVACY_POLICY_LINK}>
                  <Typography variant='bodySm'>privacy policy.</Typography>
                </Link>
              </Typography>
            </Trans>
          )}
        </Stack>
      }
    >
      {isSelected && (
        <>
          <Divider spacing={Space.Xs} />
          <DropoffLocations
            title={dropoffRecommendsTitleMapping(t)[slug]}
            locations={location?.locations}
          >
            {slug === ReturnMethodSlug.HappyReturns && (
              <ViewHappyReturnLocations
                isLoading={isNearbyLoading}
                locations={nearbyLocations?.locations || []}
                allLocationsLink={nearbyLocations?.link || location.link || ''}
                onNearByLocations={onNearByLocations}
              />
            )}
            {slug === ReturnMethodSlug.RetailRework && location.locations.length > 3 && (
              <ViewRetailReworkLocations locations={location.locations} />
            )}
          </DropoffLocations>
        </>
      )}
    </DefaultMethod>
  );
};

export default DropoffMethod;
