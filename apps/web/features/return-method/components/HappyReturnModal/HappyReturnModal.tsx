import { useEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Box, Icon, Spinner, Stack, Typography } from '@aftership/astra';
import { LocationOutlined } from '@aftership/astra-icons';
import { semanticTokenVar } from '@aftership/astra-tokens/semantic-token/Contract.css';
import { DistanceUnit, DropoffLocationItem, LngLat } from '@aftership/returns-logics-core';

import { Button } from '@/components/Button';
import { LinkWithIcon } from '@/components/LinkWithIcon';
import { Modal } from '@/components/Modal';
import { LocationItem } from '@/features/return-method/components/LocationItem';
import useGeoLocation from '@/hooks/useGeoLocation';

const { Space } = semanticTokenVar;

interface DeniedPermissionContentProps {
  showAllLocations?: boolean;
  allLocationsLink?: string | null;
}
interface PromptPermissionContentProps {
  onPressPreciseLocation: () => void;
  showAllLocations?: boolean;
  allLocationsLink?: string | null;
}
interface HappyReturnModalProps {
  isLoading?: boolean;
  locations?: DropoffLocationItem[];
  allLocationsLink?: string | null;
  showAllLocations?: boolean;
  onNearByLocations?: (latitude: number, longitude: number) => void;
}

const DeniedPermissionContent = ({
  showAllLocations,
  allLocationsLink,
}: DeniedPermissionContentProps) => {
  const { t } = useTranslation();
  return (
    <Stack flex={1} direction='column' align='center' justify='center' gap='m'>
      <Stack direction='column' align='center' gap='2xs'>
        <Typography variant='bodyMdSemibold'>{t('location.permission.deny.title')}</Typography>
        <Typography variant='bodySm' color='secondary'>
          {t('location.permission.deny.desc')}
        </Typography>
      </Stack>
      {showAllLocations && allLocationsLink && (
        <LinkWithIcon link={allLocationsLink}>
          {t('page.happyReturn.action.viewAllLocations')}
        </LinkWithIcon>
      )}
    </Stack>
  );
};

const PromptPermissionContent = ({
  showAllLocations,
  allLocationsLink,
  onPressPreciseLocation,
}: PromptPermissionContentProps) => {
  const [isLoading, setIsLoading] = useState(false);
  const { t } = useTranslation();
  return (
    <Stack flex={1} direction='column' align='center' justify='center' gap='m'>
      <Stack direction='column' align='center' gap='2xs'>
        {/* todo: i18n key 拼写错误 */}
        <Typography variant='bodyMdSemibold' color='primary'>
          {t('location.permission.reques.title')}
        </Typography>
        <Typography variant='bodySm' color='secondary'>
          {t('location.permission.request.desc')}
        </Typography>
      </Stack>
      <Button
        variant='basic'
        onPress={() => {
          setIsLoading(true);
          onPressPreciseLocation();
        }}
        isLoading={isLoading}
      >
        {t('location.permission.request.preciseLocation')}
      </Button>
      {showAllLocations && allLocationsLink && (
        <LinkWithIcon link={allLocationsLink}>
          {t('page.happyReturn.action.viewAllLocations')}
        </LinkWithIcon>
      )}
    </Stack>
  );
};

const HappyReturnModal = ({
  isLoading,
  locations = [],
  allLocationsLink,
  showAllLocations = false,
  onNearByLocations,
}: HappyReturnModalProps) => {
  const { t } = useTranslation();
  const { location, locationPermission, requestPreciseLocation } = useGeoLocation();
  const locationRef = useRef<LngLat>();

  useEffect(() => {
    if (location && locationRef.current !== location) {
      locationRef.current = location;
      onNearByLocations?.(location.latitude, location.longitude);
    }
  }, [location, isLoading, onNearByLocations]);

  const contentMap = {
    denied: (
      <DeniedPermissionContent
        showAllLocations={showAllLocations}
        allLocationsLink={allLocationsLink}
      />
    ),
    prompt: (
      <PromptPermissionContent
        showAllLocations={showAllLocations}
        allLocationsLink={allLocationsLink}
        onPressPreciseLocation={requestPreciseLocation}
      />
    ),
    granted: (
      <>
        {locations.length <= 0 ? (
          <Stack flex={1} direction='column' gap='m' align='center' justify='center'>
            <Stack direction='column' gap='2xs' align='center'>
              <Typography variant='bodyMdSemibold' color='primary'>
                {t('location.empty.title')}
              </Typography>
              <Typography variant='bodySm' color='secondary'>
                {t('location.empty.desc')}
              </Typography>
            </Stack>
            {showAllLocations && allLocationsLink && (
              <LinkWithIcon link={allLocationsLink}>
                {t('page.happyReturn.action.viewAllLocations')}
              </LinkWithIcon>
            )}
          </Stack>
        ) : (
          <Stack direction='column' gap='m' style={{ paddingBlockEnd: Space.Xl }}>
            <Stack align='center' gap='2xs'>
              <Icon source={LocationOutlined} size={Space.M} color='secondary' />
              <Typography variant='bodySm' color='secondary'>
                {t('page.happyReturn.baseShippingAddress')}
              </Typography>
            </Stack>
            {locations.map((location) => (
              <LocationItem
                key={location.name}
                isRedirectToMap
                location={location}
                distanceUnit={DistanceUnit.MI}
              />
            ))}
            {showAllLocations && allLocationsLink && (
              <Stack justify='center' style={{ paddingBlockStart: Space.Xl }}>
                <LinkWithIcon link={allLocationsLink}>
                  {t('page.happyReturn.action.viewAllLocations')}
                </LinkWithIcon>
              </Stack>
            )}
          </Stack>
        )}
      </>
    ),
  };

  return (
    <Modal isDismissable title={t('nearby_location.modal.title')}>
      <Box height={440} overflow='auto'>
        <Stack style={{ height: '100%' }} direction='column'>
          {isLoading ? (
            <Stack flex={1} direction='column' align='center' justify='center'>
              <Spinner />
            </Stack>
          ) : (
            locationPermission && contentMap[locationPermission]
          )}
        </Stack>
      </Box>
    </Modal>
  );
};

export default HappyReturnModal;
