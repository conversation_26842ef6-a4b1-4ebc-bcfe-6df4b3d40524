import { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Box, Icon, Link, Pressable, Stack, Typography } from '@aftership/astra';
import { ArrowDropDownFilled, ArrowDropUpFilled, LocationOutlined } from '@aftership/astra-icons';
import { semanticTokenVar } from '@aftership/astra-tokens/semantic-token/Contract.css';
import { DistanceUnit, DropoffLocationItem } from '@aftership/returns-logics-core';

import { calcBusinessHourScope, calcTodayCloseTime } from '@/utils/weeksUtils';

import { positionIconClassName } from './LocationItem.css';

const { Space } = semanticTokenVar;

interface LocationItemProps {
  location: DropoffLocationItem;
  distanceUnit: DistanceUnit;
  isRedirectToMap?: boolean;
  showMore?: boolean;
}

const LocationItem = ({
  location,
  showMore = true,
  distanceUnit,
  isRedirectToMap = false,
}: LocationItemProps) => {
  const { t } = useTranslation();
  const [isExpand, setExpand] = useState(!showMore);
  const { country, city, longitude, street, latitude, postal_code } = location.address ?? {};

  const secondaryTitle = useMemo(() => {
    const todayCloseTime = calcTodayCloseTime(location.business_hours);
    if (todayCloseTime) {
      return `${location.distance.toFixed(1)} ${distanceUnit} | ${t(
        'page.happyReturn.description.closeTime',
        { closeTime: todayCloseTime },
      )}`;
    } else {
      return `${location.distance.toFixed(1)} ${distanceUnit}`;
    }
  }, [distanceUnit, location.business_hours, location.distance, t]);
  const businessHourScope = useMemo(
    () => calcBusinessHourScope(location.business_hours),
    [location],
  );

  const addressText = [street, city, postal_code, country].filter(Boolean).join(', ');

  const handleClick = () => {
    setExpand(() => !isExpand);
  };

  const needMoreEntrance = businessHourScope.length > 0 || addressText.length > 0;

  return (
    <Stack direction='column'>
      <Stack align='center'>
        <Stack flex={1} direction='column' gap='2xs'>
          <Typography variant='bodyMdSemibold' color='primary'>
            {location.name}
          </Typography>
          <Stack gap='xs'>
            <Typography variant='bodySm' color='secondary'>
              {secondaryTitle}
            </Typography>
            {needMoreEntrance && showMore && (
              <Pressable onPress={handleClick}>
                <Stack align='center' gap='2xs' style={{ height: Space.L }}>
                  <Typography style={{ flexShrink: 0 }} variant='bodySm' color='secondary'>
                    {isExpand ? t('page.action.showLess') : t('page.action.showAll')}
                  </Typography>
                  <Icon
                    color='secondary'
                    source={isExpand ? ArrowDropUpFilled : ArrowDropDownFilled}
                  />
                </Stack>
              </Pressable>
            )}
          </Stack>
        </Stack>
        {isRedirectToMap && (
          <Link
            className={positionIconClassName}
            href={`https://maps.google.com/maps?q=${location.address?.latitude},${location.address?.longitude}`}
            target='_blank'
          >
            <Icon source={LocationOutlined} />
          </Link>
        )}
      </Stack>

      {isExpand && (
        <Box>
          {businessHourScope.length > 0 && (
            <Stack direction='column' style={{ paddingBlockEnd: Space['2Xs'] }}>
              {businessHourScope.map((item) => (
                <Typography variant='bodySm' color='secondary' key={item}>
                  {item}
                </Typography>
              ))}
            </Stack>
          )}
          {/* RMA Detail 场景下设计要求 address 应该是纯文案而不是 link */}
          {/* 因为 RMA Detail 底下已经有 Get directions 按钮，所以只需要展示地址即可 */}
          {Boolean(addressText) &&
            (!showMore ? (
              <Typography variant='bodySm' color='secondary' as='p'>
                {addressText}
              </Typography>
            ) : (
              <Link
                target='_blank'
                href={`https://maps.google.com/maps?q=${latitude},${longitude}`}
              >
                <Typography
                  variant='bodySm'
                  color='secondary'
                  style={{ textDecoration: 'underline' }}
                  as='p'
                >
                  {addressText}
                </Typography>
              </Link>
            ))}
        </Box>
      )}
    </Stack>
  );
};

export default LocationItem;
