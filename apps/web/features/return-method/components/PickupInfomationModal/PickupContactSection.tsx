import { t } from 'i18next';
import { useFormContext } from 'react-hook-form';

import { Stack } from '@aftership/astra';
import { PickupInfomationData } from '@aftership/returns-logics-core';

import FormInput from '@/components/Form/FormInput';

import { SectionWrapper } from './SectionWrapper';

export const PickupContactContent = () => {
  const { control } = useFormContext<PickupInfomationData>();

  return (
    <Stack direction='column' gap='m'>
      <Stack gap='xs'>
        <FormInput
          fullWidth
          control={control}
          name='pickupContact.firstName'
          placeholder={t('first_name.optional')}
        />
        <FormInput
          fullWidth
          control={control}
          name='pickupContact.lastName'
          placeholder={t('page.gift.placeholder.lastName')}
        />
      </Stack>
      <FormInput
        fullWidth
        control={control}
        name='pickupContact.phoneNumber'
        placeholder={t('page.gift.placeholder.phoneNumber')}
      />
    </Stack>
  );
};

export const PickupContactSection = () => {
  return (
    <SectionWrapper title={t('pickup.location.outerContact.title')}>
      <PickupContactContent />
    </SectionWrapper>
  );
};
