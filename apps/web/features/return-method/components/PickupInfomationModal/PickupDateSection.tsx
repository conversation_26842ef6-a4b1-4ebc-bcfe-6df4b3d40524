import { t } from 'i18next';
import { get } from 'lodash-es';
import { useMemo } from 'react';
import { Controller, useFormContext } from 'react-hook-form';

import { Stack, Typography } from '@aftership/astra';
import { tokenVars } from '@aftership/astra-tokens/Contract.css';
import { PickupInfomationData } from '@aftership/returns-logics-core';

import { DatePicker, DatePickerProps } from '@/components/DatePicker/DatePicker';
import { PICK_UP_DATE_LIMIT, PICK_UP_LAST_14_DAYS } from '@/features/return-method/utils/constants';
import useDevice from '@/hooks/useDevice';

import { SectionWrapper } from './SectionWrapper';

const { Color } = tokenVars.Primitive;

interface PickupDateSectionProps {
  isDisabledWeekend?: boolean;
}

export const PickupDateSection = ({ isDisabledWeekend }: PickupDateSectionProps) => {
  const { control } = useFormContext<PickupInfomationData>();
  const isMobile = useDevice().mobile;

  const dates: DatePickerProps['dates'] = useMemo(() => {
    if (!isDisabledWeekend) return PICK_UP_LAST_14_DAYS;

    // 如果禁用周末，需要将周末日期禁用
    return PICK_UP_LAST_14_DAYS.map((date) => {
      return {
        ...date,
        disabled: date.date.day() === 0 || date.date.day() === 6,
      };
    });
  }, [isDisabledWeekend]);

  return (
    <SectionWrapper title={t('pickup.info.title')}>
      <Stack direction='column' gap='m'>
        <Controller
          control={control}
          name='pickupAppointmentDate'
          render={({ field: { value, onChange }, formState }) => {
            const msg = get(formState?.errors, 'pickupAppointmentDate')?.message as string;

            return (
              <Stack gap='xs' direction='column'>
                <DatePicker
                  dates={dates}
                  limit={isMobile ? PICK_UP_DATE_LIMIT : 7}
                  value={value}
                  onSelect={onChange}
                  showPagination={!isMobile}
                  scrollable={isMobile}
                  minTooltip={t('pick_up.date.limitation')}
                  maxTooltip={t('pick_up.date.limitation')}
                  style={{ width: '100%' }}
                />
                {msg && (
                  <Typography variant='bodySm' style={{ color: Color.Red[700] }}>
                    {msg}
                  </Typography>
                )}
              </Stack>
            );
          }}
        />
        <Typography variant='bodyMd' color='tertiary'>
          {t('pick_up.date.instructions')}
        </Typography>
      </Stack>
    </SectionWrapper>
  );
};
