import { yupResolver } from '@hookform/resolvers/yup';
import { t } from 'i18next';
import { useRef } from 'react';
import { FormProvider, useForm } from 'react-hook-form';

import { Stack } from '@aftership/astra';
import { PickupInfomationData } from '@aftership/returns-logics-core';
import { useCountries } from 'returns-logics/react';

import { Modal } from '@/components/Modal';
import { useGetPickupDefaultValues } from '@/features/return-method/hooks/useGetPickupDefaultValues';
import {
  getIsShowContectSection,
  usePickupSettings,
} from '@/features/return-method/hooks/usePickupInfomation';
import { useMainFlow } from '@/hooks/useMainFlow';

import { PickupContactSection } from './PickupContactSection';
import { PickupDateSection } from './PickupDateSection';
import { PickupShippingAddress } from './PickupShippingAddress';
import { SpecialInstruction } from './SpecialInstruction';
import { pickupInfomationSchema } from './schema';

interface PickupInfomationFormProps {
  open: boolean;
  onClose: VoidFunction;
  handleComfirmPickupInfo: (data: PickupInfomationData) => void;
}

export const PickupInfomationForm = ({
  open,
  onClose,
  handleComfirmPickupInfo,
}: PickupInfomationFormProps) => {
  const mainFlow = useMainFlow();

  const defaultValues = useGetPickupDefaultValues();
  const { isPickupInstructionRequired, isWeekendDisabled } = usePickupSettings();

  const {
    data: { countries },
  } = useCountries();

  const form = useForm<PickupInfomationData>({
    mode: 'all',
    defaultValues,
    resolver: yupResolver(pickupInfomationSchema()) as any,
    context: {
      countries,
      isMissingPhoneNumber: mainFlow?.context?.request?.orders?.missing_phone_number,
      isPickupInstructionRequired,
    },
  });

  const ref = useRef<HTMLDivElement | null>(null);

  const { isShowOuterContactSection, isShowInnerContactSection } = getIsShowContectSection(
    form.watch('pickupAddressSource'),
    !!mainFlow?.context?.request?.orders?.missing_phone_number,
  );

  const handleSubmit = form.handleSubmit(
    (data) => {
      handleComfirmPickupInfo({
        ...data,
        pickupContact:
          isShowInnerContactSection || isShowOuterContactSection ? data.pickupContact : null,
      });
    },
    () => {
      if (ref.current) {
        const scrollElement = ref.current.parentElement;
        if (scrollElement) {
          scrollElement.scrollTo({ top: 0, behavior: 'smooth' });
        }
      }
    },
  );

  return (
    <Modal
      isOpen={open}
      title={t('schedule_pickup.title')}
      onClose={onClose}
      primaryAction={{
        content: t('page.request.nextStep'),
        onAction: handleSubmit,
      }}
    >
      <FormProvider {...form}>
        <Stack direction='column'>
          <div ref={ref} />
          <Stack direction='column' gap='xl' flex={1}>
            <PickupDateSection isDisabledWeekend={isWeekendDisabled} />

            {isShowOuterContactSection ? <PickupContactSection /> : null}
            <PickupShippingAddress countries={countries} />
            <SpecialInstruction />
          </Stack>
        </Stack>
      </FormProvider>
    </Modal>
  );
};
