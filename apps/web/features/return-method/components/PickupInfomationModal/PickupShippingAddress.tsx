import { t } from 'i18next';
import { useMemo } from 'react';
import { useFormContext } from 'react-hook-form';

import { Box, Stack, Typography } from '@aftership/astra';
import {
  CountryCodeMap,
  PickupAddressSource,
  PickupInfomationData,
} from '@aftership/returns-logics-core';

import { Divider } from '@/components/Divider';
import FormInput from '@/components/Form/FormInput';
import FormRadio from '@/components/Form/FormRadio';
import FormSelect from '@/components/Form/FormSelect';
import useCountry from '@/hooks/useCountry';

import { PickupContactContent } from './PickupContactSection';
import { SectionWrapper } from './SectionWrapper';
import { anotherAddressWrapper } from './index.css';

import { usePickupInfomation } from '../../hooks/usePickupInfomation';

const getRadioOptions = () => [
  {
    value: PickupAddressSource.order,
    label: t('pickup.address.source.order'),
  },
  {
    value: PickupAddressSource.manual,
    label: t('pickup.address.source.manual'),
  },
];

export const PickupShippingAddress = ({ countries }: { countries?: CountryCodeMap }) => {
  const { control } = useFormContext<PickupInfomationData>();

  const { isShowAddressSection, isShowInnerContactSection } = usePickupInfomation();

  return (
    <SectionWrapper title={t('pickup.location.title')}>
      <Stack direction='column' gap='xs'>
        <FormRadio control={control} name='pickupAddressSource' radioItems={getRadioOptions()} />
        {isShowAddressSection && (
          <Stack className={anotherAddressWrapper} direction='column'>
            {isShowInnerContactSection && (
              <Box>
                <SectionWrapper
                  title={
                    <Typography color='secondary' variant='bodySm'>
                      Contact
                    </Typography>
                  }
                >
                  <PickupContactContent />
                </SectionWrapper>
                <Divider spacing={16} />
              </Box>
            )}
            <SectionWrapper
              title={
                <Typography color='secondary' variant='bodySm'>
                  {t('pickup.location.address.title')}
                </Typography>
              }
            >
              <AddressForm countries={countries} />
            </SectionWrapper>
          </Stack>
        )}
      </Stack>
    </SectionWrapper>
  );
};

const AddressForm = ({ countries }: { countries?: CountryCodeMap }) => {
  const { control, watch, setValue } = useFormContext<PickupInfomationData>();

  const { options, findHasState, getStateOption } = useCountry(countries);

  const countryCode = watch('pickupAddress.country');
  const stateOptions = useMemo(() => getStateOption(countryCode), [countryCode, getStateOption]);
  const hasState = findHasState(countryCode);

  return (
    <Stack direction='column' gap='m'>
      <Stack gap='xs'>
        <FormInput
          control={control}
          placeholder={t('page.pickup.placeholder.firstName')}
          name='pickupAddress.firstName'
          fullWidth
        />
        <FormInput
          control={control}
          placeholder={t('page.pickup.placeholder.lastName')}
          name='pickupAddress.lastName'
          fullWidth
        />
      </Stack>
      <FormSelect
        control={control}
        options={options}
        placeholder={t('page.gift.placeholder.country')}
        name='pickupAddress.country'
        isDisabled
      />
      <FormInput
        control={control}
        placeholder={t('address.placeholder')}
        name='pickupAddress.addressLine1'
        fullWidth
      />
      <Stack gap='xs'>
        <Box width={hasState ? '50%' : '100%'}>
          <FormInput
            control={control}
            placeholder={t('page.gift.placeholder.city')}
            name='pickupAddress.city'
            fullWidth
          />
        </Box>
        {hasState && (
          <Box width='50%'>
            <FormSelect
              control={control}
              name='pickupAddress.state'
              options={stateOptions}
              placeholder={t('page.gift.placeholder.state')}
            />
          </Box>
        )}
      </Stack>
      <FormInput
        control={control}
        placeholder={t('page.gift.placeholder.postalCode')}
        name='pickupAddress.postalCode'
        onChange={(value) => {
          setValue('pickupAddress.postalCode', value.toUpperCase(), {
            shouldValidate: true,
          });
        }}
        fullWidth
      />
    </Stack>
  );
};
