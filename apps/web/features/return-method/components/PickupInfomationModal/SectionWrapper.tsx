import { ReactNode } from 'react';

import { Stack, Typography, clsx } from '@aftership/astra';

import { fullWidthFlex } from './index.css';

type SectionWrapperProps = {
  title: ReactNode;
  children: React.ReactNode;
  className?: string;
};

export const SectionWrapper = ({ title, children, className }: SectionWrapperProps) => {
  return (
    <Stack className={clsx(className)}>
      <Stack direction='column' gap='xs' className={fullWidthFlex}>
        {typeof title === 'string' ? (
          <Typography variant='bodyLgSemibold'>{title}</Typography>
        ) : (
          title
        )}
        {children}
      </Stack>
    </Stack>
  );
};
