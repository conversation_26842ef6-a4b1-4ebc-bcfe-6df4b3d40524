import { style } from '@vanilla-extract/css';

import { primitiveTokenVar } from '@aftership/astra-tokens/primitive-token/Contract.css';
import { semanticTokenVar } from '@aftership/astra-tokens/semantic-token/Contract.css';

const { Color } = primitiveTokenVar;
const { Space, Radius } = semanticTokenVar;

export const fullWidthFlex = style({
  width: '0',
  flex: 1,
});

export const addressWrapper = style({
  backgroundColor: Color.Gray['200'],
  borderRadius: Radius.M,
  padding: Space.M,
});

export const anotherAddressWrapper = style({
  backgroundColor: Color.Gray['200'],
  borderRadius: Radius.M,
  padding: Space.M,
});
