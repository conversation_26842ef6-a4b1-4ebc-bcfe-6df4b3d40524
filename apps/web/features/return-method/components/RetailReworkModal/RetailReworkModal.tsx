import { useTranslation } from 'react-i18next';

import { Box, Icon, Stack, Typography } from '@aftership/astra';
import { LocationOutlined } from '@aftership/astra-icons';
import { semanticTokenVar } from '@aftership/astra-tokens/semantic-token/Contract.css';
import { DistanceUnit, DropoffLocationItem } from '@aftership/returns-logics-core';

import { Modal } from '@/components/Modal';
import { LocationItem } from '@/features/return-method/components/LocationItem';

const { Space } = semanticTokenVar;

interface RetailReworkModalProps {
  locations?: DropoffLocationItem[];
}

const RetailReworkModal = ({ locations = [] }: RetailReworkModalProps) => {
  const { t } = useTranslation();

  return (
    <Modal isDismissable title={t('nearby_location.modal.title')}>
      <Box height={440} overflow='auto'>
        <Stack direction='column' gap='m'>
          <Stack align='center' gap='2xs'>
            <Icon source={LocationOutlined} size={Space.M} />
            <Typography variant='bodySm' color='secondary'>
              {t('page.happyReturn.baseShippingAddress')}
            </Typography>
          </Stack>
          {locations.map((location) => (
            <LocationItem
              key={location.name}
              isRedirectToMap
              location={location}
              distanceUnit={DistanceUnit.MI}
            />
          ))}
        </Stack>
      </Box>
    </Modal>
  );
};

export default RetailReworkModal;
