import { useTranslation } from 'react-i18next';

import { Link, ModalTrigger, Stack, Typography, UnstyledButton } from '@aftership/astra';
import { DropoffLocationItem, GrayFeatureKey } from '@aftership/returns-logics-core';

import { HappyReturnModal } from '@/features/return-method/components/HappyReturnModal';
import useGetGrayFeatureEnabled from '@/hooks/useGetGrayFeatureEnabled';

interface ViewHappyReturnLocationsProps {
  isLoading?: boolean;
  allLocationsLink?: string | null;
  locations?: DropoffLocationItem[];
  nearByLayout?: 'start' | 'center' | 'end';
  onNearByLocations?: (latitude: number, longitude: number) => void;
  children?: React.ReactNode;
}

const ViewHappyReturnLocations = ({
  isLoading = false,
  locations = [],
  allLocationsLink,
  nearByLayout = 'start',
  onNearByLocations,
  children,
}: ViewHappyReturnLocationsProps) => {
  const { t } = useTranslation();
  const hiddenAllLocations = useGetGrayFeatureEnabled(
    GrayFeatureKey.HiddenHappyReturnViewAllLocation,
  );

  return (
    <ModalTrigger>
      {children || (
        <UnstyledButton>
          <Stack justify={nearByLayout}>
            <Link>
              <Typography variant='bodyMd'>{t('view.nearby.locations')}</Typography>
            </Link>
          </Stack>
        </UnstyledButton>
      )}
      <HappyReturnModal
        isLoading={isLoading}
        locations={locations}
        allLocationsLink={allLocationsLink}
        showAllLocations={!hiddenAllLocations}
        onNearByLocations={onNearByLocations}
      />
    </ModalTrigger>
  );
};

export default ViewHappyReturnLocations;
