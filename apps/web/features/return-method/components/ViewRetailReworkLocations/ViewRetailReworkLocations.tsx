import { useTranslation } from 'react-i18next';

import { Link, ModalTrigger, Typography, UnstyledButton } from '@aftership/astra';
import { DropoffLocationItem } from '@aftership/returns-logics-core';

import { RetailReworkModal } from '@/features/return-method/components/RetailReworkModal';

interface ViewRetailReworkLocationsProps {
  locations?: DropoffLocationItem[];
  children?: React.ReactNode;
}

const ViewRetailReworkLocations = ({ locations, children }: ViewRetailReworkLocationsProps) => {
  const { t } = useTranslation();

  return (
    <ModalTrigger>
      {children || (
        <UnstyledButton>
          <Link>
            <Typography variant='bodyMd'>{t('view.more.locations')}</Typography>
          </Link>
        </UnstyledButton>
      )}
      <RetailReworkModal locations={locations} />
    </ModalTrigger>
  );
};

export default ViewRetailReworkLocations;
