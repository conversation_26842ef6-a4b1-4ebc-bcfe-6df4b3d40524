import { useMemo } from 'react';

import { PickupAddressSource } from '@aftership/returns-logics-core';

import { useReturnMethodSubFlow } from './useReturnMethodSubFlow';

export const useGetPickupDefaultValues = () => {
  const returnMethodSubFlow = useReturnMethodSubFlow();

  const { context } = returnMethodSubFlow || {};
  const pickupData = context?.pickupData;
  const returnShippingAddress = context?.returnShippingAddress;

  return useMemo(() => {
    return {
      ...pickupData,
      pickupAddress: {
        ...pickupData?.pickupAddress,
        country: pickupData?.pickupAddress?.country || returnShippingAddress?.country,
      },
      pickupAppointmentDate: pickupData?.pickupAppointmentDate ?? '',
      pickupAddressSource: pickupData?.pickupAddressSource ?? PickupAddressSource.order,
    };
  }, [pickupData, returnShippingAddress]);
};
