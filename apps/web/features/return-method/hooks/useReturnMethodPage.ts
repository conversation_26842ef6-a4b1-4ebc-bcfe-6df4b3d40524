import { useFlow } from 'returns-logics/react';

import { useReturnMethodSubFlow } from './useReturnMethodSubFlow';

export const useReturnMethodPage = () => {
  const mainFlow = useFlow();
  const returnMethodFlow = useReturnMethodSubFlow();
  const { context, currentStep, dispatch } = returnMethodFlow || {};

  return {
    isLoading: currentStep?.isLoading,
    returnMethods: context?.returnMethods ?? [],
    dropoffLocations: context?.dropoffLocations ?? [],
    showMerchantModeOverrideBanner: !!mainFlow.context?.orderLookup?.isMerchantMode,

    handleGoBack: () => {
      dispatch?.({ type: 'GO_BACK' });
    },
  };
};
