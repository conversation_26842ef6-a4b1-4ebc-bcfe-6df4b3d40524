import { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Box, Icon, Spinner, Stack, clsx } from '@aftership/astra';
import { CheckFilled } from '@aftership/astra-icons';
import { primitiveTokenVar } from '@aftership/astra-tokens/primitive-token/Contract.css';
import { semanticTokenVar } from '@aftership/astra-tokens/semantic-token/Contract.css';
import { usePreviewContext } from '@aftership/preview-kit/client';
import { PickupInfomationData, ReturnMethodSlug } from '@aftership/returns-logics-core';

import { Button } from '@/components/Button';
import { ListBox, ListBoxItem } from '@/components/ListBox';
import { ScrollBox } from '@/components/ScrollBox';
import { SomethingWentWrong } from '@/components/SomethingWentWrong';
import { StepCard } from '@/components/StepCard';
import {
  ReturnMethodItemBox,
  ReturnMethodPageTitleText,
} from '@/features/preview/components/WithPreviewSection';
import { OrderWarningTips } from '@/features/request-returns/components/OrderWarningTips';
import { DefaultMethod } from '@/features/return-method/components/DefaultMethod';
import useDevice from '@/hooks/useDevice';
import { useStepCardMinHeight } from '@/hooks/useStepCardMinHeight';
import { ReturnMethodSuffix, genReturnRoutingRuleCode } from '@/i18n/dynamic';
import {
  customScrollbarDesktopStyle,
  customScrollbarMobileStyle,
  disablePointerEventsStyle,
  stableScrollbarGutterStyle,
} from '@/styles/common.css';
import getTrackerInstance from '@/utils/tracker';
import { EventName } from '@/utils/tracker/consts';

import { DropoffMethod } from './components/DropoffMethod';
import { PickupInfomationForm } from './components/PickupInfomationModal/PickupInfomationModal';
import { useGetNearByLocation } from './hooks/useGetNearByLocation';
import { useReturnMethodPage } from './hooks/useReturnMethodPage';
import { useSelectReturnMethod } from './hooks/useSelectReturnMethod';
import { formatCostOfReturn } from './utils/costOfReturn';

const { Space: PrimitiveSpace } = primitiveTokenVar;
const { Space } = semanticTokenVar;

const ReturnMethodList = () => {
  const { t } = useTranslation();
  const { isPreview } = usePreviewContext();
  const { handleLoadNearbyLocations, nearbyLocations, isNearbyLoading } = useGetNearByLocation();
  const { returnMethods, isLoading, dropoffLocations } = useReturnMethodPage();
  const { selectedMethodData, handleSelectReturnMethod } = useSelectReturnMethod();

  const listBoxDependencies = useMemo(() => {
    return [nearbyLocations, returnMethods, isNearbyLoading, selectedMethodData];
  }, [isNearbyLoading, nearbyLocations, returnMethods, selectedMethodData]);

  return (
    <ListBox
      selectionMode='single'
      rowGap={Space.M}
      dependencies={listBoxDependencies}
      selectedKeys={new Set([selectedMethodData?.selectedMethodId || ''])}
      items={isLoading ? [] : returnMethods || []}
      className={clsx({ [disablePointerEventsStyle]: isPreview })}
      onSelectionChange={(selectedKeys) => {
        // tips: 不支持反选和多选
        if (typeof selectedKeys !== 'string' && selectedKeys.size > 0) {
          const methodId = selectedKeys.keys().next().value || '';
          handleSelectReturnMethod(
            String(methodId),
            returnMethods.find((method) => method.id === methodId)?.slug!,
          );
        }
      }}
    >
      {(method) => {
        return (
          <ListBoxItem isDisabled={method.disabled}>
            {({ isSelected }) => {
              const title = t(
                genReturnRoutingRuleCode({
                  methodId: method.id,
                  suffix: ReturnMethodSuffix.Name,
                }),
                { rawValue: method.name, defaultValue: method.name },
              );
              const costOfReturn = formatCostOfReturn(
                method.cost_of_return,
                method.return_cost_option,
              );
              const description = t(
                genReturnRoutingRuleCode({
                  methodId: method.id,
                  suffix: ReturnMethodSuffix.Description,
                }),
                { rawValue: method.description, defaultValue: method.description },
              );
              return (
                <ReturnMethodItemBox position='relative' padding={Space.M}>
                  {isSelected && (
                    <Box position='absolute' top={Space.M} right={Space.M}>
                      <Icon source={CheckFilled} size={Space.Xl} color='brand' />
                    </Box>
                  )}
                  {method.slug === ReturnMethodSlug.HappyReturns ||
                  method.slug === ReturnMethodSlug.RetailRework ? (
                    <DropoffMethod
                      slug={method.slug}
                      title={title + costOfReturn}
                      description={description}
                      location={dropoffLocations?.[method.slug]}
                      nearbyLocations={nearbyLocations}
                      onNearByLocations={handleLoadNearbyLocations}
                      isSelected={isSelected}
                      isNearbyLoading={isNearbyLoading}
                      disabled={method.disabled}
                    />
                  ) : (
                    <DefaultMethod
                      slug={method.slug}
                      title={title + costOfReturn}
                      description={description}
                      disabled={method.disabled}
                    />
                  )}
                </ReturnMethodItemBox>
              );
            }}
          </ListBoxItem>
        );
      }}
    </ListBox>
  );
};

const ReturnMethod = () => {
  const { mobile: isMobile } = useDevice();
  const { t } = useTranslation();
  const { isLoading, returnMethods, showMerchantModeOverrideBanner, handleGoBack } =
    useReturnMethodPage();

  const {
    selectedMethodData,
    handleSelectReturnMethod,
    handleFillPickupData,
    handleSelectMethodDone,
  } = useSelectReturnMethod();

  const minHeight = useStepCardMinHeight();
  const isWrongInput = returnMethods.length <= 0;

  const [isOpenCarrierPickupModal, setIsOpenCarrierPickupModal] = useState(false);

  const handleNext = () => {
    const { selectedMethodSlug } = selectedMethodData;

    getTrackerInstance().reportClickEvent({ eventName: EventName.clickReturnMethodNext });

    // 如果是 carrier pickup，需要填写信息
    if (selectedMethodSlug === ReturnMethodSlug.CarrierPickup) {
      setIsOpenCarrierPickupModal(true);
      return;
    }

    handleSelectReturnMethod(selectedMethodData.selectedMethodId, selectedMethodSlug!);
    handleSelectMethodDone();
  };

  const handleCloseCarrierPickupModal = () => {
    setIsOpenCarrierPickupModal(false);
  };

  const handleComfirmPickupInfo = (pickupData: PickupInfomationData) => {
    handleFillPickupData(pickupData);
    handleSelectMethodDone();
  };

  return (
    <StepCard
      width={800}
      style={{ height: minHeight }}
      title={
        <ReturnMethodPageTitleText as='p' variant='headingXs' textAlign='center' color='primary'>
          {t('page.description.howWillYouReturnTheItems')}
        </ReturnMethodPageTitleText>
      }
      onBack={handleGoBack}
    >
      <Stack flex={1} style={{ height: '0' }} direction='column'>
        {showMerchantModeOverrideBanner && (
          <Box paddingTop={Space['2Xl']} paddingX={Space['3Xl']}>
            <OrderWarningTips tips={t('page.banner.overrideRule')} />
          </Box>
        )}
        {isLoading ? (
          <Stack flex={1} align='center' justify='center'>
            <Spinner size='large' />
          </Stack>
        ) : isWrongInput ? (
          <SomethingWentWrong
            type='return-method'
            style={{ paddingBottom: PrimitiveSpace['1600'] }}
          />
        ) : (
          <>
            <ScrollBox
              className={clsx(
                isMobile ? customScrollbarMobileStyle : customScrollbarDesktopStyle,
                stableScrollbarGutterStyle,
              )}
              paddingX={isMobile ? Space.M : Space['3Xl']}
              paddingTop={isMobile ? 0 : Space.S}
              paddingBottom={isMobile ? 0 : Space.M}
              marginX={isMobile ? 0 : '2px'}
            >
              {isMobile && (
                <ReturnMethodPageTitleText
                  as='p'
                  variant='headingXs'
                  textAlign='center'
                  color='primary'
                  style={{ paddingBlockEnd: Space.Xl }}
                >
                  {t('page.description.howWillYouReturnTheItems')}
                </ReturnMethodPageTitleText>
              )}
              <ReturnMethodList />
            </ScrollBox>
            <Box
              paddingX={isMobile ? semanticTokenVar.Space.M : '160px'}
              paddingY={semanticTokenVar.Space.M}
            >
              <Button
                isFullWidth
                size='large'
                isDisabled={!selectedMethodData.selectedMethodId}
                onPress={handleNext}
              >
                {t('page.request.nextStep')}
              </Button>
            </Box>
          </>
        )}
      </Stack>
      <PickupInfomationForm
        open={isOpenCarrierPickupModal}
        onClose={handleCloseCarrierPickupModal}
        handleComfirmPickupInfo={handleComfirmPickupInfo}
      />
    </StepCard>
  );
};

export default ReturnMethod;
