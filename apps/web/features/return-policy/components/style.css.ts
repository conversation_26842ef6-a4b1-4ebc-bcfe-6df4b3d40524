import { style } from '@vanilla-extract/css';

import { semanticTokenVar } from '@aftership/astra-tokens/semantic-token/Contract.css';

const { Space } = semanticTokenVar;

export const policyMobile = style({
  flex: 1,
  overflowY: 'auto',
  display: 'flex',
  paddingLeft: Space.M,
  paddingRight: Space.M,
  paddingBottom: Space.M,
  flexDirection: 'column',
  maxHeight: 'max-content',
  gap: Space['2Xl'],
});
