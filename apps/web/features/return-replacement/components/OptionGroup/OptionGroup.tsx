import clsx from 'clsx';
import React, { useCallback, useMemo } from 'react';

import { Stack, ToggleButton, ToggleButtonGroup, Typography } from '@aftership/astra';
import { usePreviewContext } from '@aftership/preview-kit/client';
import { VariantOption } from '@aftership/returns-logics-core';

import { disablePointerEventsStyle } from '@/styles/common.css';

import { OptionGroupListClassName } from './styles.css';

export interface OptionGroupProps {
  name: string;
  values: Array<string>;
  selectedOptions: VariantOption[];
  onSelectedOption?: (optionName: string, value: string) => void;
}

const OptionGroup = ({ name, values, selectedOptions, onSelectedOption }: OptionGroupProps) => {
  const { isPreview } = usePreviewContext();
  const isThisOptionSelected = selectedOptions.find((option) => option.name === name);
  const selectedKeys = isThisOptionSelected?.value ? [isThisOptionSelected?.value] : [];
  const handleSelectionChange = useCallback(
    ([item]: string[]) => {
      onSelectedOption?.(name, item);
    },
    [onSelectedOption, name],
  );
  const items = useMemo(
    () => values.map((item) => ({ id: item, value: item, name: item })),
    [values],
  );

  return (
    <Stack direction='column' gap='xs'>
      <Typography variant='heading2Xs'>{name}</Typography>
      <ToggleButtonGroup
        defaultSelectedValues={selectedKeys}
        selectedValues={selectedKeys}
        items={items}
        selectionMode='single'
        onSelectionChange={handleSelectionChange}
      >
        {(item) => (
          <ToggleButton
            key={item.id}
            value={item.id}
            className={clsx(OptionGroupListClassName, { [disablePointerEventsStyle]: isPreview })}
          >
            {item.name}
          </ToggleButton>
        )}
      </ToggleButtonGroup>
    </Stack>
  );
};

export default OptionGroup;
