import React from 'react';
import { useTranslation } from 'react-i18next';

import { Box, Icon, Stack, Typography, UnstyledButton } from '@aftership/astra';
import { BagCheckedFilled } from '@aftership/astra-icons';
import { tokenVars } from '@aftership/astra-tokens/Contract.css';

import { EllipsisText } from '@/components/EllipsisText';
import { exchangeTextStyle } from '@/features/return-replacement/components/ReplaceInfoBanner/styles.css.ts';
import useDevice from '@/hooks/useDevice';

const { Space } = tokenVars.Semantic;
const { Color: PrimitiveColor } = tokenVars.Primitive;

export interface Props {
  text: string;
  onEdit: VoidFunction;
}

const ReplaceInfoBanner = ({ text, onEdit }: Props) => {
  const { t } = useTranslation();
  const isMobile = useDevice().mobile;

  return (
    <Box
      backgroundColor={PrimitiveColor.Black_Alpha[100]}
      borderRadius={Space.Xs}
      paddingX={Space.M}
      paddingY={isMobile ? Space.S : Space.Xs}
    >
      <Stack gap='xs' align='center'>
        <Icon source={BagCheckedFilled} />
        <EllipsisText
          variant='bodyMdSemibold'
          color='primary'
          className={exchangeTextStyle}
          text={text}
        />

        <UnstyledButton
          onPress={() => onEdit?.()}
          style={{
            paddingBlock: Space.S,
            paddingInline: Space.M,
            marginBlock: `calc(-1 * ${Space.S})`,
            marginInline: `calc(-1 * ${Space.M})`,
          }}
        >
          <Typography variant='bodyMd' color='primary'>
            {t('page.request.edit')}
          </Typography>
        </UnstyledButton>
      </Stack>
    </Box>
  );
};

export default ReplaceInfoBanner;
