import { Box, Stack, Typography } from '@aftership/astra';
import { tokenVars } from '@aftership/astra-tokens/Contract.css';

import { Button } from '@/components/Button';
import useDevice from '@/hooks/useDevice';

import { priceDifferenceStyle } from './ReplaceItemOverlay.css';

const { Space } = tokenVars.Semantic;

interface FooterProps {
  isDisabled: boolean;
  onPress?: VoidFunction;
  buttonText: string;
  showPriceDiffrence?: boolean;
  priceDirrenceText?: string;
}

export const Footer = ({
  isDisabled,
  onPress,
  buttonText,
  priceDirrenceText,
  showPriceDiffrence,
}: FooterProps) => {
  const isMobile = useDevice().mobile;

  return (
    <Stack
      align='end'
      direction='column'
      style={{
        paddingBlockStart: Space.M,
        paddingBlockEnd: isMobile ? Space.M : '36px',
        paddingInline: isMobile ? Space.M : '57px',
      }}
    >
      <Stack gap='s' direction='column' style={{ width: isMobile ? '100%' : 400 }}>
        {showPriceDiffrence && priceDirrenceText ? (
          <Box className={priceDifferenceStyle}>
            <Typography variant='bodyLg' color='primary'>
              {priceDirrenceText}
            </Typography>
          </Box>
        ) : null}
        <Button size='large' isDisabled={isDisabled} onPress={onPress}>
          {buttonText}
        </Button>
      </Stack>
    </Stack>
  );
};
