import { Box, Icon, Stack } from '@aftership/astra';
import { CloseOutlined } from '@aftership/astra-icons';
import { tokenVars } from '@aftership/astra-tokens/Contract.css';

import { Button } from '@/components/Button';

const { Space } = tokenVars.Semantic;

interface HeaderProps {
  onClose: VoidFunction;
}

export const Header = ({ onClose }: HeaderProps) => {
  return (
    <Box paddingY={Space.Xl} paddingX={Space.S}>
      <Stack justify='end' style={{ paddingInlineEnd: Space['2Xs'] }}>
        <Button
          variant={'plain'}
          onPress={() => {
            setTimeout(() => {
              onClose();
            }, 20);
          }}
        >
          <Icon source={CloseOutlined} size={Space['3Xl']} />
        </Button>
      </Stack>
    </Box>
  );
};
