import { style } from '@vanilla-extract/css';

import { tokenVars } from '@aftership/astra-tokens/Contract.css';

const { Space, Color } = tokenVars.Semantic;

export const containerStyle = style({
  flex: 1,
  alignSelf: 'stretch',
  overflowX: 'hidden',
  overflowY: 'auto',
  paddingInlineEnd: Space['3Xl'],
});

export const variantBodyMobileClassName = style({
  flex: 1,
  overflowY: 'auto',
  display: 'flex',
  flexDirection: 'column',
  gap: Space.Xl,
});

export const priceDifferenceStyle = style({
  textAlign: 'center',
});

export const overlayModalClassName = style({
  position: 'relative',
  top: '50%',
  transform: 'translateY(-50%)',
});
export const overlayContentClassName = style({
  margin: 'auto',
  width: 880,
  height: 540,
  background: Color.Bg.Body,
  borderRadius: tokenVars.Semantic.Radius.M,
  overflow: 'hidden',
});
