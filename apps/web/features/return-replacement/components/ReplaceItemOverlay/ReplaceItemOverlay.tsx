import { t } from 'i18next';
import React, { PropsWithChildren, useState } from 'react';

import {
  Box,
  IconButton,
  Overlay,
  Spinner,
  Stack,
  TextArea,
  Typography,
  clsx,
} from '@aftership/astra';
import { ChevronLeftOutlined, CloseOutlined } from '@aftership/astra-icons';
import { tokenVars } from '@aftership/astra-tokens/Contract.css';
import { usePreviewContext } from '@aftership/preview-kit/client';
import {
  ISelectedItem,
  PresentmentMoney,
  SelectSingleReplaceItemContext,
  VariantForReplace,
} from '@aftership/returns-logics-core';

import { EllipsisText } from '@/components/EllipsisText';
import { ImageWithFallback } from '@/components/ImageWithFallback';
import Modal from '@/components/Modal';
import {
  CommentTitleText,
  VariantInstructionTitleText,
} from '@/features/preview/components/WithPreviewSection/index.tsx';
import { VariantList } from '@/features/return-replacement/components/VariantList';
import { useExchangeConfig } from '@/features/return-replacement/hooks/useExchangeConfig';
import { getReplaceButtonInvalidResourceCode } from '@/features/return-replacement/utils/i18n';
import useDevice from '@/hooks/useDevice';
import { useMainFlow } from '@/hooks/useMainFlow';
import { customScrollbarDesktopStyle, customScrollbarMobileStyle } from '@/styles/common.css.ts';
import { toCurrency } from '@/utils/price';
import { decodeHtmlEntities } from '@/utils/products.ts';

import { Footer } from './Footer.tsx';
import {
  containerStyle,
  overlayContentClassName,
  overlayModalClassName,
  variantBodyMobileClassName,
} from './ReplaceItemOverlay.css.ts';

const { Space, Radius } = tokenVars.Semantic;

interface CoverProps {
  variantCoverUrl?: string;
}

const Cover = ({ variantCoverUrl }: CoverProps) => {
  const isMobile = useDevice().mobile;
  const imageProps = isMobile ? { autoFitWidth: true } : { width: 334, height: 334 };
  return (
    <ImageWithFallback
      usingShopifyPreview
      {...imageProps}
      src={variantCoverUrl ?? ''}
      style={{ borderRadius: Radius.Xs }}
      alt='cover'
    />
  );
};

const getReplacePriceDiffirenceText = (
  isCompleteSelectOptions: boolean,
  selectedQuantity: number,
  replacePriceDiffirenceSet?: PresentmentMoney,
) => {
  if (!isCompleteSelectOptions) return '';

  const { amount, currency } = replacePriceDiffirenceSet || {};

  const priceText = amount ? toCurrency(Math.abs(Number(amount) * selectedQuantity), currency) : '';

  if (Number(amount) < 0) {
    return t('v2.replace.pay', {
      price: priceText,
    });
  }

  if (Number(amount) > 0) {
    return t('v2.replace.refund', {
      price: priceText,
    });
  }

  return '';
};

/**
 * 最优先展示 selectedVariant 的信息, 否则展示上次选择的 variant 信息, 否则展示原始 selectItem 的信息
 * @param activeSelectedItem
 * @param selectedVariant
 * @param lastSelectedVariant
 */
const generateDefaultVariantInfo = (
  activeSelectedItem?: ISelectedItem,
  selectedVariant?: VariantForReplace,
  lastSelectedVariant?: VariantForReplace,
): DefaultVariantInfo => {
  const variant = selectedVariant || lastSelectedVariant;

  return {
    productTitle: decodeHtmlEntities(
      variant?.productTitle || activeSelectedItem?.productTitle || '',
    ),
    variantTitle: decodeHtmlEntities(
      selectedVariant?.variantTitle || selectedVariant?.productTitle || '',
    ),
    variantCoverUrl: variant?.variantCoverUrl || activeSelectedItem?.productCoverUrl || '',
    replacePriceDiffirenceSet: variant?.replacePriceDiffirenceSet,
    variantPrice: variant?.priceSet?.presentment_money
      ? toCurrency(
          variant?.priceSet?.presentment_money?.amount,
          variant?.priceSet?.presentment_money?.currency,
        )
      : '',
  };
};
const generateButtonText = (
  isCommentOnly: boolean,
  isCompleteSelectOptions: boolean,
  selectedVariant?: VariantForReplace,
  orgId?: string,
) => {
  if (isCommentOnly) {
    return t('v2.popup.request.next');
  }
  // options 没有选完
  if (!isCompleteSelectOptions) {
    return t('v2.popup.request.select');
  }
  const replaceButtonInvalidResourceCode = getReplaceButtonInvalidResourceCode(orgId);
  if (selectedVariant && !selectedVariant.availability.available) {
    return !selectedVariant.availability.all_of.minimum_inventory_level_passed ||
      !selectedVariant.availability.all_of.online_inventory_level_passed
      ? t('page.request.soldOut')
      : t(replaceButtonInvalidResourceCode);
  } else if (!selectedVariant?.isValid) {
    return t(replaceButtonInvalidResourceCode);
  }
  return t('v2.popup.request.select');
};

interface DefaultVariantInfo {
  productTitle: string;
  variantTitle?: string;
  variantCoverUrl?: string;
  replacePriceDiffirenceSet?: PresentmentMoney;
  variantPrice?: string;
}
interface ContainerProps {
  isOpen: boolean;
  onClose?: VoidFunction;
  onBack?: VoidFunction;
  footer: React.ReactNode;
  header?: React.ReactNode;
  cover: React.ReactNode;
}
const ContainerComp = ({
  isOpen,
  onClose,
  onBack,
  children: body,
  footer,
  header,
  cover,
}: PropsWithChildren<ContainerProps>) => {
  const isMobile = useDevice().mobile;
  const { isPreview } = usePreviewContext();
  const customScrollbarStyle = isMobile ? customScrollbarMobileStyle : customScrollbarDesktopStyle;

  return isMobile ? (
    <Modal
      isOpen={isOpen}
      footer={footer}
      disableFocusManagement={isPreview}
      style={{ height: '95%', maxHeight: '95%' }}
      bodyStyle={{ paddingBlockStart: 0 }}
      bodyClassName={customScrollbarStyle}
      leadingAction={onBack ? { icon: ChevronLeftOutlined, onPress: onBack } : undefined}
      onClose={onClose}
    >
      <Box className={variantBodyMobileClassName}>
        {cover}
        {body}
      </Box>
    </Modal>
  ) : (
    <Overlay isOpen={isOpen} disableFocusManagement={isPreview} className={overlayModalClassName}>
      <Stack direction='column' className={overlayContentClassName}>
        {header}
        <Stack
          direction='row'
          flex={1}
          gap='2xl'
          style={{
            height: '0px',
            paddingInlineStart: Space['4Xl'],
            paddingInlineEnd: Space.Xs,
          }}
        >
          {cover}
          <Stack direction='column' className={clsx(containerStyle, customScrollbarStyle)}>
            {body}
          </Stack>
        </Stack>
        {footer}
      </Stack>
    </Overlay>
  );
};

export interface ReplaceItemModalProps {
  isOpen: boolean;
  context?: SelectSingleReplaceItemContext;
  onBack?: () => void;
  onDone?: (comment: string) => void;
  onClose?: () => void;
  onSelectOption?: (optionName: string, value: string) => void;
}

const ReplaceItemOverlay = ({
  isOpen,
  context,
  onBack,
  onDone,
  onClose,
  onSelectOption,
}: ReplaceItemModalProps) => {
  const { isDisplayComment, isCommentOnly, isDisplayPriceDifference } = useExchangeConfig();
  const { context: mainContext } = useMainFlow();
  const [comment, setComment] = useState<string>(context?.activeSelectedItem?.replaceComment || '');

  const isCompleteSelectOptions =
    context?.availableOptions?.length === context?.selectedOptions?.length;
  const isButtonDisable = isCommentOnly
    ? !comment
    : !context?.selectedVariant || !context?.selectedVariant?.availability?.available;
  const buttonText = generateButtonText(
    isCommentOnly,
    isCompleteSelectOptions,
    context?.selectedVariant,
    mainContext?.storeConfig?.shopInfo?.organization?.id,
  );

  const isShowPriceDiffirence =
    !!context?.selectedVariant?.availability?.available && isDisplayPriceDifference;

  const priceDirrenceText = getReplacePriceDiffirenceText(
    isCompleteSelectOptions,
    context?.activeSelectedItem?.quantity || 1,
    context?.selectedVariant?.replacePriceDiffirenceSet,
  );

  const { productTitle, variantTitle, variantCoverUrl, variantPrice } = generateDefaultVariantInfo(
    context?.activeSelectedItem,
    context?.selectedVariant,
    context?.lastSelectedVariant,
  );

  if (!context) {
    return (
      <ContainerComp isOpen={isOpen} footer={null} cover={null}>
        <Stack align='center' justify='center' flex={1}>
          <Spinner size='large' />
        </Stack>
      </ContainerComp>
    );
  }

  return (
    <ContainerComp
      isOpen={isOpen}
      onClose={onClose}
      onBack={onBack}
      cover={<Cover variantCoverUrl={variantCoverUrl} />}
      header={
        <Stack align='center' style={{ padding: Space.Xl }}>
          {onBack && <IconButton size='large' icon={ChevronLeftOutlined} onPress={onBack} />}
          <Box flex={1} />
          <IconButton size='large' icon={CloseOutlined} onPress={onClose} />
        </Stack>
      }
      footer={
        <Footer
          onPress={() => onDone?.(comment)}
          isDisabled={isButtonDisable}
          buttonText={buttonText}
          showPriceDiffrence={isShowPriceDiffirence}
          priceDirrenceText={priceDirrenceText}
        />
      }
    >
      {isCommentOnly ? (
        <Stack direction='column' gap='xl'>
          <Typography variant='heading2Xs'>{t('page.description.exchange_for')}</Typography>
          <Stack direction='column' gap='xs'>
            <Typography variant='heading2Xs'>{t('page.description.replaceComments')}*</Typography>
            <TextArea
              value={comment}
              showCounter={true}
              maxLength={200}
              height='120px'
              placeholder={t('page.description.writeDownNote')}
              onChange={(value) => setComment(value)}
            />
          </Stack>
        </Stack>
      ) : (
        <Stack direction='column' gap='xl'>
          <Stack direction='column' gap='2xs' style={{ flexShrink: 0 }}>
            <EllipsisText maxLine={3} variant='heading2Xs' text={productTitle} />
            {variantTitle ? (
              <Stack gap='s'>
                <Typography variant='bodyMd' color='secondary'>
                  {variantTitle}
                </Typography>
                <Typography variant='bodyMdSemibold'>{`${variantPrice} x ${context?.activeSelectedItem?.quantity}`}</Typography>
              </Stack>
            ) : (
              <VariantInstructionTitleText variant='bodyMd' color='secondary'>
                {t('page.details.selectReplacementOptions')}
              </VariantInstructionTitleText>
            )}
          </Stack>
          {context && (
            <VariantList
              productOptions={context?.availableOptions}
              selectedOptions={context?.selectedOptions}
              onSelectedOption={onSelectOption}
            />
          )}
          {isDisplayComment && (
            <Stack direction='column' gap='xs'>
              <CommentTitleText variant='heading2Xs'>
                {t('page.description.replaceComments')}
              </CommentTitleText>
              <TextArea
                value={comment}
                showCounter={true}
                maxLength={200}
                height='120px'
                placeholder={t('page.description.writeDownNote')}
                onChange={(value) => setComment(value)}
              />
            </Stack>
          )}
        </Stack>
      )}
    </ContainerComp>
  );
};

export default ReplaceItemOverlay;
