import React from 'react';
import { useTranslation } from 'react-i18next';

import { Box, Icon, Pressable, Stack, Typography, UnstyledButton } from '@aftership/astra';
import { ChevronRightOutlined } from '@aftership/astra-icons';
import { tokenVars } from '@aftership/astra-tokens/Contract.css';
import { PresentmentMoney, VariantForReplace } from '@aftership/returns-logics-core';

import { ItemCardContent } from '@/components/ItemCardContent';
import { ReplaceInfoBanner } from '@/features/return-replacement/components/ReplaceInfoBanner';
import { useExchangeConfig } from '@/features/return-replacement/hooks/useExchangeConfig';
import useDevice from '@/hooks/useDevice';

const { Space, Radius } = tokenVars.Semantic;
const { Color: PrimitiveColor } = tokenVars.Primitive;

export interface IReplacementItem {
  itemId: string;
  productTitle: string;
  productTags: string[];
  productCoverUrl?: string;
  variantTitle?: string;
  price?: PresentmentMoney;
  originPrice?: PresentmentMoney;
  quantity: number;
  exchangeVariant?: VariantForReplace;
  replaceComment?: string;
}
export interface IReplacementItemProps {
  replacementItem: IReplacementItem;
  onEdit: (itemId: string) => void;
  onSelected: (itemId: string) => void;
}
const ReplacementItem = ({
  replacementItem: {
    itemId,
    productTitle,
    productCoverUrl,
    variantTitle,
    price,
    originPrice,
    quantity,
    productTags,
    exchangeVariant,
    replaceComment,
  },
  onSelected,
  onEdit,
}: IReplacementItemProps) => {
  const isMobile = useDevice().mobile;
  const { t } = useTranslation();
  const { isCommentOnly, isVariantEnable } = useExchangeConfig();
  const actionText = isCommentOnly
    ? t('v2.page.action.specify_details')
    : t('v2.page.action.selectVariant');

  /**
   * @description
   * variant 模式下存在 VariantTitle 意味着选择了
   * comment only 模式下存在 replaceComment 意味着选择了
   */
  const isSelected = (exchangeVariant && isVariantEnable) || (isCommentOnly && replaceComment);

  const rightContent = () => {
    if (!isSelected && !isMobile) {
      return (
        <Box alignSelf={'center'}>
          <UnstyledButton onPress={() => onSelected(itemId)}>
            <Stack gap='2xs' align='center'>
              <Typography variant='bodyMdSemibold'>{actionText}</Typography>
              <Icon source={ChevronRightOutlined} />
            </Stack>
          </UnstyledButton>
        </Box>
      );
    }
  };
  const footer = () => {
    if (isMobile && !isSelected) {
      return (
        <Pressable width={'100%'} onPress={() => onSelected(itemId)}>
          <Box
            borderRadius={Radius.M}
            paddingX={Space.M}
            paddingY={Space.Xs}
            backgroundColor={PrimitiveColor.Black_Alpha[100]}
          >
            <Stack gap='xs' align='center'>
              <Box flex={1}>
                <Typography variant='bodyMdSemibold' color='primary'>
                  {actionText}
                </Typography>
              </Box>
              <Icon source={ChevronRightOutlined} size={Space.M} />
            </Stack>
          </Box>
        </Pressable>
      );
    }

    if (isSelected) {
      const selectedInfo = isCommentOnly
        ? t('v2.page.description.exchange_details_added')
        : t('v2.page.description.exchangeFor', {
            description: `${exchangeVariant?.variantTitle ?? exchangeVariant?.productTitle ?? ''} x ${quantity}`,
          });
      return <ReplaceInfoBanner text={selectedInfo} onEdit={() => onEdit(itemId)} />;
    }
  };

  return (
    <Box padding={Space.M}>
      <Stack direction='column' gap='m'>
        <ItemCardContent
          productInfo={{
            productTitle: productTitle,
            variantTitle: variantTitle,
            productTags: productTags,
            price: price,
            quantity: quantity,
            originPrice: originPrice,
            productCoverUrl: productCoverUrl,
          }}
          rightContent={rightContent()}
        />
        {footer()}
      </Stack>
    </Box>
  );
};

export default ReplacementItem;
