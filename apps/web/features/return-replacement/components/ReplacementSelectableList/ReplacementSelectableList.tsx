import React from 'react';

import { tokenVars } from '@aftership/astra-tokens/Contract.css';
import { ISelectedItem } from '@aftership/returns-logics-core';

import { ListBox, ListBoxItem } from '@/components/ListBox';
import { useExchangeConfig } from '@/features/return-replacement/hooks/useExchangeConfig';
import useDevice from '@/hooks/useDevice.ts';

import { IReplacementItem, ReplacementItem } from '../ReplacementItem';

const { Space } = tokenVars.Semantic;

export interface ReplacementListProps {
  items: ISelectedItem[];
  onSelected: (selectedItem: ISelectedItem) => void;
}
const ReplacementSelectableList = ({ items, onSelected }: ReplacementListProps) => {
  const { isCommentOnly } = useExchangeConfig();
  const selectedKeys = items
    .filter((item) => {
      if (isCommentOnly) {
        return !!item.replaceComment;
      }
      return !!item.exchangeVariant;
    })
    .map((item) => item.itemId);
  const isMobile = useDevice().mobile;
  return (
    <ListBox
      selectionMode='single'
      items={items.map((item) => ({
        id: item.itemId,
        ...item,
      }))}
      selectedKeys={selectedKeys}
      rowGap={isMobile ? Space.Xl : Space.S}
    >
      {(item) => {
        const replacementItem: IReplacementItem = {
          ...item,
          price: item.frontEndPrice,
        };
        return (
          <ListBoxItem key={item.itemId}>
            {() => {
              return (
                <ReplacementItem
                  replacementItem={replacementItem}
                  onEdit={() => {
                    onSelected(item);
                  }}
                  onSelected={() => {
                    onSelected(item);
                  }}
                />
              );
            }}
          </ListBoxItem>
        );
      }}
    </ListBox>
  );
};

export default ReplacementSelectableList;
