import React from 'react';

import { Stack } from '@aftership/astra';
import { ProductOption, VariantOption } from '@aftership/returns-logics-core';

import { OptionGroup } from '@/features/return-replacement/components/OptionGroup';

export interface VariantListProps {
  productOptions: ProductOption[];
  selectedOptions: VariantOption[];
  onSelectedOption?: (optionName: string, value: string) => void;
}
const VariantList = ({ productOptions, selectedOptions, onSelectedOption }: VariantListProps) => {
  return (
    <Stack direction='column' gap='xl'>
      {productOptions?.map((productOption, index) => {
        return (
          <OptionGroup
            key={`${productOption.name}-${index}`}
            selectedOptions={selectedOptions}
            name={productOption.name}
            values={productOption.values}
            onSelectedOption={onSelectedOption}
          />
        );
      })}
    </Stack>
  );
};

export default VariantList;
