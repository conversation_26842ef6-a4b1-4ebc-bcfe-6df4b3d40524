import { ExchangeChargeMode, ExchangeMode } from '@aftership/returns-logics-core';

import { useMainFlow } from '@/hooks/useMainFlow';
import { useShopInfo } from '@/hooks/useShopInfo';

export const useExchangeConfig = () => {
  const { context: mainContext } = useMainFlow();
  const { exchangeMode, exchangeRuleItemPriceDifferenceSettlement, exchangeRuleAllowAddNotes } =
    useShopInfo();

  const isUnEvenExchange = exchangeMode === ExchangeMode.DifferentPrice;
  const isChargeByRC =
    isUnEvenExchange && exchangeRuleItemPriceDifferenceSettlement === ExchangeChargeMode.ChargeByRC;
  const isDisplayComment = exchangeMode === ExchangeMode.NotesOnly || exchangeRuleAllowAddNotes;
  const isCommentOnly = exchangeMode === ExchangeMode.NotesOnly;
  const isRegularReturnMode = !mainContext?.orderLookup?.isGiftReturnMode;
  const isVariantEnable = !isCommentOnly;

  return {
    isDisplayComment,
    isCommentOnly,
    isChargeByRC,
    isDisplayPriceDifference: isUnEvenExchange && isRegularReturnMode,
    isVariantEnable,
  };
};
