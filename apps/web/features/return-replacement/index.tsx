import React from 'react';
import { useTranslation } from 'react-i18next';

import { <PERSON><PERSON>, <PERSON>ack, Typography } from '@aftership/astra';
import { useFlow } from 'returns-logics/react';

import { NextButton } from '@/components/NextButton';
import { ScrollFlex } from '@/components/ScrollFlex';
import { OrderWarningTips } from '@/features/request-returns/components/OrderWarningTips';
import { useExchangeConfig } from '@/features/return-replacement/hooks/useExchangeConfig';
import useDevice from '@/hooks/useDevice';
import { useMerchantMode } from '@/hooks/useMerchantMode';
import { useReturnReplacementFlow } from '@/hooks/useReturnReplacementFlow';
import getTrackerInstance from '@/utils/tracker';
import { EventName } from '@/utils/tracker/consts';

import { ReplaceItemOverlay } from './components/ReplaceItemOverlay';
import { ReplacementSelectableList } from './components/ReplacementSelectableList';
import { wrapperClassName } from './styles.css';

const ReplacementPage = () => {
  const { isCommentOnly } = useExchangeConfig();
  const { isLoading, isSelectSingleReplaceItem, context, dispatch } = useReturnReplacementFlow();
  const { selectedItems = [], activeSelectedItem } = context || {};
  const isMerchantMode = useMerchantMode();
  const isMobile = useDevice().mobile;
  const { t } = useTranslation();

  const { children } = useFlow();
  const { dispatch: selectItemDispatch, context: selectItemContext } =
    children?.replaceTheSameItemSubFlow?.children?.[activeSelectedItem?.itemId || ''] || {};

  const isNextButtonDisable = selectedItems?.some(
    (item) =>
      (isCommentOnly && !item?.replaceComment) || (!isCommentOnly && !item?.exchangeVariant),
  );

  const handleClose = () => {
    if (!activeSelectedItem) {
      return;
    }
    selectItemDispatch?.({
      type: 'SELECT_ITEM_CANCEL',
      data: { cancelSelectedItem: activeSelectedItem },
    });
  };
  const handleDone = (comment: string) => {
    if (!activeSelectedItem) {
      return;
    }
    selectItemDispatch?.({
      type: 'SELECT_ITEM_DONE',
      data: {
        itemId: activeSelectedItem.itemId,
        variant: selectItemContext?.selectedVariant!,
        replaceComment: comment,
      },
    });
  };
  const handleSelectOption = (optionName: string, value: string) => {
    selectItemDispatch?.({
      type: 'SELECT_OPTION_ITEM',
      data: { name: optionName, value: value },
    });
  };

  if (isLoading) {
    return (
      <Stack flex={1} align='center' justify='center'>
        <Spinner size='large' />
      </Stack>
    );
  }

  return (
    <>
      <Stack direction='column' flex={1} style={{ height: 0 }}>
        <ScrollFlex className={wrapperClassName}>
          {isMobile && (
            <Typography variant='headingXs' color='primary' textAlign='center'>
              {t('resolution.description.replaceSameItemNew')}
            </Typography>
          )}
          {isMerchantMode && <OrderWarningTips tips={t('page.banner.overrideRule')} />}
          <ReplacementSelectableList
            items={selectedItems}
            onSelected={(selectedItem) => {
              getTrackerInstance().reportClickEvent({
                eventName: EventName.clickSelectReplaceVariant,
              });

              dispatch?.({
                type: 'SELECT_ITEM',
                data: {
                  selectedItem: selectedItem,
                },
              });
            }}
          />
        </ScrollFlex>
        <NextButton
          isLoading={false}
          isDisabled={isNextButtonDisable}
          onPress={() => {
            getTrackerInstance().reportClickEvent({ eventName: EventName.clickReplacePageNext });
            dispatch?.({
              type: 'GO_TO_NEXT_STATE',
            });
          }}
        />
      </Stack>
      {activeSelectedItem && (
        <ReplaceItemOverlay
          isOpen={isSelectSingleReplaceItem}
          onDone={handleDone}
          onClose={handleClose}
          context={selectItemContext}
          onSelectOption={handleSelectOption}
        />
      )}
    </>
  );
};

export default ReplacementPage;
