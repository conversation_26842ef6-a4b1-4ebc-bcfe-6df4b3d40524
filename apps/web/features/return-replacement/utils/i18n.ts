type StringTriple = `page.replace.${string}`;

export const replaceButtonInvalidResourceCodeMap = new Map<string, StringTriple>([
  // Ten little, replaceWithSameItem 按钮需要展示不同的文案
  // https://aftership.atlassian.net/browse/RTC-18743
  ['5f27215d98cd409280a08dc4bda125d6', 'page.replace.tenLittleVariantInvalid'],

  // dev
  ['872feb366c6a4a8fa27da886c91a16b9', 'page.replace.tenLittleVariantInvalid'],
]);

export const getReplaceButtonInvalidResourceCode = (orgId?: string) => {
  return orgId
    ? replaceButtonInvalidResourceCodeMap.get(orgId) ?? 'page.request.variantInvalid'
    : 'page.request.variantInvalid';
};
