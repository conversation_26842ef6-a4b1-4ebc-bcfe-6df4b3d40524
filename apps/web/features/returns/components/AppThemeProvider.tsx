import mediaQuery from 'css-mediaquery';
import {
  PropsWithChildren,
  createContext,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useState,
} from 'react';
import { UAParser } from 'ua-parser-js';

import { AppProvider, DynamicTheme } from '@aftership/astra';
import { defaultFont } from '@aftership/returns-logics-core';

import { getUserTheme } from '@/styles/theme';

export interface AppTheme {
  primaryFont: string;
  secondaryFont: string;
  primaryColor: string;
}

export interface AppThemeContextValue {
  theme: AppTheme;
  updateTheme: (theme: AppTheme) => void;
  deviceType?: 'mobile' | 'desktop';
}

export interface AppThemeProviderProps extends PropsWithChildren {
  theme: AppTheme;
  uaString?: string;
}

export const AppThemeContext = createContext<AppThemeContextValue | null>(null);

export const AppThemeProvider: React.FC<AppThemeProviderProps> = ({
  theme,
  uaString,
  children,
}) => {
  const { primaryFont, secondaryFont, primaryColor } = theme;
  const [userThemeConfig, setUserThemeConfig] = useState<DynamicTheme | undefined>();

  const updateTheme = useCallback(({ primaryColor, secondaryFont, primaryFont }: AppTheme) => {
    return getUserTheme({
      primaryFont: `"${primaryFont}", "${defaultFont.name}"`,
      secondaryFont: `"${secondaryFont}", "${defaultFont.name}"`,
      primaryColor,
    });
  }, []);

  const device = useMemo(() => {
    const parser = new UAParser(uaString);
    const isMobile = parser.getDevice().type === 'mobile';
    return isMobile ? 'mobile' : 'desktop';
  }, [uaString]);

  const ssrMatchMedia = useCallback(
    (query: string) => {
      const isMobile = device === 'mobile';

      return {
        matches: mediaQuery.match(query, {
          // The estimated CSS width of the browser.
          width: isMobile ? '0px' : '840px',
        }),
      };
    },
    [device],
  );

  useEffect(() => {
    setUserThemeConfig(updateTheme({ primaryFont, secondaryFont, primaryColor }));
  }, [primaryFont, secondaryFont, primaryColor, updateTheme]);

  return (
    <AppThemeContext.Provider value={{ theme, updateTheme, deviceType: device }}>
      <AppProvider isRounded theme={userThemeConfig} ssrMatchMedia={ssrMatchMedia}>
        {children}
      </AppProvider>
    </AppThemeContext.Provider>
  );
};

export const useAppTheme = (): AppThemeContextValue => {
  const context = useContext(AppThemeContext);
  if (!context) {
    throw new Error('useAppTheme must be used within a AppThemeProvider');
  }
  return context;
};
