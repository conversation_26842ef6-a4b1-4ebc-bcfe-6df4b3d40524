import { ReactNode, createContext, useEffect, useMemo, useState } from 'react';

import { useApp, useMediaQuery } from '@aftership/astra';

import getTrackerInstance from '@/utils/tracker';
import { DeviceType } from '@/utils/tracker/consts';

import { useAppTheme } from './AppThemeProvider';

export enum PreviewLayout {
  mobile,
  desktop,
}

interface BreakPointContextProps {
  isPreview?: boolean;
  children: ReactNode;
}
interface BreakPointContextValue {
  device: {
    mobile: boolean;
    tablet: boolean;
    desktop: boolean;
    ultra: boolean;
  };
  updatePreviewLayout?: (layout: PreviewLayout) => void;
}

export const BreakPointContext = createContext<BreakPointContextValue>({
  device: { mobile: false, tablet: false, desktop: true, ultra: false },
});

const BreakPointProvider = ({ isPreview = false, children }: BreakPointContextProps) => {
  const [previewLayout, setPreviewLayout] = useState<PreviewLayout>(PreviewLayout.desktop);
  const { breakpoints } = useApp();
  const { deviceType } = useAppTheme();
  const [mobile, tablet, desktop, ultra] = useMediaQuery([
    breakpoints.down('m'),
    breakpoints.only('m'),
    breakpoints.only('l'),
    breakpoints.up('xl'),
  ]);

  const value = useMemo(() => {
    return {
      // preview 模式以 message bridge 传过来的 layout为准
      device: isPreview
        ? {
            mobile: previewLayout === PreviewLayout.mobile,
            tablet: false,
            desktop: previewLayout === PreviewLayout.desktop,
            ultra: false,
          }
        : { mobile, tablet, desktop, ultra },
      updatePreviewLayout: setPreviewLayout,
    };
  }, [mobile, tablet, desktop, ultra, previewLayout, isPreview]);

  useEffect(() => {
    // Delay to debounce to prevent push event repeatedly
    // when Page component mount and unmount quickly
    const timeoutId = setTimeout(() => {
      getTrackerInstance().updateExtraDataByDeviceMode(
        deviceType === 'mobile' ? DeviceType.mobile : DeviceType.desktop,
      );

      if (typeof window !== 'undefined' && window.screen) {
        getTrackerInstance().updateExtraDataByScreenResolution(
          window.screen.width + 'x' + window.screen.height,
        );
      }
    }, 0);
    return () => {
      clearTimeout(timeoutId);
    };
  }, [deviceType]);

  return <BreakPointContext.Provider value={value}>{children}</BreakPointContext.Provider>;
};

export default BreakPointProvider;
