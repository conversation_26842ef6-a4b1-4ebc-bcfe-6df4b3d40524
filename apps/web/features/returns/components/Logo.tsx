import Image from 'next/image';

import { Typography } from '@aftership/astra';
import { type ShopAsset } from '@aftership/returns-logics-core';

const LOGO_HIGHT_MAX = 36;
const LOGO_WIDTH_MAX = 200;

export interface ILogoProps {
  storeName: string;
  logoImage?: ShopAsset;
}

export const Logo = ({ logoImage, storeName }: ILogoProps) => {
  const { height = 0, width = 0, src } = logoImage || {};
  const imageWidth = Math.min(
    (height >= LOGO_HIGHT_MAX ? (width / height) * LOGO_HIGHT_MAX : width) || LOGO_WIDTH_MAX,
    LOGO_WIDTH_MAX,
  );
  const imageHeight = Math.min(height || LOGO_HIGHT_MAX, LOGO_HIGHT_MAX);

  return logoImage ? (
    <Image
      src={src ?? (logoImage?.name && URL.createObjectURL(logoImage as unknown as File))}
      style={{ width: imageWidth, height: imageHeight }}
      width={imageWidth}
      height={imageHeight}
      alt='store_logo'
    />
  ) : (
    <Typography variant='heading2Xs'>{storeName}</Typography>
  );
};

export default Logo;
