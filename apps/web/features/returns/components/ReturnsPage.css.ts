import { style } from '@vanilla-extract/css';

export const containerClassName = style({
  display: 'flex',
  flex: 1,
  alignItems: 'center',
  justifyContent: 'center',
  overflow: 'auto',
  // @ts-ignore
  pointerEvents: 'auto !important', // preview 模式下，container 会被设置为 pointer-events: none，导致无法滚动，因此这儿需要重置
});

export const returnsPageContainerInAppProxy = style({
  overflow: 'auto',
  height: 'var(--proxy-content-height)', // 在 useProxyWrapperStyle 中计算出来的值
});

export const returnsPageContainer = style({
  height: '100dvh',
});

export const backgroundImageClassName = style({
  position: 'absolute',
  top: 0,
  left: 0,
  width: '100%',
  height: '100%',
  objectFit: 'cover',
  zIndex: -1,
});
