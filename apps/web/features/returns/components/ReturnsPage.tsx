import clsx from 'clsx';
import Image from 'next/image';
import React, { PropsWithChildren, ReactNode, useEffect, useMemo, useState } from 'react';

import { Box, Spinner, Stack, useIsMounted } from '@aftership/astra';

import { useScrollUpdate } from '@/features/atta/useSrollUpdate';
import useDevice from '@/hooks/useDevice';
import { isIframe } from '@/utils/common';
import getTrackerInstance from '@/utils/tracker';

import {
  backgroundImageClassName,
  containerClassName,
  returnsPageContainer,
  returnsPageContainerInAppProxy,
} from './ReturnsPage.css';
import ReturnsPageFooter from './ReturnsPageFooter';
import ReturnsPageHeader from './ReturnsPageHeader';
import { PageLayoutContext } from './context/PageLayoutContext';

import { PageType } from '../../../utils/tracker/consts';
import { useLayoutHidden } from '../hooks/useLayoutHidden';
import { useReturns } from '../hooks/useReturns';
import { useShopInfo } from '../hooks/useShopInfo';
import { useSyncXStateAndRoute } from '../hooks/useSyncXStateAndRoute';

interface ReturnsPageProps {
  children: ReactNode;
}

const BackgroundImage = ({ url }: { url?: string }) => {
  const heroImage = useMemo(() => {
    return url ?? require('@/assets/returns-page-background.png').default?.src;
  }, [url]);
  return (
    <Image
      fill
      sizes='100vw'
      src={heroImage}
      className={backgroundImageClassName}
      alt='returns page background'
    />
  );
};

export const Header: React.FC = () => {
  const { hiddenHeader } = useLayoutHidden();
  /**
   * @argument isCompact
   * @description 是否为紧凑模式布局，该模式下不展示 header 和 footer
   */
  const { isCompact } = useReturns() ?? {};
  const shopInfo = useShopInfo();
  return <>{!hiddenHeader && !isCompact && <ReturnsPageHeader shopInfo={shopInfo} />}</>;
};

export const Footer: React.FC = () => {
  const { hiddenFooter } = useLayoutHidden();
  const { isCompact } = useReturns() ?? {};
  const shopInfo = useShopInfo();
  return <>{!hiddenFooter && !isCompact && <ReturnsPageFooter shopInfo={shopInfo} />}</>;
};

export const Container: React.FC<PropsWithChildren> = ({ children }) => {
  const isMounted = useIsMounted();
  const { fullScreen } = useLayoutHidden();
  const { paddingTop, paddingBottom } = useDevice();
  const [layoutElement, setLayoutElement] = useState<HTMLDivElement | null>(null);

  const scrollClassName = useScrollUpdate();

  return (
    <PageLayoutContext.Provider value={{ layoutElement }}>
      {isMounted ? (
        <Box
          flex={1}
          height={0}
          className={clsx(fullScreen ? undefined : containerClassName, scrollClassName)}
          paddingTop={fullScreen ? undefined : paddingTop}
          paddingBottom={fullScreen ? undefined : paddingBottom}
          ref={setLayoutElement}
        >
          <Stack
            direction='column'
            style={{
              height: fullScreen ? '100%' : undefined,
              // https://stackoverflow.com/questions/33454533/cant-scroll-to-top-of-flex-item-that-is-overflowing-container
              margin: fullScreen ? undefined : 'auto',
            }}
          >
            {children}
          </Stack>
        </Box>
      ) : (
        <Stack flex={1} style={{ height: 0 }} align='center' justify='center'>
          <Spinner color='primary' />
        </Stack>
      )}
    </PageLayoutContext.Provider>
  );
};

export const Layout: React.FC<PropsWithChildren> = ({ children }) => {
  const { isAppProxy } = useReturns() ?? {};
  const shopInfo = useShopInfo();
  const heroImage = shopInfo.hero_image?.src;

  useEffect(() => {
    // Delay to debounce to prevent push event repeatedly
    // when Page component mount and unmount quickly
    const timeoutId = setTimeout(() => {
      let pageType = PageType.shopper;

      if (isAppProxy) pageType = PageType.appProxy;
      if (isIframe()) pageType = PageType.iframe;

      getTrackerInstance().updateExtraDataByPagetType(pageType);
    }, 0);
    return () => {
      clearTimeout(timeoutId);
    };
  }, [isAppProxy]);

  return (
    <>
      <Stack
        direction='column'
        style={{ position: 'relative', zIndex: 0 }}
        className={isAppProxy ? returnsPageContainerInAppProxy : returnsPageContainer}
      >
        <BackgroundImage url={heroImage} />
        {children}
      </Stack>
    </>
  );
};

const ReturnsPage = ({ children }: ReturnsPageProps) => {
  useSyncXStateAndRoute();
  return (
    <Layout>
      <Header />
      <Container>{children}</Container>
      <Footer />
    </Layout>
  );
};

export default ReturnsPage;
