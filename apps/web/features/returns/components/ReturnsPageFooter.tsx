import { Fragment } from 'react';

import { Box, Icon, Link, Stack, Typography } from '@aftership/astra';
import { tokenVars } from '@aftership/astra-tokens/Contract.css';
import { Shops } from '@aftership/returns-logics-core';

import useShopUrls from '../hooks/useShopUrls';

const { Space, Color, Radius } = tokenVars.Semantic;

interface ReturnsPageFooterProps {
  shopInfo: Shops;
  isPreview?: boolean;
}

const ReturnsPageFooter = ({ shopInfo }: ReturnsPageFooterProps) => {
  const { socialLinks, footerLinks } = useShopUrls(shopInfo);

  return (
    <Box backgroundColor={Color.Brand.Primary} padding='0 40px'>
      <Stack align='center' style={{ height: '80px' }}>
        <Stack flex={1} align='center' gap='s'>
          {footerLinks.map((link, index) => (
            <Fragment key={link.name}>
              <Link
                href={link.url}
                style={{ color: Color.Text.White_Alpha_Primary }}
                showUnderline={false}
              >
                <Typography variant='bodySm' style={{ textAlign: 'left' }}>
                  {link.name}
                </Typography>
              </Link>
              {index < footerLinks.length - 1 && (
                <Box
                  backgroundColor={Color.Bg.Body}
                  width={Space['2Xs']}
                  height={Space['2Xs']}
                  borderRadius={Radius.S}
                />
              )}
            </Fragment>
          ))}
        </Stack>

        <Stack gap='2xl' align='center'>
          {socialLinks.map((socialLink) => (
            <Link href={socialLink.url} key={socialLink.url}>
              <Icon source={socialLink.icon} size={Space.L} style={{ color: Color.Bg.Body }} />
            </Link>
          ))}
        </Stack>
      </Stack>
    </Box>
  );
};

export default ReturnsPageFooter;
