import { type Shops } from '@aftership/returns-logics-core';

import useDevice from '@/hooks/useDevice';

import ReturnsPageHeaderDesktop from './ReturnsPageHeaderDesktop';
import ReturnsPageHeaderMobile from './ReturnsPageHeaderMobile';

interface ReturnsPageHeaderProps {
  shopInfo: Shops;
}

const ReturnsPageHeader = ({ shopInfo }: ReturnsPageHeaderProps) => {
  const isMobile = useDevice().mobile;

  if (isMobile) {
    return <ReturnsPageHeaderMobile shopInfo={shopInfo} />;
  }
  return <ReturnsPageHeaderDesktop shopInfo={shopInfo} />;
};

export default ReturnsPageHeader;
