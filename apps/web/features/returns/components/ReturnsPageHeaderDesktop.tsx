import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';

import { ActionMenu, Box, Icon, Link, Stack, Typography } from '@aftership/astra';
import { GlobalOutlined } from '@aftership/astra-icons';
import { tokenVars } from '@aftership/astra-tokens/Contract.css';
import { LogoPosition, type Shops } from '@aftership/returns-logics-core';

import { FullScreenLoading } from '@/components/FullScreenLoading';
import { useChangeLanguage } from '@/features/returns/hooks/useChangeLanguage';
import { getNameForLanguage } from '@/i18n/utils';

import Logo from './Logo';
import { hiddenBox } from './ReturnsPageHeader.css';
import { Button } from '@/components/Button';

const { Space, Color } = tokenVars.Semantic;

interface ReturnsPageHeaderProps {
  shopInfo: Shops;
}

const ReturnsPageHeaderDesktop = ({ shopInfo }: ReturnsPageHeaderProps) => {
  const {
    logo_image,
    store_name,
    menu_items = [],
    organization,
    store_url,
    languages,
    logo_position_on_large_screen,
  } = shopInfo;

  const { i18n } = useTranslation();
  const { language: currentLanguage } = i18n;
  const { loading, changeLanguage } = useChangeLanguage(organization?.id);

  const logoDirection = useMemo(() => {
    switch (logo_position_on_large_screen) {
      case LogoPosition.MiddleLeft:
        return 'row';
      case LogoPosition.TopLeft:
      case LogoPosition.TopCenter:
        return 'column';
    }
  }, [logo_position_on_large_screen]);
  const logoJustifyContent = useMemo(() => {
    switch (logo_position_on_large_screen) {
      case LogoPosition.MiddleLeft:
      case LogoPosition.TopLeft:
        return 'start';
      case LogoPosition.TopCenter:
        return 'center';
    }
  }, [logo_position_on_large_screen]);

  return (
    <>
      <Box backgroundColor={Color.Bg.Body} padding='0 80px'>
        <Stack align='center' style={{ height: '64px' }}>
          {logoJustifyContent === 'center' && (
            <>
              <Box className={hiddenBox}>
                <ActionMenu
                  items={languages.map((language) => ({
                    label: getNameForLanguage(language),
                    value: language,
                  }))}
                  onAction={(language) => changeLanguage(String(language))}
                >
                  <Stack align='center' gap='xs' style={{ position: 'relative' }}>
                    <Icon color='primary' source={GlobalOutlined} size={Space.Xl} />
                    <Typography variant='bodyLgSemibold'>
                      {getNameForLanguage(currentLanguage)}
                    </Typography>
                  </Stack>
                </ActionMenu>
              </Box>
              <Box flex={1} />
            </>
          )}

          <Stack
            align={
              logoJustifyContent === 'start' && logoDirection === 'column' ? 'start' : 'center'
            }
            direction={logoDirection}
            gap={logoDirection === 'row' ? '2xl' : 'none'}
          >
            <Link href={store_url} showUnderline={false} style={{ color: Color.Text.Primary }}>
              <Logo storeName={store_name} logoImage={logo_image} />
            </Link>
            <Stack align='center' gap='2xl'>
              {menu_items.map((item) => (
                <Link
                  key={item.url}
                  href={item.url}
                  showUnderline={false}
                  style={{ color: Color.Text.Secondary }}
                >
                  <Typography variant='bodyMd'>{item.name}</Typography>
                </Link>
              ))}
            </Stack>
          </Stack>
          <Box flex={1} />

          <ActionMenu
            placement='bottom'
            items={languages.map((language) => ({
              label: getNameForLanguage(language),
              value: language,
            }))}
            onAction={(language) => changeLanguage(String(language))}
          >
            <Button variant='plain' size='small' color='default'>
              <Stack align='center' gap='2xs' style={{ position: 'relative' }}>
                <Icon color='primary' source={GlobalOutlined} size={Space.L} />
                <Typography variant='bodyMd'>{getNameForLanguage(currentLanguage)}</Typography>
              </Stack>
            </Button>
          </ActionMenu>
        </Stack>
      </Box>
      <FullScreenLoading loading={loading} />
    </>
  );
};

export default ReturnsPageHeaderDesktop;
