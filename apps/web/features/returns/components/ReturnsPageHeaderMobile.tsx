import Image from 'next/image';
import { Fragment, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Box, Drawer, Icon, Link, Pressable, Stack, Typography } from '@aftership/astra';
import { ChevronRightOutlined, CloseFilled } from '@aftership/astra-icons';
import { tokenVars } from '@aftership/astra-tokens/Contract.css';
import { type Shops } from '@aftership/returns-logics-core';

import { FullScreenLoading } from '@/components/FullScreenLoading';
import { ListBox, ListBoxItem } from '@/components/ListBox';
import { Modal } from '@/components/Modal';
import { ScrollBox } from '@/components/ScrollBox';
import { useChangeLanguage } from '@/features/returns/hooks/useChangeLanguage';
import { getNameForLanguage } from '@/i18n/utils';

import Logo from './Logo';
import { headerBorderClassName } from './ReturnsPageHeader.css';

import useShopUrls from '../hooks/useShopUrls';

const { Space, Color, Radius } = tokenVars.Semantic;
const { Space: PrimitiveSpace } = tokenVars.Primitive;

interface ReturnsPageHeaderProps {
  shopInfo: Shops;
}

const ReturnsPageHeaderMobile = ({ shopInfo }: ReturnsPageHeaderProps) => {
  const {
    logo_image,
    store_name,
    organization,
    languages,
    menu_items = [],
    theme_color,
  } = shopInfo;
  const { t } = useTranslation();
  const { loading, changeLanguage } = useChangeLanguage(organization?.id);
  const { socialLinks, footerLinks } = useShopUrls(shopInfo);
  const [isPopupOpen, setIsPopupOpen] = useState(false);
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const noMenuLayout = menu_items.length === 0;
  const fallbackMenuItems = useMemo(() => {
    return [
      ...socialLinks.map(({ url, name }) => ({
        url,
        name,
      })),
      ...footerLinks.map(({ url, name }) => ({
        url,
        name,
      })),
    ];
  }, [socialLinks, footerLinks]);

  return (
    <>
      <Box
        backgroundColor={Color.Bg.Body}
        padding={`0 ${Space.M}`}
        className={headerBorderClassName}
      >
        <Stack align='center' gap='m' style={{ height: 80, rowGap: Space['2Xs'] }}>
          <Logo storeName={store_name} logoImage={logo_image} />
          <Box flex={1} />
          <Pressable onPress={() => setIsPopupOpen(true)}>
            <Image
              src={require('@/assets/i18n.png').default?.src}
              width={36}
              height={36}
              alt='i18n'
            />
          </Pressable>
          <Pressable onPress={() => setIsDrawerOpen(true)}>
            <Image
              src={require('@/assets/menu.png').default?.src}
              width={36}
              height={36}
              alt='menu'
            />
          </Pressable>
        </Stack>
        <Modal
          isOpen={isPopupOpen}
          title={t('page.request.language')}
          onClose={() => setIsPopupOpen(false)}
        >
          <ScrollBox>
            <ListBox
              selectionMode='single'
              rowGap={Space.S}
              items={languages.map((language, index) => ({
                id: index,
                name: language,
              }))}
            >
              {(item) => {
                return (
                  <ListBoxItem
                    style={{ padding: Space.M }}
                    onAction={() => {
                      changeLanguage(String(item.name));
                      setIsPopupOpen(false);
                    }}
                  >
                    <Stack align='center' gap='xs'>
                      <Typography variant='bodyLg'>{getNameForLanguage(item.name)}</Typography>
                      <Box flex={1} />
                      <Icon source={ChevronRightOutlined} size={Space.M} />
                    </Stack>
                  </ListBoxItem>
                );
              }}
            </ListBox>
          </ScrollBox>
        </Modal>
        <Drawer
          isFullScreen
          isClosable={false}
          isOpen={isDrawerOpen}
          onOpenChange={(isOpen) => !isOpen && setIsDrawerOpen(false)}
        >
          <Box height='100dvh' backgroundColor={theme_color} paddingX={Space.M} overflow='auto'>
            <Stack direction='column' style={{ height: '100%' }}>
              <Stack justify='end' style={{ paddingBlock: Space.Xl }}>
                <Pressable onPress={() => setIsDrawerOpen(false)}>
                  <Icon
                    source={CloseFilled}
                    size={Space['2Xl']}
                    style={{ color: Color.Text.White }}
                  />
                </Pressable>
              </Stack>
              <Stack justify='center'>
                <Stack direction='column' gap='m' style={{ paddingBlockEnd: Space.Xl }}>
                  {(noMenuLayout ? fallbackMenuItems : menu_items).map((item) => (
                    <Link
                      key={item.url}
                      href={item.url}
                      style={{ color: Color.Bg.Body }}
                      showUnderline={false}
                    >
                      <Typography variant='headingSm'>{item.name}</Typography>
                    </Link>
                  ))}
                </Stack>
              </Stack>
              <Box flex={1} />
              {!noMenuLayout && (
                <Stack
                  direction='column'
                  gap='xl'
                  align='center'
                  style={{ paddingBlockEnd: PrimitiveSpace[1200] }}
                >
                  <Stack gap='xs' align='center'>
                    {socialLinks.map(({ url, icon }) => (
                      <Link href={url} key={url} style={{ color: 'white' }} showUnderline={false}>
                        <Icon source={icon} size={Space.Xl} color='inherit' />
                      </Link>
                    ))}
                  </Stack>
                  <Stack wrap gap='s' align='center' style={{ paddingInline: Space['2Xl'] }}>
                    {footerLinks.map(({ url, name }, index) => (
                      <Fragment key={name}>
                        <Link href={url} style={{ color: Color.Bg.Body }} showUnderline={false}>
                          <Typography variant='bodySm'>{name}</Typography>
                        </Link>
                        {index < footerLinks.length - 1 && (
                          <Box
                            backgroundColor={Color.Bg.Body}
                            width={Space['2Xs']}
                            height={Space['2Xs']}
                            borderRadius={Radius.S}
                          />
                        )}
                      </Fragment>
                    ))}
                  </Stack>
                </Stack>
              )}
            </Stack>
          </Box>
        </Drawer>
        <FullScreenLoading loading={loading} />
      </Box>
    </>
  );
};

export default ReturnsPageHeaderMobile;
