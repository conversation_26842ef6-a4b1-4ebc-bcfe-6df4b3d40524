import { useState } from 'react';
import { useTranslation } from 'react-i18next';

import { fetchLanguageResource } from '@/i18n/dynamic';
import { I18NEXT_NAMESPACE } from '@/i18n/utils';

export const languageCookieKey = 'rc_language';

export const useChangeLanguage = (uuid: string) => {
  const [loading, setLoading] = useState(false);
  const { i18n, t } = useTranslation();

  const handleChangeLanguage = async (selectedLang: string, onError?: (error: Error) => void) => {
    try {
      setLoading(true);
      if (!i18n.hasResourceBundle(selectedLang, I18NEXT_NAMESPACE)) {
        const resource = await fetchLanguageResource(uuid, selectedLang);
        i18n.addResourceBundle(selectedLang, I18NEXT_NAMESPACE, resource, false, true);
      }
      await i18n.changeLanguage(selectedLang);
      document.documentElement.lang = selectedLang;
      // 设置 cookie 过期时间为 1 年
      const expirationDate = new Date();
      expirationDate.setFullYear(expirationDate.getFullYear() + 1);
      document.cookie = `${languageCookieKey}=${selectedLang}; path=/; SameSite=None; Secure; expires=${expirationDate.toUTCString()}`;
    } catch (e) {
      onError?.(new Error(t('page.common.tryAgain')));
    } finally {
      setLoading(false);
    }
  };

  return {
    changeLanguage: handleChangeLanguage,
    loading,
  };
};
