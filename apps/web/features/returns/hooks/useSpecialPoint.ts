import { useMemo } from 'react';

import { useMediaQuery } from '@aftership/astra';

import { useUniversalRouting } from './useUniversalRouting';

export const SpecialPafeBreakPoint = {
  review: {
    min: 801,
    max: 1080,
    regex: /^\/review.*/,
  },
  returnDetail: {
    min: 801,
    max: 1080,
    regex: /^\/return-detail.*/,
  },
};

const matchPath = (pathname: string, regex: RegExp) => regex.test(pathname);

const useSpecialPoint = () => {
  const router = useUniversalRouting();
  const [specialDesktopForReview, specialDesktopForReturnDetail] = useMediaQuery([
    `only screen and (min-width : ${SpecialPafeBreakPoint.review.min}px) and (max-width : ${SpecialPafeBreakPoint.review.max}px)`,
    `only screen and (min-width : ${SpecialPafeBreakPoint.returnDetail.min}px) and (max-width : ${SpecialPafeBreakPoint.returnDetail.max}px)`,
  ]);
  const specialDesktop = useMemo(
    () => ({
      review:
        specialDesktopForReview && matchPath(router.pathname, SpecialPafeBreakPoint.review.regex),
      returnDetail:
        specialDesktopForReturnDetail &&
        matchPath(router.pathname, SpecialPafeBreakPoint.returnDetail.regex),
    }),
    [router.pathname, specialDesktopForReturnDetail, specialDesktopForReview],
  );

  return {
    specialDesktop,
  };
};

export default useSpecialPoint;
