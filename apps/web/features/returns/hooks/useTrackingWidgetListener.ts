import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { EventName } from '@aftership/preview-kit/business/rc';

import { useMessageBridge } from './useMessageBridge';

export interface TrackingWidgetConfig {
  button_text: string;
  tracking_page_id: string;
  enabled: boolean;
}

export const useTrackingWidgetListener = () => {
  const { t } = useTranslation();
  const messageBridge = useMessageBridge();
  const [trackingConfig, setTrackingConfig] = useState<TrackingWidgetConfig>({
    button_text: t('page.widget.trackingWidget.buttonText', 'Track your return'),
    tracking_page_id: '',
    enabled: false,
  });

  useEffect(() => {
    // 监听 UpdatePreviewData 消息
    const removeEventListener = messageBridge.onMessage({
      type: EventName.UpdatePreviewData,
      callback: (data: any) => {
        if (data.type === 'UpdateTrackingWidget') {
          // eslint-disable-next-line no-console
          console.log(
            '%c [ 💬 client - receive tracking widget config ]',
            'font-size:13px; background:lightblue; color:#0066cc;',
            data.payload,
          );
          setTrackingConfig(data.payload);
        }
      },
    });

    return () => {
      removeEventListener();
    };
  }, [messageBridge]);

  return { trackingConfig };
};
