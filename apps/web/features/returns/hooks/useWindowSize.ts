import { useSyncExternalStore } from 'react';

function subscribe(callback: (this: Window, ev: UIEvent) => any) {
  window.addEventListener('resize', callback);
  return () => {
    window.removeEventListener('resize', callback);
  };
}
function getSnapshot() {
  return window.innerWidth;
}

function getServerSnapshot() {
  return 0;
}

export default function useWindowSize() {
  return useSyncExternalStore(subscribe, getSnapshot, getServerSnapshot);
}
