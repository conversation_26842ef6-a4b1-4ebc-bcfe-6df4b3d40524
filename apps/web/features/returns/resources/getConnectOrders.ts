import getConfig from 'next/config';

import { connectorHostName } from '@/constants/hostname';
import { request } from '@/utils/request';

interface OrderItem {
  image_urls: string[];
  title: string;
  quantity: number;
  discounted_price_incl_tax_set: {
    // 用户下单币种的价格
    presentment_money: {
      amount: number;
      currency: string;
    };
  };
  variant_title: string;
}

interface Order {
  order_number: string;
  created_at: string;
  // 通过 csv 导入的情况可能会不提供相关信息
  metrics?: {
    placed_at: string;
    updated_at: string;
  };
  items: OrderItem[];
  customer: {
    // 在不同平台的 id 可能相同
    external_id: string;
    // 当不同平台的 id 相同时，email 也考虑多个的情况，所以这里是数组
    emails: string[];
  };
}

export interface ConnectorOrders {
  orders: Order[] | null;
}

const {
  serverRuntimeConfig: { AM_API_KEY },
} = getConfig();

export function getConnectOrders({ customerId, organizationId, appKey }: Record<string, string>) {
  if (!customerId || !organizationId || !appKey) {
    return { orders: null };
  }

  const params = {
    limit: 100,
    app_key: appKey,
    app_name: 'returnscenter',
    app_platform: 'shopify',
    organization_id: organizationId,
    external_customer_ids: customerId,
  };

  const uri = `${connectorHostName}/orders?${Object.entries(params)
    .map((item) => item.join('='))
    .join('&')}`;

  return request<ConnectorOrders>(uri, {
    headers: { 'am-api-key': AM_API_KEY },
  }).then((response) => response.data || Promise.reject(response));
}
