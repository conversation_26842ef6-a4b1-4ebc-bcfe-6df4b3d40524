import { FiledBy, Resolution } from '@aftership/returns-logics-core';

type ExternalConfigResolutionType =
  | Resolution.ReplaceTheSameItem
  | Resolution.Refundid
  | Resolution.StoreCredit
  | Resolution.OriginalPayment;

export interface MerchantSession {
  customer_email: string;
  order_number: string;
  token: string;
}

export interface IMerchantModeConfig {
  trace_id: string;
  session: MerchantSession;
}

export interface IExternalConfig {
  /**
   * https://aftership.atlassian.net/browse/RTC-15582
   *
   * ["exchange"]
   * external_config=JTdCJTIyYWN0aXZlZFJlc29sdXRpb25PcHRpb25zJTIyJTNBJTVCJTIyZXhjaGFuZ2UlMjIlNUQlN0Q%3D
   *
   * ["store_credit"]
   * external_config=JTdCJTIyYWN0aXZlZFJlc29sdXRpb25PcHRpb25zJTIyJTNBJTVCJTIyc3RvcmVfY3JlZGl0JTIyJTVEJTdE
   *
   * ["original_payment"]
   * external_config=JTdCJTIyYWN0aXZlZFJlc29sdXRpb25PcHRpb25zJTIyJTNBJTVCJTIyb3JpZ2luYWxfcGF5bWVudCUyMiU1RCU3RA%3D%3D
   *
   * ["store_credit", "original_payment"]
   *external_config=JTdCJTIyYWN0aXZlZFJlc29sdXRpb25PcHRpb25zJTIyJTNBJTVCJTIyb3JpZ2luYWxfcGF5bWVudCUyMiUyQyUyMnN0b3JlX2NyZWRpdCUyMiU1RCU3RA%3D%3D
   *
   * ["original_payment", "store_credit", "exchange"]
   *external_config=JTdCJTIyYWN0aXZlZFJlc29sdXRpb25PcHRpb25zJTIyJTNBJTVCJTIyb3JpZ2luYWxfcGF5bWVudCUyMiUyQyUyMnN0b3JlX2NyZWRpdCUyMiUyQyUyMmV4Y2hhbmdlJTIyJTVEJTdE
   */
  activedResolutionOptions?: ExternalConfigResolutionType[];
  merchantModeConfig?: IMerchantModeConfig;
}

export interface IQueryParamOrderInfo {
  order_number: string;
  email?: string;
  postal_code?: string;
  phone_number?: string;
  rma_id?: string;
  type: string;
  filed_by?: FiledBy;
  intention_id?: string;
}
