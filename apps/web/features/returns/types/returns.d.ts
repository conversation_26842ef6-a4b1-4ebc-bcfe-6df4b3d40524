import { ReturnsPageAccessCode, ReturnsPageAccessStatus } from './features/returns/utils/constants';

declare global {
  namespace Returns {
    interface AppProxyInfo {
      shopifyProxyMode: boolean;
      pathPrefix: string;
      shop: string;
      customizedConfig?: {
        headerSelector: string;
      };
    }
    interface ReturnsPageAccessResult {
      status: ReturnsPageAccessStatus;
      code: ReturnsPageAccessCode | null;
    }
    interface Resource {
      [language: string]: ResourceLanguage;
    }
    interface ResourceLanguage {
      [namespace: string]: ResourceKey;
    }
    type ResourceKey =
      | string
      | {
          [key: string]: any;
        };
  }
}
