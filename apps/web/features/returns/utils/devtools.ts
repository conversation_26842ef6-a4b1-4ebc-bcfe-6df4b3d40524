const convertRefToObject = (snapshot: Record<string, any>) => {
  if (!snapshot) return null;

  const result: Record<string, any> = {
    currentStep: {
      name: snapshot.value,
      isLoading: snapshot?.hasTag?.('loading'),
    },
    context: snapshot.context ?? {},
    children: {},
  };

  for (const id in snapshot.children) {
    const childRef = snapshot?.children?.[id];
    result.children[id] = convertRefToObject(childRef?.getSnapshot?.());
  }

  return result;
};
export const providerDevToolOrigin = (actor: Record<string, any>) => {
  if (process.env.NODE_ENV === 'development' && !(typeof window === 'undefined')) {
    const sendSnapshot = () => {
      const snapshot = actor?.getSnapshot();
      let snapshotCopy = convertRefToObject(Object.assign({}, snapshot ?? {}));
      window?.postMessage?.(
        {
          type: 'xstate-message',
          data: JSON.stringify(snapshotCopy),
        },
        // 当前源
        window.location.origin,
      );
    };
    const childrenSuscribe = (childrenActor: any, snapshot: any) => {
      if (!snapshot) return;
      childrenActor?.subscribe((s: any) => {
        requestIdleCallback(() => {
          sendSnapshot();
        });
        const children = s?.children;
        if (children) {
          for (const id in children) {
            childrenSuscribe(children[id], children[id]?.getSnapshot?.());
          }
        }
      });
    };
    childrenSuscribe(actor, Object.assign({}, actor?.getSnapshot() ?? {}));
  }
};
