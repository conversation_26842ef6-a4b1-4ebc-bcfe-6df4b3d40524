import { useTranslation } from 'react-i18next';

import { Stack, Typography } from '@aftership/astra';

import { Button } from '@/components/Button';
import MobilePopup from '@/components/MobilePopup';
import { Modal } from '@/components/Modal';
import useDevice from '@/hooks/useDevice';

import ModalButtonGroup from '../ModalButtonGroup/ModalButtonGroup';

export interface AlreadyPaidModalProps {
  isOpen: boolean;
  createLoading: boolean;
  onCreate: () => void;
  onClose: () => void;
  onAlreadyPaid: () => void;
}

const AlreadyPaidModal = ({
  isOpen,
  createLoading,
  onAlreadyPaid,
  onCreate,
  onClose,
}: AlreadyPaidModalProps) => {
  const { t } = useTranslation();
  const { mobile } = useDevice();

  const Container = () => {
    return (
      <Stack justify='center'>
        <Typography variant='bodyLg'>
          {t('popup.description.submittedBefore')}
          {t('popup.description.wantToKeepIt')}
        </Typography>
      </Stack>
    );
  };

  const Footer = () => {
    return (
      <ModalButtonGroup>
        <Button size='large' onPress={onAlreadyPaid}>
          {}
        </Button>
        <Button size='large' onPress={onCreate} variant='basic'>
          {createLoading ? 'loading' : t('popup.request.createNewRequest')}
        </Button>
      </ModalButtonGroup>
    );
  };

  if (mobile) {
    return (
      <MobilePopup
        isOpen={isOpen}
        title={t('popup.description.requestSubmitted')}
        onClose={onClose}
        footer={<Footer />}
      >
        <Container />
      </MobilePopup>
    );
  }
  return (
    <Modal
      isOpen={isOpen}
      title={t('popup.description.requestSubmitted')}
      onClose={onClose}
      primaryAction={{
        content: t('popup.request.yes'),
        onAction: onAlreadyPaid,
      }}
      secondaryAction={{
        content: createLoading ? 'loading' : t('popup.request.createNewRequest'),
        onAction: onCreate,
      }}
    >
      <Container />
    </Modal>
  );
};

export default AlreadyPaidModal;
