import { useMemo } from 'react';
import { Trans, useTranslation } from 'react-i18next';

import { Link, Notice } from '@aftership/astra';
import { ErrorCode } from '@aftership/returns-logics-core';

import { useReturnPreviewFlow } from '../../hooks/useReturnPreviewFlow';

const LabelRateUnavailableBanner = () => {
  const { t } = useTranslation();
  const { reviewError, returnPreviewFlowMatches, returnPreviewFlowDispatch, mainDispatch } =
    useReturnPreviewFlow();
  const isPostReviewError =
    !!returnPreviewFlowMatches?.({ postReturnPreviewActor: 'error' }) ||
    returnPreviewFlowMatches?.({ refetchReturnPreview: 'error' });

  const costOfReturnText = t('page.description.costOfReturns');
  const refreshPage = t('page.action.refreshPage');
  const changeMethod = t('page.action.changeReturnMethod');

  const showLabelCostError = useMemo(
    () => isPostReviewError && reviewError?.code === ErrorCode.ReturnLabelRateUnavailable,
    [isPostReviewError, reviewError?.code],
  );

  return (
    showLabelCostError && (
      <Notice state='error'>
        <Trans
          i18nKey='page.description.getCostOfReturnsFailure'
          values={{
            costOfReturn: costOfReturnText,
            refreshPage,
            changeMethod,
          }}
          components={[
            <Link
              key='page.action.refreshPage'
              onPress={() => {
                returnPreviewFlowDispatch?.({ type: 'REFETCH_PREVIEW' });
              }}
            >
              {refreshPage}
            </Link>,
            <Link
              key='page.action.changeReturnMethod'
              onPress={() => {
                mainDispatch?.({ type: 'GO_TO_RETURN_METHOD' });
              }}
            >
              {changeMethod}
            </Link>,
          ]}
        />
      </Notice>
    )
  );
};

export default LabelRateUnavailableBanner;
