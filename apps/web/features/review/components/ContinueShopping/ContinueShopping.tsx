import { memo } from 'react';
import { useTranslation } from 'react-i18next';

import { Link, Stack } from '@aftership/astra';

import { Button } from '@/components/Button';
import getTrackerInstance from '@/utils/tracker';
import { EventName } from '@/utils/tracker/consts';

import { continueShoppingFontSize } from './ContinueShopping.css';

import { useReturnPreviewFlow } from '../../hooks/useReturnPreviewFlow';

const ContinueShopping = memo(
  ({
    type = 'text',
    buttonVariant = 'basic',
  }: {
    type?: 'button' | 'text';
    buttonVariant?: 'filled' | 'basic';
  }) => {
    const { t } = useTranslation();
    const { isLoading, onStoreUrl, mainDispatch, currentStep } = useReturnPreviewFlow();
    const isButtonLoading = currentStep === 'exchangeOnStore';

    return (
      onStoreUrl && (
        <Stack direction='row' justify='center'>
          {type === 'button' ? (
            <Button
              size='large'
              isFullWidth
              variant={buttonVariant}
              isDisabled={isLoading || isButtonLoading}
              className={continueShoppingFontSize}
              onPress={() => {
                window.location.href = onStoreUrl;
                mainDispatch?.({
                  type: 'GO_TO_EXCHANGE_ON_STORE',
                });
                getTrackerInstance().reportClickEvent({ eventName: EventName.continueShopping });
              }}
            >
              {t('page.action.continueShopping')}
            </Button>
          ) : (
            <Link
              isDisabled={isLoading || isButtonLoading}
              className={continueShoppingFontSize}
              onPress={() => {
                window.location.href = onStoreUrl;
                mainDispatch?.({
                  type: 'GO_TO_EXCHANGE_ON_STORE',
                });
                getTrackerInstance().reportClickEvent({ eventName: EventName.continueShopping });
              }}
            >
              {t('page.action.continueShopping')}
            </Link>
          )}
        </Stack>
      )
    );
  },
);

ContinueShopping.displayName = 'ContinueShopping';

export default ContinueShopping;
