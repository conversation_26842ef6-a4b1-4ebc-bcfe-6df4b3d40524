import { useEffect, useMemo } from 'react';
import { useTranslation } from 'react-i18next';

import {
  PreferDomainType,
  TrackingUrlSubFlowInput,
  trackingUrlSubFlow,
} from '@aftership/returns-logics-core';
import {
  useActorRef,
  useMainFlowContext,
  useSelector as useMainFlowSelector,
  useSelector,
} from 'returns-logics/react';

import { useCachedAppProxy } from '@/features/returns/hooks/useCachedAppProxy';
import { useShopInfo } from '@/hooks/useShopInfo';
import { getPreferDomainType, getTrackingPageId } from '@/utils/tracking';

export const useTrackingUrls = (trackingId?: string) => {
  const { i18n } = useTranslation();
  const shopInfo = useShopInfo();
  const appProxy = useCachedAppProxy();

  // 从主流程获取全局参数
  const mainFlowActorRef = useMainFlowContext();
  const token = useMainFlowSelector(mainFlowActorRef, (state) => state.context.token);
  const orderAppKey = useMainFlowSelector(
    mainFlowActorRef,
    (state) => state.context.request?.orders?.order?.app?.key,
  );

  // 计算 input 参数
  const machineInput = useMemo((): TrackingUrlSubFlowInput => {
    const hostname = typeof window !== 'undefined' ? window.location.hostname : '';

    // 使用工具函数计算 preferDomainType
    const preferDomainType = getPreferDomainType(appProxy.shopifyProxyMode, hostname);

    // 使用工具函数计算 trackingPageId
    const trackingPageId = getTrackingPageId(shopInfo.returnTrackingPageWidgetSchema);

    return {
      organizationId: shopInfo.organization?.id || '',
      storeId: orderAppKey || '',
      token: token || '',
      language: i18n.language,
      preferDomainType,
      trackingPageId,
      ...(preferDomainType === PreferDomainType.CustomDomain && {
        customDomain: hostname,
      }),
    };
  }, [
    shopInfo.organization?.id,
    orderAppKey,
    token,
    i18n.language,
    appProxy.shopifyProxyMode,
    shopInfo.returnTrackingPageWidgetSchema,
  ]);

  // 直接使用状态机
  const actorRef = useActorRef(trackingUrlSubFlow, {
    input: machineInput,
  });

  const { trackingUrl, error, currentState, isLoading } = useSelector(actorRef, (state) => ({
    trackingUrl: state.context?.trackingUrl,
    error: state.context?.error,
    currentState: state.value as string,
    isLoading: state.tags?.has('loading'),
  }));

  // 当 trackingId,语言 变化时，发送获取请求
  useEffect(() => {
    if (trackingId) {
      const hostname = typeof window !== 'undefined' ? window.location.hostname : '';
      const preferDomainType = getPreferDomainType(appProxy.shopifyProxyMode, hostname);

      actorRef.send({
        type: 'GET_TRACKING_URL',
        data: {
          trackingId,
          language: i18n.language,
          preferDomainType,
        },
      });
    }
  }, [trackingId, actorRef, i18n.language, appProxy.shopifyProxyMode]);

  return {
    trackingUrl,
    isLoading,
    error,
    currentState,
    isSuccess: currentState === 'success',
    isError: currentState === 'error',
  };
};
