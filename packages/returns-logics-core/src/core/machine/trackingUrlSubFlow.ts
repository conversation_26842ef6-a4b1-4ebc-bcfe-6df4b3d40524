import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>nap<PERSON><PERSON>rom, assertEvent, assign, setup } from 'xstate';

import { getTrackingUrl } from '../promise/trackingUrl';
import {
  PreferDomainType,
  TrackingUrlSubFlowContext,
  TrackingUrlSubFlowInput,
} from '../types/tracking';

type TrackingUrlSubFlowEvents = {
  type: 'GET_TRACKING_URL';
  data: {
    trackingId: string;
    language?: string;
    preferDomainType?: PreferDomainType;
  };
};

export const trackingUrlSubFlow = setup({
  types: {
    context: {} as TrackingUrlSubFlowContext,
    input: {} as TrackingUrlSubFlowInput,
    events: {} as TrackingUrlSubFlowEvents,
  },
  actors: {
    getTrackingUrl,
  },
  actions: {
    assignRequestParams: assign({
      trackingId: ({ event }) => {
        assertEvent(event, 'GET_TRACKING_URL');
        return event.data.trackingId;
      },
      language: ({ event, context }) => {
        assertEvent(event, 'GET_TRACKING_URL');
        return event.data.language ?? context.language;
      },
      preferDomainType: ({ event, context }) => {
        assertEvent(event, 'GET_TRACKING_URL');
        return event.data.preferDomainType ?? context.preferDomainType;
      },
    }),
  },
  guards: {
    hasRequiredParams: ({ context }) => {
      return !!(
        context.organizationId &&
        context.storeId &&
        context.token &&
        context.trackingPageId
      );
    },
  },
}).createMachine({
  id: 'trackingUrlSubFlow',
  context: ({ input }) => ({
    organizationId: input.organizationId,
    storeId: input.storeId,
    token: input.token,
    language: input.language || 'en',
    preferDomainType: input.preferDomainType || PreferDomainType.AftershipDomain,
    trackingPageId: input.trackingPageId || '',
    customDomain: input.customDomain,
    trackingId: undefined,
    trackingUrl: undefined,
    error: undefined,
  }),
  initial: 'idle',
  states: {
    idle: {
      on: {
        GET_TRACKING_URL: {
          target: 'loading',
          guard: 'hasRequiredParams',
          actions: 'assignRequestParams',
        },
      },
    },
    loading: {
      tags: ['loading'],
      on: {
        GET_TRACKING_URL: {
          target: 'loading',
          guard: 'hasRequiredParams',
          actions: 'assignRequestParams',
          reenter: true,
        },
      },
      invoke: {
        src: 'getTrackingUrl',
        input: ({ context }) => {
          const trackingId = context.trackingId;
          if (!trackingId) {
            throw new Error('trackingId is required');
          }

          const language = context.language;
          const preferDomainType = context.preferDomainType;

          return {
            organization_id: context.organizationId,
            token: context.token,
            scenario: 'branded-returns-page' as const,
            order: { store_id: context.storeId },
            tracking: { id: trackingId },
            prefer_domain_type: preferDomainType,
            tracking_page_id: context.trackingPageId,
            language,
            ...(preferDomainType === PreferDomainType.CustomDomain && {
              custom_domain: context.customDomain,
            }),
          };
        },
        onDone: {
          target: 'success',
          actions: assign({
            trackingUrl: ({ event }) => event.output?.url ?? undefined,
            error: undefined,
          }),
        },
        onError: {
          target: 'error',
          actions: assign({
            error: ({ event }) => event.error,
            trackingUrl: undefined,
          }),
        },
      },
    },
    success: {
      on: {
        GET_TRACKING_URL: {
          target: 'loading',
          actions: 'assignRequestParams',
        },
      },
    },
    error: {
      on: {
        GET_TRACKING_URL: {
          target: 'loading',
          actions: 'assignRequestParams',
        },
      },
    },
  },
});

export type TrackingUrlSubFlowActorRef = ActorRefFrom<typeof trackingUrlSubFlow>;
export type TrackingUrlSubFlowSnapshot = SnapshotFrom<typeof trackingUrlSubFlow>;
