.container {
    background: #ffffff;
}

.content {
    display: flex;
    flex-direction: column;
    gap: 16px;
    padding: 20px;
}

.title {
    font-family: 'Inter', sans-serif;
    font-weight: 600;
    font-size: 16px;
    line-height: 24px;
    color: #202223;
    margin: 0;
}

.previewContainer {
    height: 172px;
    width: 100%;
    position: relative;
}

.previewBackground {
    width: 100%;
    height: 172px;
    background: linear-gradient(
        180deg,
        #ededed 0%,
        rgba(246, 246, 247, 0) 194.22%
    );
    border-radius: 8px;
    position: relative;
    overflow: hidden;
}

.previewImage {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.trackingInfo {
    position: absolute;
    bottom: 10px;
    left: 10px;
    right: 10px;
}

.trackingNumber {
    font-size: 8px;
    font-weight: bold;
    color: #202223;
    margin-bottom: 2px;
}

.trackingStatus {
    font-size: 6px;
    color: #6d7175;
    margin-bottom: 4px;
}

.trackingDetails {
    height: 2px;
    background: #dbdddf;
    border-radius: 1px;
}

.mobilePreview {
    position: absolute;
    left: 176px;
    top: 54px;
    width: 76px;
    height: 138px;
}

.mobileFrame {
    width: 100%;
    height: 100%;
    background: #f6f6f7;
    border-radius: 4.46px;
    box-shadow: 0px 3.33px 13.3px rgba(0, 0, 0, 0.05);
    padding: 4.47px;
}

.mobileContent {
    width: 100%;
    height: 100%;
    background-image: url('data:image/svg+xml;base64,PHN2ZwogICAgICB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciCiAgICAgIHZpZXdCb3g9IjAgMCAxIDEiCiAgICAgIHByZXNlcnZlQXNwZWN0UmF0aW89Im5vbmUiCiAgICAgIHdpZHRoPSIxMDAlIgogICAgICBoZWlnaHQ9IjEwMCUiCiAgICA+CiAgICAgIDxyZWN0IHdpZHRoPSIxIiBoZWlnaHQ9IjEiIGZpbGw9IiNFRUUiIC8+CiAgICA8L3N2Zz4=');
    background-size: cover;
    background-position: 100% 1.84%;
    background-repeat: no-repeat;
    border-radius: 2.23px;
    position: relative;
}

.mobileTrackingInfo {
    position: absolute;
    top: 10px;
    left: 8px;
    right: 8px;
}

.mobileTrackingNumber {
    font-size: 6px;
    font-weight: bold;
    color: #202223;
    margin-bottom: 1px;
}

.mobileTrackingStatus {
    font-size: 4px;
    color: #6d7175;
}

.description {
    font-family: 'Inter', sans-serif;
    font-weight: 400;
    font-size: 13px;
    line-height: 20px;
    color: #6d7175;
    margin: 0;
    min-width: 100%;
}

.learnMoreLink {
    background: none;
    border: none;
    color: #2c6ecb;
    cursor: pointer;
    font-family: inherit;
    font-size: inherit;
    font-weight: inherit;
    line-height: inherit;
    padding: 0;
    text-decoration: none;

    &:hover {
        text-decoration: underline;
    }

    &:focus {
        outline: 2px solid #2c6ecb;
        outline-offset: 2px;
        border-radius: 2px;
    }
}

.enableNowLink {
    :global(.Polaris-Link) {
        color: var(--p-interactive);
    }
}

// 按钮样式覆盖
:global(.Polaris-Button) {
    &.Polaris-Button--fullWidth {
        border-radius: 5px;

        .Polaris-Button__Content {
            font-family: 'SF Pro Text', sans-serif;
            font-weight: 500;
            font-size: 14px;
            line-height: 20px;
        }
    }
}

// 状态标签样式
.statusBadge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 4px;
    font-family: 'Inter', sans-serif;
    font-weight: 500;
    font-size: 12px;
    line-height: 16px;
    margin-bottom: 16px;

    &--error {
        background-color: #fef2f2;
        color: #dc2626;
        border: 1px solid #fecaca;
    }

    &--warning {
        background-color: #fffbeb;
        color: #d97706;
        border: 1px solid #fed7aa;
    }
}

// Banner 操作按钮样式
.bannerAction {
    background: none;
    border: none;
    color: #2c6ecb;
    cursor: pointer;
    font-family: inherit;
    font-size: inherit;
    font-weight: inherit;
    line-height: inherit;
    padding: 0;
    text-decoration: underline;

    &:hover {
        text-decoration: none;
        color: #1e40af;
    }

    &:focus {
        outline: 2px solid #2c6ecb;
        outline-offset: 2px;
        border-radius: 2px;
    }
}

// 表单区域样式
.formSection {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.characterCount {
    font-family: 'Inter', sans-serif;
    font-weight: 400;
    font-size: 12px;
    line-height: 16px;
    color: #8c9196;
    text-align: right;
    margin-top: 4px;
}

// 调试控件样式（实际项目中应该移除）
.debugControls {
    margin-top: 24px;
    padding: 16px;
    background-color: #f0f8ff;
    border-radius: 8px;
    border: 1px solid #add8e6;

    label {
        display: block;
        font-family: 'Inter', sans-serif;
        font-weight: 500;
        font-size: 12px;
        color: #202223;
        margin-bottom: 8px;
    }

    select {
        width: 100%;
        padding: 8px 12px;
        border: 1px solid #c9cccf;
        border-radius: 4px;
        font-family: 'Inter', sans-serif;
        font-size: 13px;
        background-color: white;

        &:focus {
            outline: 2px solid #2c6ecb;
            outline-offset: 2px;
            border-color: #2c6ecb;
        }
    }
}

// Polaris组件样式覆盖
:global(.Polaris-TextField__Input) {
    font-family: 'Inter', sans-serif !important;
    font-size: 13px !important;
}

:global(.Polaris-Select__Input) {
    font-family: 'Inter', sans-serif !important;
    font-size: 13px !important;
}

:global(.Polaris-Select__SelectedOption) {
    font-family: 'Inter', sans-serif !important;
    font-size: 13px !important;
}

// 移除 Banner margin bottom
.bannerNoMargin :global(.Polaris-Banner) {
    margin-bottom: 0 !important;
}
