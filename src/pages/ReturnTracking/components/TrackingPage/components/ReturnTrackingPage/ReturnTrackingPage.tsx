import { useAuth } from '@aftership/automizely-product-auth';
import { <PERSON>, <PERSON><PERSON>, Card, Link } from '@shopify/polaris';
import useIsAfterShipPayingUser from 'hooks/useIsAfterShipPayingUser';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import useQuerySubscriptions from 'resources/billing/useQuerySubscriptions';
import { useSettingReturnsPage } from 'resources/settings/returnsPage';
import useQueryTrackingPages from 'resources/tracking/useQueryTrackingPages';

import { useAutoInstallTrackingWidget } from '../../../../hooks/useAutoInstallTrackingWidget';
import styles from './ReturnTrackingPage.module.scss';

const ReturnTrackingPage = () => {
    const { t } = useTranslation();
    const bannerBackground = require('./images/banner_corner_background.png');
    const [{ organization }] = useAuth();

    // 获取return page配置数据
    const { data: returnsPageData } = useSettingReturnsPage();

    // 获取tracking pages数据
    const {
        data: trackingPagesData,
        isLoading: isLoadingTrackingPages,
    } = useQueryTrackingPages({
        orgId: organization?.id,
    });

    // 获取订阅数据
    const { data: subscriptionsData } = useQuerySubscriptions({
        productCode: 'returns',
        orgId: organization?.id,
    });

    // 使用现有的tracking订阅检查hook
    const hasTrackingSubscription = useIsAfterShipPayingUser();

    // 处理数据转换
    const trackingPages = useMemo(() => {
        return trackingPagesData?.tracking_pages || [];
    }, [trackingPagesData]);

    // 检查是否有 returns 订阅功能
    const hasReturnsSubscription = useMemo(() => {
        const activeSubscriptions = subscriptionsData?.subscriptions?.filter(
            ({ active }) => active
        );
        return Boolean(activeSubscriptions && activeSubscriptions.length > 0);
    }, [subscriptionsData]);

    const configuredTrackingPageIds = useMemo(() => {
        const widgetSchema =
            returnsPageData?.return_tracking_page_widget_schema;
        if (!widgetSchema) return [];

        try {
            const parsedSchema = JSON.parse(widgetSchema);
            const trackingPageIds: string[] = [];

            // 递归遍历schema查找tracking page id
            const findTrackingPageIds = (obj: any) => {
                if (obj && typeof obj === 'object') {
                    if (
                        obj.tracking_page_id &&
                        typeof obj.tracking_page_id === 'string'
                    ) {
                        trackingPageIds.push(obj.tracking_page_id);
                    }
                    Object.values(obj).forEach(findTrackingPageIds);
                }
            };

            findTrackingPageIds(parsedSchema);
            return [...new Set(trackingPageIds)]; // 去重
        } catch (error) {
            console.error('Failed to parse widget schema:', error);
            return [];
        }
    }, [returnsPageData?.return_tracking_page_widget_schema]);

    // 检测所有配置的 tracking widgets 的状态 - 使用与 useTrackingWidgetStatus 相同的逻辑
    const hasInvalidTrackingPages = useMemo(() => {
        // 如果没有配置任何 tracking widgets，不显示警告
        if (configuredTrackingPageIds.length === 0) {
            return false;
        }

        // 如果数据还在加载中，不显示警告
        if (isLoadingTrackingPages) {
            return false;
        }

        // 检查每个配置的 tracking page ID 的状态
        return configuredTrackingPageIds.some(selectedTrackingPage => {
            // 直接实现状态检测逻辑（复制自 useTrackingWidgetStatus）

            // 1. 未订阅tracking服务
            if (!hasTrackingSubscription) {
                return true; // NO_SUBSCRIPTION 也算问题状态
            }

            // 2. 页面数据检查
            if (trackingPages.length === 0) {
                // 如果有选中的页面但trackingPages为空，说明页面被删除了
                if (selectedTrackingPage) {
                    return true; // PAGE_DELETED_SINGLE
                }
                return false; // INITIAL 状态不算问题
            }

            // 3. 订阅到期但有选中页面的情况
            if (!hasReturnsSubscription && selectedTrackingPage) {
                return true; // SUBSCRIPTION_EXPIRED
            }

            // 4. Returns订阅检查
            if (!hasReturnsSubscription) {
                return true; // UPGRADE_REQUIRED
            }

            // 5. 当前选中页面存在性检查
            const currentPageExists = trackingPages.some(
                p => p.id === selectedTrackingPage
            );
            if (selectedTrackingPage && !currentPageExists) {
                return true; // PAGE_DELETED_SINGLE 或 PAGE_DELETED_MULTIPLE
            }

            // 6. 当前选中页面功能检查
            if (selectedTrackingPage) {
                const currentPage = trackingPages.find(
                    p => p.id === selectedTrackingPage
                );
                if (
                    currentPage &&
                    !currentPage.service_types?.includes('returns')
                ) {
                    return true; // PAGE_INVALID
                }
            }

            // 7. Returns服务功能检查
            const pagesWithReturns = trackingPages.filter(p =>
                p.service_types?.includes('returns')
            );
            if (pagesWithReturns.length === 0) {
                return true; // SERVICE_DISABLED
            }

            // 8. 正常状态
            return false;
        });
    }, [
        configuredTrackingPageIds,
        trackingPages,
        hasReturnsSubscription,
        hasTrackingSubscription,
        isLoadingTrackingPages,
    ]);

    const { autoInstallAndNavigate } = useAutoInstallTrackingWidget();

    const handleEditReturnsPage = () => {
        autoInstallAndNavigate();
    };

    return (
        <div className={styles.returnTrackingPage}>
            {hasInvalidTrackingPages && (
                <Banner status="critical">
                    {t(
                        'tracking_page_invalid_warning',
                        'The tracking widget on your returns page is invalid. Edit the page to allow customers to track their return shipments.'
                    )}
                </Banner>
            )}
            <Card>
                <div className={styles.container}>
                    <div className={styles.content}>
                        <h3 className={styles.title}>
                            {t(
                                'return_tracking_page.title',
                                'Return tracking page'
                            )}
                        </h3>
                        <div className={styles.description}>
                            {t(
                                'return_tracking_page.description',
                                'Customized tracking experience for your customer just like how they track their orders. '
                            )}
                            &nbsp;
                            <Link url="#" external>
                                {t(
                                    'return_tracking_page.learn_more',
                                    'Learn more'
                                )}
                            </Link>
                        </div>
                        <div className={styles.buttonWrapper}>
                            <Button onClick={handleEditReturnsPage}>
                                {t(
                                    'return_tracking_page.button.edit',
                                    'Edit returns page'
                                )}
                            </Button>
                        </div>
                    </div>
                    <div className={styles.preview}>
                        <div className={styles.previewContainer}>
                            <div className={styles.previewContent}>
                                <img
                                    src={bannerBackground}
                                    alt="Return tracking page preview"
                                    className={styles.previewImage}
                                />
                            </div>
                        </div>
                    </div>
                </div>
            </Card>
        </div>
    );
};

export default ReturnTrackingPage;
